package com.buque.webview

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.webkit.ValueCallback
import android.webkit.WebView
import com.buque.webview.handler.BaseBridgeHandler
import com.smallbuer.jsbridge.core.BridgeHandler
import com.smallbuer.jsbridge.core.BridgeTiny
import com.smallbuer.jsbridge.core.IWebView
import com.smallbuer.jsbridge.core.OnBridgeCallback

private const val TAG = "AppBridgeWebView"

class AppBridgeWebView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : WebView(context, attrs), IWebView {

    var bridgeTiny: BridgeTiny? = null


    private val mLocalMessageHandlers: MutableMap<String, BridgeHandler> by lazy {
        mutableMapOf()
    }


    override fun destroy() {
        super.destroy()
        bridgeTiny?.freeMemory()
    }

    override fun addHandlerLocal(handlerName: String, bridgeHandler: BridgeHandler) {
        (bridgeHandler as? BaseBridgeHandler)?.tag = handlerName
        mLocalMessageHandlers[handlerName] = bridgeHandler
    }

    override fun getLocalMessageHandlers(): Map<String, BridgeHandler> {
        return mLocalMessageHandlers
    }

    override fun evaluateJavascript(p0: String, p1: Any?) {
        Log.i(TAG, "evaluateJavascript: ${p0},${p1}")
        super.evaluateJavascript(p0, p1 as? (ValueCallback<String>))
    }

    override fun callHandler(handlerName: String?, data: Any?, responseCallback: OnBridgeCallback?) {
        Log.i(TAG, "callHandler: ${handlerName},${data}")
        bridgeTiny?.callHandler(handlerName, data, responseCallback)
    }

    override fun setOverScrollMode(mode: Int) {
        super.setOverScrollMode(mode)
    }

    var distinctLoadUrl = false
    private var lastUrl = ""

    fun clearLastUrl(){
        lastUrl=""
    }

    override fun loadUrl(url: String, additionalHttpHeaders: Map<String?, String?>) {
        Log.d("LogUtils","bridge=======>   loadUrl:$url\nlastUrl:$lastUrl")
        if (distinctLoadUrl && lastUrl == url) {
            return
        }
        super.loadUrl(url, additionalHttpHeaders)
        lastUrl = url
    }
}

fun WebView?.asIWebVIew() = this as? IWebView