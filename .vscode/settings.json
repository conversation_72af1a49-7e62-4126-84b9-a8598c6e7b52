{"java.compile.nullAnalysis.mode": "automatic", "java.configuration.updateBuildConfiguration": "automatic", "java.import.gradle.enabled": true, "java.import.gradle.wrapper.enabled": true, "kotlin.languageServer.enabled": true, "kotlin.compiler.jvm.target": "1.8", "files.exclude": {"**/.gradle": true, "**/build": true, "**/.idea": false}, "search.exclude": {"**/build": true, "**/.gradle": true}, "files.watcherExclude": {"**/.gradle/**": true, "**/build/**": true}, "android.home": "/Users/<USER>/Library/Android/sdk", "android.sdk.path": "/Users/<USER>/Library/Android/sdk", "terminal.integrated.env.osx": {"ANDROID_HOME": "/Users/<USER>/Library/Android/sdk", "PATH": "${env:PATH}:/Users/<USER>/Library/Android/sdk/platform-tools:/Users/<USER>/Library/Android/sdk/tools"}, "gradle.nestedProjects": true, "gradle.javaDebug": true}