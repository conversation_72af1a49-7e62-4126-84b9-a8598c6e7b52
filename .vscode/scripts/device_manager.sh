#!/bin/bash

# Android Device Manager Script
# Provides device management functionality for VSCode

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Function to show header
show_header() {
    echo -e "${BLUE}╔══════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║        📱 Android Device Manager      ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════╝${NC}\n"
}

# Function to check Android SDK
check_android_sdk() {
    if [ -z "$ANDROID_HOME" ]; then
        echo -e "${RED}❌ ANDROID_HOME not set. Please set it to your Android SDK path.${NC}"
        exit 1
    fi
    
    if ! command -v adb &> /dev/null; then
        echo -e "${RED}❌ ADB not found in PATH.${NC}"
        exit 1
    fi
}

# Function to list connected devices
list_connected_devices() {
    echo -e "${CYAN}🔌 Connected Devices:${NC}"
    echo -e "${CYAN}═══════════════════${NC}"
    
    devices=$(adb devices -l)
    if echo "$devices" | grep -q "device"; then
        echo "$devices" | grep "device" | while read line; do
            device_id=$(echo $line | awk '{print $1}')
            device_info=$(echo $line | cut -d' ' -f2-)
            echo -e "${GREEN}✅ $device_id${NC} - $device_info"
        done
    else
        echo -e "${YELLOW}⚠️  No devices connected${NC}"
    fi
    echo
}

# Function to list available AVDs
list_avds() {
    echo -e "${PURPLE}🖥️  Available AVDs:${NC}"
    echo -e "${PURPLE}═══════════════════${NC}"
    
    if [ -f "/Users/<USER>/Library/Android/sdk/emulator/emulator" ]; then
        avds=$(/Users/<USER>/Library/Android/sdk/emulator/emulator -list-avds 2>/dev/null)
        if [ -n "$avds" ]; then
            echo "$avds" | while read avd; do
                if [ -n "$avd" ]; then
                    echo -e "${GREEN}📱 $avd${NC}"
                fi
            done
        else
            echo -e "${YELLOW}⚠️  No AVDs found${NC}"
        fi
    else
        echo -e "${RED}❌ Emulator not found at /Users/<USER>/Library/Android/sdk/emulator/emulator${NC}"
    fi
    echo
}

# Function to start an AVD
start_avd() {
    echo -e "${BLUE}🚀 Starting AVD...${NC}"
    
    # Get list of AVDs
    avds=$(/Users/<USER>/Library/Android/sdk/emulator/emulator -list-avds 2>/dev/null)
    
    if [ -z "$avds" ]; then
        echo -e "${RED}❌ No AVDs available. Please create one using Android Studio.${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}Available AVDs:${NC}"
    i=1
    echo "$avds" | while read avd; do
        if [ -n "$avd" ]; then
            echo "$i) $avd"
            i=$((i+1))
        fi
    done
    
    echo
    read -p "Enter AVD name to start (or press Enter for first one): " avd_choice
    
    if [ -z "$avd_choice" ]; then
        avd_choice=$(echo "$avds" | head -n1)
    fi
    
    echo -e "${BLUE}Starting AVD: $avd_choice${NC}"
    /Users/<USER>/Library/Android/sdk/emulator/emulator -avd "$avd_choice" &
    
    echo -e "${GREEN}✅ AVD '$avd_choice' is starting in background...${NC}"
    echo -e "${BLUE}💡 It may take a few moments to fully boot up.${NC}"
}

# Function to show device info
show_device_info() {
    echo -e "${CYAN}📊 Device Information:${NC}"
    echo -e "${CYAN}═══════════════════${NC}"
    
    devices=$(adb devices | grep "device" | awk '{print $1}')
    
    if [ -z "$devices" ]; then
        echo -e "${YELLOW}⚠️  No devices connected${NC}"
        return
    fi
    
    for device in $devices; do
        echo -e "${GREEN}Device: $device${NC}"
        
        # Get device properties
        model=$(adb -s $device shell getprop ro.product.model 2>/dev/null || echo "Unknown")
        android_version=$(adb -s $device shell getprop ro.build.version.release 2>/dev/null || echo "Unknown")
        api_level=$(adb -s $device shell getprop ro.build.version.sdk 2>/dev/null || echo "Unknown")
        
        echo "  Model: $model"
        echo "  Android: $android_version (API $api_level)"
        echo
    done
}

# Function to show menu
show_menu() {
    echo -e "${YELLOW}Choose an option:${NC}"
    echo "1) List connected devices"
    echo "2) List available AVDs"
    echo "3) Start an AVD"
    echo "4) Show device information"
    echo "5) Refresh ADB server"
    echo "6) Exit"
    echo
}

# Function to refresh ADB
refresh_adb() {
    echo -e "${BLUE}🔄 Refreshing ADB server...${NC}"
    adb kill-server
    adb start-server
    echo -e "${GREEN}✅ ADB server refreshed${NC}\n"
}

# Main function
main() {
    show_header
    check_android_sdk
    
    while true; do
        show_menu
        read -p "Enter your choice (1-6): " choice
        echo
        
        case $choice in
            1)
                list_connected_devices
                ;;
            2)
                list_avds
                ;;
            3)
                start_avd
                ;;
            4)
                show_device_info
                ;;
            5)
                refresh_adb
                ;;
            6)
                echo -e "${GREEN}👋 Goodbye!${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ Invalid choice. Please try again.${NC}\n"
                ;;
        esac
        
        echo -e "${BLUE}Press Enter to continue...${NC}"
        read
        clear
        show_header
    done
}

# Run if called directly
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main
fi