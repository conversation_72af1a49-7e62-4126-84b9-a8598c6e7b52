#!/bin/bash

# Android Build and Launch Script for VSCode
# This script handles the complete workflow: build -> install -> launch

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Android Build and Launch Process...${NC}\n"

# Function to check if adb is available
check_adb() {
    if ! command -v adb &> /dev/null; then
        echo -e "${RED}❌ ADB not found. Please make sure Android SDK is installed and added to PATH.${NC}"
        exit 1
    fi
}

# Function to list connected devices
list_devices() {
    echo -e "${YELLOW}📱 Checking connected devices...${NC}"
    devices=$(adb devices -l | grep -v "List of devices" | grep -v "^$")
    
    if [ -z "$devices" ]; then
        echo -e "${RED}❌ No devices connected. Please connect a device or start an emulator.${NC}"
        echo -e "${BLUE}💡 You can start an emulator using the 'Start AVD Emulator' task.${NC}"
        exit 1
    else
        echo -e "${GREEN}✅ Connected devices:${NC}"
        echo "$devices"
        echo
    fi
}

# Function to build the app
build_app() {
    echo -e "${YELLOW}🔨 Building debug APK...${NC}"
    if ./gradlew app:assembleDevelopmentOfficialDebug --info; then
        echo -e "${GREEN}✅ Build successful!${NC}\n"
    else
        echo -e "${RED}❌ Build failed!${NC}"
        exit 1
    fi
}

# Function to install the app
install_app() {
    echo -e "${YELLOW}📲 Installing app to device...${NC}"
    if ./gradlew app:installDevelopmentOfficialDebug; then
        echo -e "${GREEN}✅ Installation successful!${NC}\n"
    else
        echo -e "${RED}❌ Installation failed!${NC}"
        exit 1
    fi
}

# Function to launch the app
launch_app() {
    echo -e "${YELLOW}▶️ Launching app...${NC}"
    if adb shell am start -n com.buque.wakoo/.MainActivity; then
        echo -e "${GREEN}✅ App launched successfully!${NC}\n"
    else
        echo -e "${RED}❌ Failed to launch app!${NC}"
        exit 1
    fi
}

# Function to show logcat
show_logcat() {
    echo -e "${BLUE}📋 Starting logcat (press Ctrl+C to stop)...${NC}"
    echo -e "${BLUE}Filtering for package: com.buque.wakoo${NC}\n"
    adb logcat | grep "com.buque.wakoo\|AndroidRuntime\|System.err"
}

# Main execution
main() {
    check_adb
    list_devices
    build_app
    install_app
    launch_app
    
    echo -e "${GREEN}🎉 All steps completed successfully!${NC}"
    echo -e "${BLUE}📱 Your app should now be running on the connected device.${NC}\n"
    
    # Ask if user wants to see logcat
    read -p "Do you want to see the app logs? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        show_logcat
    fi
}

# Run main function
main