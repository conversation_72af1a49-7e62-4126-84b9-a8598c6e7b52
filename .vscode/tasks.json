{
    "version": "2.0.0",
    "tasks": [
        // === 设备管理任务 ===
        {
            "label": "📱 List Android Devices",
            "type": "shell",
            "command": "echo",
            "args": ["\n📱 Available Android Devices:\n"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "dedicated",
                "clear": true
            },
            "dependsOrder": "sequence",
            "dependsOn": ["Show Connected Devices", "Show Available AVDs"]
        },
        {
            "label": "Show Connected Devices",
            "type": "shell",
            "command": "adb",
            "args": ["devices", "-l"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": false
            }
        },
        {
            "label": "Show Available AVDs",
            "type": "shell",
            "command": "/Users/<USER>/Library/Android/sdk/emulator/emulator",
            "args": ["-list-avds"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": false
            }
        },
        {
            "label": "🚀 Start AVD Emulator",
            "type": "shell",
            "command": "/Users/<USER>/Library/Android/sdk/emulator/emulator",
            "args": ["-avd", "${input:avdName}"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "dedicated",
                "clear": true
            },
            "isBackground": true
        },
        
        // === 基础构建任务 ===
        {
            "label": "🔨 Build Debug (Only)",
            "type": "shell",
            "command": "./gradlew",
            "args": [
                "app:assembleDevelopmentOfficialDebug", "--info"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "dedicated",
                "showReuseMessage": false,
                "clear": true
            },
            "problemMatcher": [
                "$gradle"
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "📦 Build Release",
            "type": "shell",
            "command": "./gradlew",
            "args": [
                "app:assembleProductionOfficialRelease"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "dedicated",
                "showReuseMessage": false,
                "clear": true
            },
            "problemMatcher": [
                "$gradle"
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "🧹 Clean Project",
            "type": "shell",
            "command": "./gradlew",
            "args": [
                "clean"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "dedicated",
                "showReuseMessage": false,
                "clear": true
            },
            "problemMatcher": [
                "$gradle"
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        
        // === 主要开发任务 ===
        {
            "label": "🔨📲 Build & Install Debug",
            "type": "shell",
            "command": "./gradlew",
            "args": [
                "app:installDevelopmentOfficialDebug"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "dedicated",
                "showReuseMessage": false,
                "clear": true
            },
            "problemMatcher": [
                "$gradle"
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "🚀 Launch App (Only)",
            "type": "shell",
            "command": "adb",
            "args": [
                "shell",
                "monkey",
                "-p",
                "com.site.wakoo.debug",
                "-c",
                "android.intent.category.LAUNCHER",
                "1"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": false
            }
        },
        {
            "label": "🔨📲🚀 Build, Install & Launch Debug",
            "dependsOrder": "sequence",
            "dependsOn": ["🔨📲 Build & Install Debug", "🚀 Launch App (Only)"],
            "group": "build",
            "presentation": {
                "echo": false,
                "reveal": "always",
                "focus": true,
                "panel": "dedicated",
                "showReuseMessage": false,
                "clear": true
            }
        }
    ],
    "inputs": [
        {
            "id": "avdName",
            "description": "Enter AVD name to start",
            "default": "Pixel_3a_API_30",
            "type": "promptString"
        }
    ]
}