# Gemini Collaboration Guide: Wakoo Project

This document provides context and instructions for collaborating on the Wakoo Android project with <PERSON>.

## Project Overview

Wakoo is an Android application built with **Kotlin** and **Gradle**. The project follows a modular architecture, with the main components being:

-   `app`: The main application module, containing the core features, UI (built with Jetpack Compose), and business logic.
-   `lib-webview`: A library module for handling WebView-related functionality.
-   `l10n`: A dedicated directory containing scripts and data for localization (L10n).
-   `plugins`: A directory for custom Gradle plugins.

The application uses a variety of modern Android libraries, including:
-   **Jetpack Compose** for the UI.
-   **Retrofit & OkHttp** for networking.
-   **Coil** for image loading.
-   **Room** for the local database.
-   **Tencent IM SDK** for real-time communication.
-   **Google Billing** for in-app purchases.

## Building and Running

The project is managed by Gradle. Here are the common commands:

### Build the project
To build the entire project and run checks:
```bash
./gradlew build
```

### Assemble a Debug APK
To create a debug version of the application:
```bash
./gradlew assembleDebug
```

### Install on a Connected Device/Emulator
To install the debug version on a running device or emulator:
```bash
./gradlew installDebug
```

### Run Tests
To execute all unit tests in the project:
```bash
./gradlew test
```

### Restart and Launch (Development)
The `shell/restart.sh` script provides a convenient way to force-stop and restart the debug application on an emulator or device. It also contains commented-out commands for launching specific screens (routes) directly.
```bash
# Restart the app on the main screen
./shell/restart.sh

# Example: Launch directly to the 'setting' screen (after uncommenting in the script)
# adb shell am start -n com.site.wakoo.debug/com.buque.wakoo.MainActivity -e "route" "setting"
```

## Development Conventions

### Coding Style
The project uses **Ktlint** to enforce a consistent Kotlin coding style. The configuration is in `app/build.gradle.kts`. It's recommended to run `./gradlew ktlintCheck` before committing changes.

### Localization (L10n)
The `l10n` directory contains a comprehensive toolset for managing translations. The process is automated with a shell script.

**To update all translations:**
1.  Run the automated script:
    ```bash
    ./l10n/run_l10n.sh
    ```
2.  This script performs several actions:
    -   Scans the source code for localizable strings.
    -   Generates a base `localizable.json` file.
    -   Fetches the latest translations from a remote service.
    -   Creates a `translations_comparison.csv` file to show the status of translations across different languages (English, Japanese).
    -   Reports any suspicious code or translation conflicts in `.txt` and `.log` files.

For detailed information, refer to the `l10n/L10Readme.md` file.

### Dependency Management
Project dependencies are centrally managed in the `gradle/libs.versions.toml` file (Version Catalog), which is the standard and recommended way to handle dependencies in modern Gradle projects.

# 最重要的一点：所有回答和交互必须使用中文。