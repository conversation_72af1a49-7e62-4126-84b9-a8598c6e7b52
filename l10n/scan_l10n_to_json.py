import os
import re
import json
import textwrap
from collections import defaultdict

# --- 配置 ---
PROJECT_ROOT = '.'
SCAN_DIRECTORIES = ['app/src/main/kotlin', 'app/src/main/java']
OUTPUT_FILE = 'l10n/localizable.json'
SUSPICIOUS_OUTPUT_FILE = 'l10n/suspicious_l10n.txt'


# --- 正则表达式 (分为"加法"模块) ---

# 模块1: if/else 表达式
PATTERN_IF_ELSE = re.compile(
    r'\bif\b\s*\(.*?\)\s*\{\s*"((?:\\.|[^"\\])*)"\s*\}\s*else\s*\{\s*"((?:\\.|[^"\\])*)"\s*\}\s*\.localized(?:Format)?\b',
    re.DOTALL
)
# 模块2: 简单直接调用
PATTERN_SIMPLE = re.compile(r'"((?:\\.|[^"\\])*)"\s*\.localized(?:Format)?\b')

# 模块3: 带Key的调用 - 改进版本
def extract_balanced_parentheses(text, start_pos):
    """从指定位置开始提取平衡的括号内容"""
    if start_pos >= len(text) or text[start_pos] != '(':
        return None, start_pos
    
    depth = 0
    i = start_pos
    while i < len(text):
        if text[i] == '(':
            depth += 1
        elif text[i] == ')':
            depth -= 1
            if depth == 0:
                return text[start_pos+1:i], i+1
        elif text[i] == '"':
            # 跳过字符串字面量
            i += 1
            while i < len(text) and text[i] != '"':
                if text[i] == '\\':
                    i += 2  # 跳过转义字符
                else:
                    i += 1
        i += 1
    return None, start_pos

PATTERN_WITH_KEY_CALL = re.compile(
    r'("""[\s\S]*?"""|"(?:\\.|[^"\\])*")'  # 匹配三引号字符串或普通字符串
    r'\s*(?:\.trimIndent\(\))?\s*'  # 可选的trimIndent调用
    r'\.localized(?:Format)?WithKey\s*(?=\()',  # localizedWithKey或localizedFormatWithKey后跟开括号
    re.DOTALL
)

PATTERN_KEY_FROM_ARGS = re.compile(r'(?:key\s*=\s*)?"(.*?)"')

# 增强的匹配模式 - 加法模块
# 变量调用模式: variable.localized
PATTERN_VARIABLE_CALL = re.compile(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\.localized(?:Format)?\b')

# 函数返回值调用模式: function().localized
PATTERN_FUNCTION_CALL = re.compile(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*\.localized(?:Format)?\b')

# 属性访问调用模式: object.property.localized
PATTERN_PROPERTY_ACCESS = re.compile(r'\b([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)+)\s*\.localized(?:Format)?\b')

# 字符串模板调用模式: "$variable".localized
PATTERN_STRING_TEMPLATE = re.compile(r'"([^"]*\$[^"]*)"\.localized(?:Format)?\b')

# 多行字符串连接模式: ("string1" + "string2").localized
PATTERN_STRING_CONCAT = re.compile(r'\(\s*"[^"]*"(?:\s*\+\s*"[^"]*")+\s*\)\s*\.localized(?:Format)?\b')

# 三元运算符模式: condition ? "string1" : "string2".localized
PATTERN_TERNARY = re.compile(r'[^?]*\?\s*"([^"]*)"\.localized(?:Format)?\s*:\s*"([^"]*)"\.localized(?:Format)?\b')

# 数组/列表访问模式: array[index].localized
PATTERN_ARRAY_ACCESS = re.compile(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\[[^\]]+\]\s*\.localized(?:Format)?\b')

# 链式调用模式: getString().trim().localized
PATTERN_CHAIN_CALL = re.compile(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\([^)]*\)(?:\.[a-zA-Z_][a-zA-Z0-9_]*\([^)]*\))*\s*\.localized(?:Format)?\b')

# 审计模块
PATTERN_SUSPICIOUS = re.compile(r'\blocalized(?:Format)?(?:WithKey)?\b')
PATTERN_STRING_LITERAL = re.compile(r'"((?:\\.|[^"\\])*)"')


def safe_process_string(s):
    """手动、安全地替换已知的转义序列。"""
    s = s.replace('\\\\', '\\')
    s = s.replace('\\n', '\n')
    s = s.replace('\\t', '\t')
    s = s.replace('\\"', '"')
    s = s.replace("\\'", "'")
    return s


def scan_project_files(root_path, directories):
    """扫描整个项目，返回成功解析的翻译 和 未能解析的可疑代码行。"""
    translations = {}
    suspicious_items = {}
    print("\n🚀 开始扫描项目代码 (使用\"加法\"模式)...")

    for directory in directories:
        scan_path = os.path.join(root_path, directory)
        if not os.path.exists(scan_path): continue
        print(f"🔍 正在扫描目录: {scan_path}")

        for dirpath, _, filenames in os.walk(scan_path):
            for filename in filenames:
                if filename.endswith(('.kt', '.java')):
                    file_path = os.path.join(dirpath, filename)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                        content = "".join(lines)

                        handled_line_counts = defaultdict(int)
                        def get_line_num_from_pos(pos):
                            return content.count('\n', 0, pos) + 1

                        # **核心修正: 每个解析器独立运行，做加法**

                        # 1. 运行 if/else 解析器
                        for match in PATTERN_IF_ELSE.finditer(content):
                            string1 = safe_process_string(match.group(1))
                            string2 = safe_process_string(match.group(2))
                            if string1.strip():  # 只要不是空字符串就接受
                                translations[string1] = string1
                            if string2.strip():  # 只要不是空字符串就接受
                                translations[string2] = string2
                            handled_line_counts[get_line_num_from_pos(match.end())] += 1

                        # 2. 运行简单模式解析器
                        for match in PATTERN_SIMPLE.finditer(content):
                            processed_string = safe_process_string(match.group(1))
                            if processed_string.strip():  # 只要不是空字符串就接受
                                translations[processed_string] = processed_string
                            handled_line_counts[get_line_num_from_pos(match.start())] += 1

                        # 3. 运行带Key模式解析器 - 改进版本
                        for match in PATTERN_WITH_KEY_CALL.finditer(content):
                            match_end = match.end()
                            args_content, _ = extract_balanced_parentheses(content, match_end)
                            
                            if args_content:
                                key_match = PATTERN_KEY_FROM_ARGS.search(args_content)
                                if key_match:
                                    # 标记整个匹配范围内的所有行为已处理
                                    start_line = get_line_num_from_pos(match.start())
                                    end_line = get_line_num_from_pos(match_end + len(args_content) + 1)
                                    for line_num in range(start_line, end_line + 1):
                                        handled_line_counts[line_num] += 1
                                    
                                    key = safe_process_string(key_match.group(1))
                                    
                                    # 提取字符串内容
                                    string_content = match.group(1)
                                    if string_content.startswith('"""') and string_content.endswith('"""'):
                                        # 三引号字符串
                                        value = textwrap.dedent(string_content[3:-3]).strip()
                                    else:
                                        # 普通字符串
                                        value = safe_process_string(string_content[1:-1])
                                    
                                    # 过滤内容 - 对于带Key的调用，保持较宽松的过滤
                                    if value.strip() and len(value.strip()) > 1:
                                        translations[key] = value

                        # 4. 增强的匹配模式解析器 - 加法模块
                        
                        # 4.1 字符串模板调用解析器
                        for match in PATTERN_STRING_TEMPLATE.finditer(content):
                            template_string = match.group(1)
                            if template_string.strip():
                                # 对于模板字符串，我们记录原始模板作为key
                                translations[template_string] = template_string
                                handled_line_counts[get_line_num_from_pos(match.start())] += 1

                        # 4.2 变量调用解析器 - 标记为可疑但不报错
                        for match in PATTERN_VARIABLE_CALL.finditer(content):
                            variable_name = match.group(1)
                            line_num = get_line_num_from_pos(match.start())
                            # 变量调用通常需要人工审查，但标记为已处理避免重复报告
                            handled_line_counts[line_num] += 1

                        # 4.3 函数返回值调用解析器 - 标记为可疑但不报错
                        for match in PATTERN_FUNCTION_CALL.finditer(content):
                            line_num = get_line_num_from_pos(match.start())
                            handled_line_counts[line_num] += 1

                        # 4.4 属性访问调用解析器 - 标记为可疑但不报错
                        for match in PATTERN_PROPERTY_ACCESS.finditer(content):
                            line_num = get_line_num_from_pos(match.start())
                            handled_line_counts[line_num] += 1

                        # 4.5 数组访问调用解析器 - 标记为可疑但不报错
                        for match in PATTERN_ARRAY_ACCESS.finditer(content):
                            line_num = get_line_num_from_pos(match.start())
                            handled_line_counts[line_num] += 1

                        # 4.6 链式调用解析器 - 标记为可疑但不报错
                        for match in PATTERN_CHAIN_CALL.finditer(content):
                            line_num = get_line_num_from_pos(match.start())
                            handled_line_counts[line_num] += 1

                        # 执行增强的可疑审计
                        for i, line in enumerate(lines):
                            suspicious_matches = PATTERN_SUSPICIOUS.findall(line)
                            if not suspicious_matches: continue

                            line_num = i + 1
                            if len(suspicious_matches) <= handled_line_counts.get(line_num, 0):
                                continue

                            stripped_line = line.strip()
                            
                            # 增强的过滤逻辑
                            if stripped_line.startswith(('import ', 'fun ', 'val ', 'class ', 'interface ', 'enum ', 'object ')): 
                                continue
                            
                            # 跳过注释行
                            if stripped_line.startswith(('//', '/*', '*', '*/')):
                                continue
                            
                            # 跳过包含常见非本地化关键词的行
                            if any(keyword in stripped_line.lower() for keyword in ['test', 'debug', 'log', 'print', 'assert']):
                                continue
                            
                            # 跳过明显的方法定义或调用
                            if re.search(r'\b(fun|private|public|protected|internal)\s+\w+.*localized', stripped_line):
                                continue

                            # 检查是否包含字符串字面量
                            has_string_literal = PATTERN_STRING_LITERAL.search(line)
                            
                            if not has_string_literal:
                                # 无字符串字面量的情况，提供更多上下文
                                start_line = max(0, i - 3)
                                end_line = min(len(lines), i + 2)
                                context_block = "".join(lines[start_line:end_line])
                                suspicious_items[line_num] = f"--- Context Block (No String Literal) ---\n{file_path}:{start_line+1}-{end_line}:\n{context_block}"
                            else:
                                # 有字符串字面量但未被处理的情况
                                suspicious_items[line_num] = f"{file_path}:{line_num}: {stripped_line}"

                    except Exception as e:
                        print(f"❌ 读取文件时出错: {file_path}, 错误: {e}")

    final_suspicious_list = [suspicious_items[k] for k in sorted(suspicious_items.keys())]

    print(f"✅ 扫描完成。共发现 {len(translations)} 个明确的字符串。")
    if final_suspicious_list:
        print(f"⚠️  发现 {len(final_suspicious_list)} 个可疑代码点，请检查 '{SUSPICIOUS_OUTPUT_FILE}' 文件。")

    return translations, final_suspicious_list


def generate_json_file(translations, output_filepath):
    """生成最终的JSON文件"""
    if not translations:
        print("🤷 没有发现明确的翻译字符串，未生成JSON文件。")
        return
    print(f"\n✍️  正在生成JSON文件...")
    try:
        with open(output_filepath, 'w', encoding='utf-8') as f:
            json.dump(translations, f, sort_keys=True, ensure_ascii=False, indent=4)
        print(f"🎉 成功！结果已保存到: {output_filepath}")
    except Exception as e:
        print(f"❌ 写入JSON文件时出错: {output_filepath}, 错误: {e}")


def generate_suspicious_file(lines, output_filepath):
    """生成记录可疑代码的TXT文件"""
    if not lines:
        print("👍 未发现可疑代码行。")
        if os.path.exists(output_filepath):
            try:
                open(output_filepath, 'w').close()
            except IOError:
                pass
        return
    print(f"✍️  正在生成可疑代码日志...")
    try:
        with open(output_filepath, 'w', encoding='utf-8') as f:
            f.write("# --- Suspicious L10n Code --- #\n")
            f.write("# The following lines/blocks contain '.localized' calls but were not fully parsed by the script.\n")
            f.write("# Please review them manually.\n\n")
            for line in lines:
                f.write(line + '\n\n')
        print(f"🎉 成功！可疑代码已记录到: {output_filepath}")
    except Exception as e:
        print(f"❌ 写入可疑代码文件时出错: {output_filepath}, 错误: {e}")


if __name__ == '__main__':
    parsed_translations, unhandled_lines = scan_project_files(PROJECT_ROOT, SCAN_DIRECTORIES)
    generate_json_file(parsed_translations, OUTPUT_FILE)
    generate_suspicious_file(unhandled_lines, SUSPICIOUS_OUTPUT_FILE)