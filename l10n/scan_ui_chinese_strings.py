#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确扫描需要本地化的UI中文字符串
专注于识别真正需要本地化的用户界面文本
"""

import os
import re
import json
from collections import defaultdict

# --- 配置 ---
PROJECT_ROOT = '.'
SCAN_DIRECTORIES = ['app/src/main/java']
OUTPUT_FILE = 'l10n/ui_chinese_strings.json'
REPORT_FILE = 'l10n/ui_chinese_strings_report.txt'

# --- 正则表达式 ---

# 匹配包含中文字符的字符串字面量
PATTERN_CHINESE_STRING = re.compile(r'"([^"]*[\u4e00-\u9fff][^"]*)"')

# 本地化方法调用
LOCALIZATION_METHODS = [
    r'\.localized\b',
    r'\.localizedFormat\b', 
    r'\.localizedWithKey\b',
    r'\.localizedFormatWithKey\b'
]

PATTERN_LOCALIZED_CALL = re.compile('|'.join(LOCALIZATION_METHODS))

# 匹配已经本地化的字符串
PATTERN_STRING_WITH_L10N = re.compile(
    r'"([^"]*[\u4e00-\u9fff][^"]*)"' +
    r'\s*(?:\.trimIndent\(\))?\s*' +
    r'(?:' + '|'.join(LOCALIZATION_METHODS) + ')'
)

# 需要排除的文件路径
EXCLUDE_FILE_PATTERNS = [
    r'/demo/',
    r'/test/',
    r'/debug/',
    r'Test\.kt$',
    r'Demo\.kt$',
    r'Preview\.kt$',
    r'Sample\.kt$',
    r'Example\.kt$',
]

EXCLUDE_FILE_PATTERN = re.compile('|'.join(EXCLUDE_FILE_PATTERNS), re.IGNORECASE)

# 需要排除的行模式
EXCLUDE_LINE_PATTERNS = [
    r'^\s*//.*',                    # 单行注释
    r'^\s*/\*.*\*/',               # 单行多行注释
    r'^\s*\*.*',                   # 多行注释中的行
    r'@Preview',                   # Compose预览
    r'@Composable',                # Compose组件标记行
    r'fun\s+\w*[Pp]review',        # 预览函数
    r'Log\.[deiw]',                # 日志输出 (Log.d, Log.e, Log.i, Log.w)
    r'LogUtils\.[deiw]',           # 自定义日志工具类
    r'println',                    # 打印语句
    r'print\(',                    # 打印函数
    r'BuildConfig\.DEBUG',         # 调试构建
    r'TODO',                       # TODO注释
    r'FIXME',                      # FIXME注释
    r'System\.out\.print',         # System.out.print
    r'\.log\(',                    # 通用日志方法
    r'Logger\.',                   # Logger类
]

EXCLUDE_LINE_PATTERN = re.compile('|'.join(EXCLUDE_LINE_PATTERNS), re.IGNORECASE)

# UI相关的上下文关键词
UI_CONTEXT_KEYWORDS = [
    'text =', 'title =', 'label =', 'hint =', 'message =', 'content =', 
    'description =', 'placeholder =', 'error =', 'success =', 'warning =',
    'buttonText =', 'actionText =', 'confirmText =', 'cancelText =',
    'Text(', 'Button(', 'TextField(', 'OutlinedTextField(',
    'AlertDialog(', 'Toast.', 'Snackbar.',
]

# 常见的UI文本词汇
UI_TEXT_KEYWORDS = [
    '确认', '取消', '保存', '删除', '编辑', '设置', '登录', '注册', '退出',
    '提交', '重置', '刷新', '加载', '搜索', '筛选', '排序', '分享',
    '收藏', '点赞', '评论', '转发', '关注', '私信', '举报', '拉黑',
    '充值', '提现', '支付', '购买', '下单', '退款', '发货', '收货',
    '上传', '下载', '播放', '暂停', '停止', '录音', '拍照', '选择',
    '完成', '跳过', '继续', '返回', '关闭', '打开', '展开', '收起',
    '成功', '失败', '错误', '警告', '提示', '通知', '消息', '公告',
    '请', '您', '我', '他', '她', '的', '了', '吗', '呢', '吧',
]


def is_excluded_file(file_path):
    """检查是否应该排除这个文件"""
    return EXCLUDE_FILE_PATTERN.search(file_path) is not None


def is_excluded_line(line):
    """检查是否应该排除这一行"""
    stripped = line.strip()
    
    if not stripped:
        return True
    
    if EXCLUDE_LINE_PATTERN.search(line):
        return True
    
    # 排除JSON格式的数据定义
    if re.match(r'^\s*"[^"]*":\s*"[^"]*"[,}]?\s*$', stripped):
        return True
    
    # 排除简单的变量赋值（但保留可能的UI文本赋值）
    if re.match(r'^\s*val\s+\w+\s*=\s*"[^"]*"[,;]?\s*$', stripped):
        # 检查是否包含UI相关关键词
        if not any(keyword in stripped.lower() for keyword in ['text', 'title', 'label', 'hint', 'message']):
            return True
    
    return False


def is_likely_ui_text(text, context):
    """判断是否可能是需要本地化的UI文本"""
    # 基本过滤
    if len(text.strip()) < 2:
        return False
    
    # 排除纯英文数字符号
    if not re.search(r'[\u4e00-\u9fff]', text):
        return False
    
    # 排除包含代码特征的文本
    code_patterns = [
        r'^\w+\.\w+',           # 类似 object.property
        r'[{}()\[\]]',          # 包含代码符号
        r'^\d+$',               # 纯数字
        r'^[a-zA-Z_]\w*$',      # 纯变量名
        r'http[s]?://',         # URL
        r'\.(com|org|net)',     # 域名
        r'yyyy|MM|dd|HH|mm|ss', # 日期格式
    ]
    
    for pattern in code_patterns:
        if re.search(pattern, text):
            return False
    
    # 强UI指示器 - 如果上下文包含这些，很可能是UI文本
    strong_ui_indicators = [
        'Text(', 'Button(', 'TextField(', 'AlertDialog(',
        'text =', 'title =', 'label =', 'hint =', 'message =',
        'Toast.', 'Snackbar.', 'Dialog(',
    ]
    
    if any(indicator in context for indicator in strong_ui_indicators):
        return True
    
    # 弱UI指示器 - 需要结合其他条件
    weak_ui_indicators = [
        'content =', 'description =', 'placeholder =',
        'buttonText =', 'actionText =', 'confirmText =',
    ]
    
    has_weak_indicator = any(indicator in context for indicator in weak_ui_indicators)
    
    # 检查是否包含常见UI词汇
    has_ui_keywords = any(keyword in text for keyword in UI_TEXT_KEYWORDS)
    
    # 长度和内容判断
    is_reasonable_length = 2 <= len(text) <= 50
    has_user_facing_content = any(char in text for char in ['请', '您', '我', '的', '了', '吗', '呢', '吧'])
    
    # 综合判断
    if has_weak_indicator and (has_ui_keywords or has_user_facing_content):
        return True
    
    if is_reasonable_length and has_ui_keywords and has_user_facing_content:
        return True
    
    # 特殊情况：短小精悍的常见UI文本
    common_ui_texts = [
        '确认', '取消', '保存', '删除', '编辑', '设置', '登录', '注册', '退出',
        '提交', '重置', '刷新', '加载', '搜索', '完成', '跳过', '继续', '返回',
        '成功', '失败', '错误', '提示', '关闭', '打开', '选择', '上传', '下载',
    ]
    
    if text.strip() in common_ui_texts:
        return True
    
    return False


def is_in_preview_function(lines, line_index):
    """检查指定行是否在预览函数内部"""
    # 向上查找函数开始
    for i in range(line_index - 1, max(0, line_index - 50), -1):  # 最多向上查找50行
        line = lines[i].strip()

        # 找到函数定义
        if re.match(r'^\s*(private\s+)?fun\s+\w*[Pp]review.*\(\s*\)\s*\{?\s*$', line):
            # 检查是否有@Preview注解
            for j in range(max(0, i - 10), i):  # 向上查找10行内的注解
                if '@Preview' in lines[j]:
                    return True
            break

        # 如果遇到其他函数定义，说明不在预览函数内
        if re.match(r'^\s*(private\s+|public\s+|internal\s+)?fun\s+\w+.*\(\s*.*\)\s*\{?\s*$', line):
            break

    return False


def is_in_log_statement(line):
    """检查字符串是否在日志语句中"""
    log_patterns = [
        r'Log\.[deiw]\s*\(',
        r'LogUtils\.[deiw]\s*\(',
        r'Logger\.',
        r'println\s*\(',
        r'print\s*\(',
        r'System\.out\.print',
        r'\.log\s*\(',
    ]

    for pattern in log_patterns:
        if re.search(pattern, line):
            return True

    return False


def extract_ui_chinese_strings_from_file(file_path):
    """从单个文件中提取可能需要本地化的UI中文字符串"""
    ui_strings = []

    if is_excluded_file(file_path):
        return ui_strings

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')

        # 找出已经本地化的字符串
        localized_strings = set()
        for match in PATTERN_STRING_WITH_L10N.finditer(content):
            localized_strings.add(match.group(1))

        # 扫描每一行
        for i, line in enumerate(lines, 1):
            if is_excluded_line(line):
                continue

            # 检查是否在预览函数内部
            if is_in_preview_function(lines, i - 1):  # i-1因为lines是0索引的
                continue

            # 检查是否在日志语句中
            if is_in_log_statement(line):
                continue

            # 查找包含中文的字符串
            for match in PATTERN_CHINESE_STRING.finditer(line):
                chinese_text = match.group(1)

                # 跳过已经本地化的字符串
                if chinese_text in localized_strings:
                    continue

                # 检查同一行是否有本地化调用
                line_after_string = line[match.end():]
                if PATTERN_LOCALIZED_CALL.search(line_after_string):
                    continue

                # 判断是否可能是UI文本
                if is_likely_ui_text(chinese_text, line):
                    confidence = 'high' if any(indicator in line for indicator in [
                        'Text(', 'Button(', 'TextField(', 'AlertDialog(',
                        'text =', 'title =', 'label =', 'hint =', 'message ='
                    ]) else 'medium'

                    ui_strings.append({
                        'file': file_path,
                        'line': i,
                        'text': chinese_text,
                        'context': line.strip(),
                        'confidence': confidence,
                        'type': 'ui_text'
                    })

    except Exception as e:
        print(f"❌ 读取文件时出错: {file_path}, 错误: {e}")

    return ui_strings


def scan_project_for_ui_strings(root_path, directories):
    """扫描项目中可能需要本地化的UI字符串"""
    all_ui_strings = []
    file_count = 0
    
    print("\n🎯 开始扫描项目中需要本地化的UI中文字符串...")
    
    for directory in directories:
        scan_path = os.path.join(root_path, directory)
        if not os.path.exists(scan_path):
            print(f"⚠️  目录不存在: {scan_path}")
            continue
        
        print(f"📁 正在扫描目录: {scan_path}")
        
        for dirpath, _, filenames in os.walk(scan_path):
            for filename in filenames:
                if filename.endswith(('.kt', '.java')):
                    file_path = os.path.join(dirpath, filename)
                    relative_path = os.path.relpath(file_path, root_path)
                    
                    ui_strings = extract_ui_chinese_strings_from_file(file_path)
                    if ui_strings:
                        all_ui_strings.extend(ui_strings)
                        high_confidence = len([s for s in ui_strings if s['confidence'] == 'high'])
                        print(f"  📄 {relative_path}: 发现 {len(ui_strings)} 个可能的UI字符串 (高置信度: {high_confidence})")
                    
                    file_count += 1
    
    print(f"\n✅ 扫描完成！")
    print(f"📊 总共扫描了 {file_count} 个文件")
    print(f"🎯 发现 {len(all_ui_strings)} 个可能需要本地化的UI字符串")
    
    # 按置信度分类统计
    high_confidence = len([s for s in all_ui_strings if s['confidence'] == 'high'])
    medium_confidence = len([s for s in all_ui_strings if s['confidence'] == 'medium'])
    
    print(f"   - 高置信度: {high_confidence} 个")
    print(f"   - 中等置信度: {medium_confidence} 个")
    
    return all_ui_strings


def generate_ui_report_files(ui_strings, json_output_file, report_output_file):
    """生成UI字符串报告文件"""
    if not ui_strings:
        print("👍 没有发现需要本地化的UI字符串！")
        return
    
    # 按文件和置信度分组
    strings_by_file = defaultdict(lambda: {'high': [], 'medium': []})
    for item in ui_strings:
        strings_by_file[item['file']][item['confidence']].append(item)
    
    # 生成JSON文件
    try:
        json_data = {
            'summary': {
                'total_files': len(strings_by_file),
                'total_strings': len(ui_strings),
                'high_confidence': len([s for s in ui_strings if s['confidence'] == 'high']),
                'medium_confidence': len([s for s in ui_strings if s['confidence'] == 'medium']),
                'scan_directories': SCAN_DIRECTORIES
            },
            'strings_by_file': {}
        }
        
        for file_path, confidence_groups in strings_by_file.items():
            relative_path = os.path.relpath(file_path, PROJECT_ROOT)
            json_data['strings_by_file'][relative_path] = {
                'high_confidence': [
                    {
                        'line': s['line'],
                        'text': s['text'],
                        'context': s['context']
                    }
                    for s in confidence_groups['high']
                ],
                'medium_confidence': [
                    {
                        'line': s['line'],
                        'text': s['text'],
                        'context': s['context']
                    }
                    for s in confidence_groups['medium']
                ]
            }
        
        with open(json_output_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2, sort_keys=True)
        
        print(f"📄 JSON报告已保存到: {json_output_file}")
    
    except Exception as e:
        print(f"❌ 生成JSON文件时出错: {e}")
    
    # 生成文本报告
    try:
        with open(report_output_file, 'w', encoding='utf-8') as f:
            f.write("# 需要本地化的UI中文字符串报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"扫描目录: {', '.join(SCAN_DIRECTORIES)}\n")
            f.write(f"总文件数: {len(strings_by_file)}\n")
            f.write(f"总字符串数: {len(ui_strings)}\n")
            f.write(f"高置信度: {len([s for s in ui_strings if s['confidence'] == 'high'])}\n")
            f.write(f"中等置信度: {len([s for s in ui_strings if s['confidence'] == 'medium'])}\n\n")
            
            for file_path in sorted(strings_by_file.keys()):
                relative_path = os.path.relpath(file_path, PROJECT_ROOT)
                confidence_groups = strings_by_file[file_path]
                
                total_strings = len(confidence_groups['high']) + len(confidence_groups['medium'])
                f.write(f"## {relative_path}\n")
                f.write(f"发现 {total_strings} 个可能的UI字符串:\n\n")
                
                # 先显示高置信度的
                if confidence_groups['high']:
                    f.write("### 🔴 高置信度 (强烈建议本地化)\n\n")
                    for s in sorted(confidence_groups['high'], key=lambda x: x['line']):
                        f.write(f"**第 {s['line']} 行**:\n")
                        f.write(f"```\n{s['context']}\n```\n")
                        f.write(f"中文内容: `{s['text']}`\n\n")
                
                # 再显示中等置信度的
                if confidence_groups['medium']:
                    f.write("### 🟡 中等置信度 (建议检查)\n\n")
                    for s in sorted(confidence_groups['medium'], key=lambda x: x['line']):
                        f.write(f"**第 {s['line']} 行**:\n")
                        f.write(f"```\n{s['context']}\n```\n")
                        f.write(f"中文内容: `{s['text']}`\n\n")
                
                f.write("-" * 40 + "\n\n")
        
        print(f"📄 文本报告已保存到: {report_output_file}")
    
    except Exception as e:
        print(f"❌ 生成文本报告时出错: {e}")

def main():
    ui_strings = scan_project_for_ui_strings(PROJECT_ROOT, SCAN_DIRECTORIES)
    generate_ui_report_files(ui_strings, OUTPUT_FILE, REPORT_FILE)

if __name__ == '__main__':
    main()
