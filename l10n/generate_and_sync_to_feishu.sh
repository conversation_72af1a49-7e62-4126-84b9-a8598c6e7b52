#!/bin/bash

# L10n 翻译生成和飞书同步脚本
# 自动执行翻译文件生成并上传到飞书电子表格的完整流程

set -e  # 遇到错误时立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_step() {
    echo -e "\n${BLUE}🚀 $1${NC}"
    echo "----------------------------------------"
}

# 检查Python是否可用
check_python() {
    if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
        print_error "Python未找到，请确保已安装Python"
        exit 1
    fi
    
    # 优先使用python3，如果不存在则使用python
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    else
        PYTHON_CMD="python"
    fi
    
    print_info "使用Python命令: $PYTHON_CMD"
}

# 检查必要的文件是否存在
check_files() {
    local missing_files=()
    
    if [ ! -f "l10n/run_l10n.sh" ]; then
        missing_files+=("l10n/run_l10n.sh")
    fi
    
    if [ ! -f "l10n/sheet_updater.py" ]; then
        missing_files+=("l10n/sheet_updater.py")
    fi
    
    if [ ${#missing_files[@]} -ne 0 ]; then
        print_error "缺少必要文件:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        exit 1
    fi
    
    print_success "所有必要文件检查通过"
}

# 显示使用说明
show_usage() {
    echo "使用方法:"
    echo "  $0 <工作表名称> [飞书表格Token] [是否更新配置表格]"
    echo ""
    echo "参数说明:"
    echo "  工作表名称        - 必需，目标工作表的名称"
    echo "  飞书表格Token     - 可选，默认为: OinnsRvWNhjOYGt2560cHc82nmh"
    echo "  是否更新配置表格   - 可选，true/false，默认为: false"
    echo ""
    echo "示例:"
    echo "  $0 \"翻译对比表\""
    echo "  $0 \"翻译对比表\" \"OinnsRvWNhjOYGt2560cHc82nmh\""
    echo "  $0 \"翻译对比表\" \"OinnsRvWNhjOYGt2560cHc82nmh\" true"
}

# 主函数
main() {
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                L10n 翻译生成和飞书同步脚本                      ║"
    echo "║                                                              ║"
    echo "║  此脚本将自动执行以下步骤：                                      ║"
    echo "║  1. 检查运行环境和必要文件                                       ║"
    echo "║  2. 执行翻译文件生成流程                                        ║"
    echo "║  3. 上传CSV文件到飞书电子表格                                   ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}\n"
    
    # 检查参数
    if [ $# -lt 1 ]; then
        print_error "参数不足"
        show_usage
        exit 1
    fi
    
    # 解析参数
    WORKSHEET_NAME="$1"
    SHEET_TOKEN="${2:-OinnsRvWNhjOYGt2560cHc82nmh}"
    UPDATE_CONFIG="${3:-false}"
    
    print_info "工作表名称: $WORKSHEET_NAME"
    print_info "飞书表格Token: $SHEET_TOKEN"
    print_info "是否更新配置表格: $UPDATE_CONFIG"
    
    # 检查环境
    print_step "检查运行环境"
    check_python
    check_files
    
    # 步骤1: 执行翻译文件生成
    print_step "步骤1: 生成翻译文件"
    print_info "正在执行: ./l10n/run_l10n.sh"
    
    if ./l10n/run_l10n.sh; then
        print_success "翻译文件生成完成"
    else
        print_error "翻译文件生成失败"
        exit 1
    fi
    
    # 检查生成的CSV文件是否存在
    if [ ! -f "l10n/translations_comparison.csv" ]; then
        print_error "翻译对比CSV文件不存在: l10n/translations_comparison.csv"
        exit 1
    fi
    
    # 步骤2: 构建命令参数
    CMD_ARGS="-w \"$WORKSHEET_NAME\" -s \"$SHEET_TOKEN\""
    
    if [ "$UPDATE_CONFIG" = "true" ]; then
        CMD_ARGS="$CMD_ARGS -c"
    fi
    
    # 步骤3: 上传CSV文件到飞书
    print_step "步骤2: 上传CSV文件到飞书电子表格"
    print_info "正在执行: $PYTHON_CMD l10n/sheet_updater.py $CMD_ARGS"
    
    if eval "$PYTHON_CMD l10n/sheet_updater.py $CMD_ARGS"; then
        print_success "CSV文件上传完成"
    else
        print_error "CSV文件上传失败"
        exit 1
    fi
    
    # 完成总结
    print_step "执行完成"
    echo -e "${GREEN}"
    echo "🎉 L10n翻译生成和飞书同步脚本执行完成！"
    echo ""
    echo "操作结果："
    echo "  📄 已生成基准翻译文件: l10n/localizable.json"
    echo "  📊 已生成翻译对比文件: l10n/translations_comparison.csv"
    echo "  ☁️  已将翻译对比文件上传到飞书工作表: $WORKSHEET_NAME"
    echo "  🔗 请在飞书中查看更新结果"
    echo -e "${NC}"
}

# 脚本入口
main "$@"