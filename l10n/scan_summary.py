#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文字符串扫描结果总结
对比不同扫描器的结果，提供最终建议
"""

import json
import os
import scan_production_ui_strings
import scan_ui_chinese_strings
import scan_chinese_strings

def load_json_report(file_path):
    """加载JSON报告文件"""
    if not os.path.exists(file_path):
        return None
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {file_path}, 错误: {e}")
        return None

def print_summary():
    """打印扫描结果总结"""
    print("=" * 60)
    print("🔍 中文字符串扫描结果总结")
    print("=" * 60)
    
    # 加载各个报告
    reports = {
        "完整扫描": "l10n/chinese_strings_without_l10n.json",
        "UI字符串扫描": "l10n/ui_chinese_strings.json", 
        "生产环境UI扫描": "l10n/production_ui_strings.json"
    }
    
    results = {}
    for name, file_path in reports.items():
        data = load_json_report(file_path)
        if data and 'summary' in data:
            results[name] = data['summary']
        else:
            results[name] = None
    
    print("\n📊 扫描结果对比:")
    print("-" * 60)
    print(f"{'扫描类型':<15} {'总文件数':<8} {'总字符串数':<10} {'高置信度':<8} {'中等置信度':<8}")
    print("-" * 60)
    
    for name, summary in results.items():
        if summary:
            total_files = summary.get('total_files', 0)
            total_strings = summary.get('total_strings', 0)
            high_conf = summary.get('high_confidence', 0)
            medium_conf = summary.get('medium_confidence', 0)
            print(f"{name:<15} {total_files:<8} {total_strings:<10} {high_conf:<8} {medium_conf:<8}")
        else:
            print(f"{name:<15} {'N/A':<8} {'N/A':<10} {'N/A':<8} {'N/A':<8}")
    
    print("\n🎯 推荐处理优先级:")
    print("-" * 40)
    
    # 生产环境UI扫描结果
    prod_data = load_json_report("l10n/production_ui_strings.json")
    if prod_data and 'strings_by_file' in prod_data:
        prod_summary = results.get("生产环境UI扫描")
        prod_count = prod_summary.get('total_strings', 0) if prod_summary else 0
        print(f"🔴 【最高优先级】生产环境UI字符串 ({prod_count}个)")
        print("   这些是真正需要本地化的用户界面文本")
        print("   已排除：预览函数、日志、异常、测试代码、companion object预览数据")
        
        # 按文件分组显示
        high_priority_files = []
        for file_path, file_data in prod_data['strings_by_file'].items():
            high_count = len(file_data.get('high_confidence', []))
            medium_count = len(file_data.get('medium_confidence', []))
            if high_count > 0 or medium_count > 0:
                high_priority_files.append((file_path, high_count + medium_count))
        
        # 按字符串数量排序
        high_priority_files.sort(key=lambda x: x[1], reverse=True)
        
        print("\n   📁 重点文件 (按字符串数量排序):")
        for file_path, count in high_priority_files[:10]:  # 显示前10个
            print(f"      • {file_path}: {count}个字符串")
        
        if len(high_priority_files) > 10:
            print(f"      ... 还有 {len(high_priority_files) - 10} 个文件")
    
    print("\n🟡 【中等优先级】其他可能的UI字符串")
    print("   可以人工审查，判断是否需要本地化")
    
    print("\n🟢 【低优先级】完整扫描发现的其他字符串")
    print("   主要是注释、测试代码、调试信息等")
    
    print("\n💡 使用建议:")
    print("-" * 40)
    print(f"1. 优先处理生产环境UI扫描的结果 ({prod_count}个字符串)")
    print("2. 使用以下本地化方法:")
    print("   • .localized - 简单字符串")
    print("   • .localizedFormat() - 带占位符的字符串") 
    print("   • .localizedWithKey() - 自定义key")
    print("   • .localizedFormatWithKey() - 自定义key + 占位符")
    print("3. 处理完高优先级后，可以审查其他扫描结果")
    
    print("\n📄 生成的报告文件:")
    print("-" * 40)
    report_files = [
        ("l10n/production_ui_strings_report.txt", "生产环境UI字符串详细报告 (推荐)"),
        ("l10n/production_ui_strings.json", "生产环境UI字符串JSON数据"),
        ("l10n/ui_chinese_strings_report.txt", "UI字符串扫描报告"),
        ("l10n/chinese_strings_report.txt", "完整扫描报告"),
    ]
    
    for file_path, description in report_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
            print(f"   {description}")
        else:
            print(f"❌ {file_path} (文件不存在)")
    
    print("\n" + "=" * 60)
    print("🎉 扫描完成！建议优先处理生产环境UI字符串报告中的内容。")
    print("=" * 60)

if __name__ == '__main__':
    scan_chinese_strings.main()
    scan_ui_chinese_strings.main()
    scan_production_ui_strings.main()
    print_summary()
