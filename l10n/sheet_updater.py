# -*- coding: utf-8 -*-

import requests
import csv
import argparse
import time
import sys
import copy

# --- 配置区域 ---
# 请在此处填写您的飞书应用的 App ID 和 App Secret
# 警告：请勿将这些凭证公开分享。如果已泄露，请立即重置。
APP_ID = "cli_a65fcc556b781013"
APP_SECRET = "67CSuu7sFzCU3YM8ogBQkQbT20MYE2Yz"
# 飞书/Lark 的域名，用于生成最终的URL。例如 "feishu.cn" 或 "larksuite.com"
FEISHU_DOMAIN = "feishu.cn"
# --- 配置区域结束 ---

def get_column_letter(col_idx):
    """将列的数字索引 (1-based) 转换为字母 (A, B, C...)."""
    letter = ''
    while col_idx > 0:
        col_idx, remainder = divmod(col_idx - 1, 26)
        letter = chr(65 + remainder) + letter
    return letter

def normalize_data_for_comparison(data, force_cols=None):
    """
    将二维列表标准化。
    如果提供了 force_cols，则所有行都会被补齐到该宽度。
    否则，会按数据自身的最大宽度进行补齐。
    """
    if not data:
        return []

    max_cols = force_cols
    if max_cols is None:
        max_cols = 0
        for row in data:
            if len(row) > max_cols:
                max_cols = len(row)

    normalized = []
    for row in data:
        normalized_row = row[:]
        while len(normalized_row) < max_cols:
            normalized_row.append('')
        normalized.append(normalized_row)

    return normalized

def print_data_diff(old_data, new_data):
    """比较并打印两个数据集之间的差异，使用全局标准化。"""
    print("\n--- 检测到的数据差异详情 ---")

    # 1. 找到全局最大列数
    global_max_cols = 0
    for row in old_data:
        if len(row) > global_max_cols: global_max_cols = len(row)
    for row in new_data:
        if len(row) > global_max_cols: global_max_cols = len(row)

    # 2. 使用全局最大列数来标准化两个数据集
    norm_old = normalize_data_for_comparison(old_data, force_cols=global_max_cols)
    norm_new = normalize_data_for_comparison(new_data, force_cols=global_max_cols)

    max_rows = max(len(norm_old), len(norm_new))

    diff_found = False
    for r in range(max_rows):
        old_row = norm_old[r] if r < len(norm_old) else [''] * global_max_cols
        new_row = norm_new[r] if r < len(norm_new) else [''] * global_max_cols

        row_has_diff = False
        diff_details = []

        for c in range(global_max_cols):
            old_val = old_row[c]
            new_val = new_row[c]

            if old_val != new_val:
                diff_found = True
                row_has_diff = True
                col_letter = get_column_letter(c + 1)
                diff_details.append(f"  - 列 {col_letter}: 旧值='{old_val}' → 新值='{new_val}'")

        if row_has_diff:
            key_info = f"(Key: {new_row[0]})" if new_row and new_row[0] else "(新增/删除的行或Key为空)"
            print(f"第 {r + 1} 行 {key_info}:")
            for detail in diff_details:
                print(detail)

    if not diff_found:
        print("逻辑错误：主程序检测到差异，但差异打印函数未找到。请检查标准化逻辑。")

    print("---------------------------------\n")

def merge_data(csv_data, existing_data):
    """
    根据指定逻辑合并 CSV 数据和已存在的数据。
    以 csv_data 为基础，使用 existing_data 中有效且非空的译文来更新它。
    """
    print("开始合并数据...")
    if not existing_data:
        print("工作表中无现有数据，无需合并。")
        return csv_data

    existing_data_map = {row[0]: row for row in existing_data if row and len(row) > 1}
    merged_data = copy.deepcopy(csv_data)
    updated_rows_count = 0

    for row in merged_data:
        if not row or len(row) < 2: continue
        csv_key = row[0]
        csv_original_text = row[1]
        if csv_key in existing_data_map:
            existing_row = existing_data_map[csv_key]
            if len(existing_row) > 1 and csv_original_text == existing_row[1]:
                is_row_updated = False
                for i in range(2, len(row)):
                    if i < len(existing_row):
                        existing_translation = existing_row[i]
                        if existing_translation:
                            row[i] = existing_translation
                            is_row_updated = True
                if is_row_updated:
                    updated_rows_count += 1
    print(f"数据合并完成，共更新了 {updated_rows_count} 行的译文。")
    return merged_data


class FeishuSheetUpdater:
    """
    一个用于通过飞书开放平台API更新电子表格的类。
    """
    BASE_URL = "https://open.feishu.cn/open-apis"

    def __init__(self, app_id, app_secret):
        self.app_id = app_id
        self.app_secret = app_secret
        self.tenant_access_token = None
        self.token_expire_time = 0

        if self.app_id == "在此处填写你的APP_ID" or self.app_secret == "在此处填写你的APP_SECRET":
            print("错误: 请在脚本的配置区域填写您的 APP_ID 和 APP_SECRET。")
            sys.exit(1)

    def get_tenant_access_token(self):
        if self.tenant_access_token and self.token_expire_time > time.time():
            return True
        print("正在获取 tenant_access_token...")
        url = f"{self.BASE_URL}/auth/v3/tenant_access_token/internal"
        headers = {"Content-Type": "application/json; charset=utf-8"}
        data = {"app_id": self.app_id, "app_secret": self.app_secret}
        try:
            response = requests.post(url, headers=headers, json=data, timeout=10)
            response.raise_for_status()
            result = response.json()
            if result.get("code") == 0:
                self.tenant_access_token = result["tenant_access_token"]
                self.token_expire_time = time.time() + 6900
                print("成功获取 tenant_access_token")
                return True
            else:
                print(f"获取 tenant_access_token 失败，服务器响应: {result}")
                return False
        except requests.RequestException as e:
            print(f"请求 tenant_access_token 异常: {e}")
            return False

    def _get_auth_headers(self):
        if not self.get_tenant_access_token():
            raise Exception("无法获取 tenant_access_token，请求中断")
        return {
            "Authorization": f"Bearer {self.tenant_access_token}",
            "Content-Type": "application/json; charset=utf-8"
        }

    def list_worksheets(self, sheet_token):
        print("步骤1: 正在查询工作表列表...")
        url = f"{self.BASE_URL}/sheets/v3/spreadsheets/{sheet_token}/sheets/query"
        try:
            headers = self._get_auth_headers()
            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status()
            result = response.json()
            if result.get("code") == 0:
                sheets = result.get("data", {}).get("sheets", [])
                print(f"查询成功，共找到 {len(sheets)} 个工作表。")
                return sheets
            else:
                print(f"查询工作表列表失败，服务器响应: {result}")
                return None
        except Exception as e:
            print(f"查询工作表列表时发生异常: {e}")
            return None

    def read_data(self, sheet_token, range_str):
        """从指定范围读取数据"""
        print(f"正在从范围 {range_str} 读取现有数据...")
        url = f"{self.BASE_URL}/sheets/v2/spreadsheets/{sheet_token}/values/{range_str}"
        params = {"valueRenderOption": "ToString"}
        try:
            headers = self._get_auth_headers()
            response = requests.get(url, headers=headers, params=params, timeout=60)
            response.raise_for_status()
            result = response.json()
            if result.get("code") == 0:
                values = result.get("data", {}).get("valueRange", {}).get("values", [])
                print(f"成功读取 {len(values)} 行数据。")
                return values
            else:
                print(f"从范围 {range_str} 读取数据失败，服务器响应: {result}")
                return None
        except Exception as e:
            print(f"从范围 {range_str} 读取数据时发生异常: {e}")
            return None

    def clear_range(self, sheet_token, range_str, row_count, col_count):
        """通过向指定范围写入空字符串来清空单元格内容"""
        if row_count == 0 or col_count == 0:
            print("范围为空，无需清空。")
            return True

        print(f"正在清空范围 {range_str} 的内容...")
        empty_values = [[""] * col_count for _ in range(row_count)]
        result = self.write_data(sheet_token, range_str, empty_values)
        if result:
            print("范围内容清空成功。")
            return True
        else:
            print("范围内容清空失败。")
            return False

    def create_worksheet(self, sheet_token, title, index):
        print(f"正在创建名为 '{title}' 的新工作表，位置在 (index: {index})...")
        url = f"{self.BASE_URL}/sheets/v2/spreadsheets/{sheet_token}/sheets_batch_update"
        payload = {"requests": [{"addSheet": {"properties": {"title": title, "index": index}}}]}
        try:
            headers = self._get_auth_headers()
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()
            result = response.json()
            if result.get("code") == 0:
                new_sheet_info = result["data"]["replies"][0]["addSheet"]["properties"]
                print(f"新工作表 '{title}' 创建成功, Sheet ID: {new_sheet_info.get('sheetId')}")
                return new_sheet_info
            else:
                print(f"创建工作表失败，服务器响应: {result}")
                return None
        except Exception as e:
            print(f"创建工作表时发生异常: {e}")
            return None

    def write_data(self, sheet_token, range_str, values):
        url = f"{self.BASE_URL}/sheets/v2/spreadsheets/{sheet_token}/values"
        payload = {"valueRange": {"range": range_str, "values": values}}
        try:
            headers = self._get_auth_headers()
            response = requests.put(url, headers=headers, json=payload, timeout=60)
            response.raise_for_status()
            result = response.json()
            if result.get("code") == 0:
                return result.get("data", {})
            else:
                print(f"向范围 {range_str} 写入数据失败，服务器响应: {result}")
                return None
        except requests.exceptions.Timeout:
            print(f"向范围 {range_str} 写入数据时发生网络超时。虽然脚本报错，但数据可能已写入成功。")
            return None
        except Exception as e:
            print(f"向范围 {range_str} 写入数据时发生异常: {e}")
            return None

def run_script(csv_path, sheet_token, worksheet_name_b, update_config_c, is_debug_mode):
    print("--- 脚本开始执行 ---")
    target_sheet_id = None

    try:
        client = FeishuSheetUpdater(APP_ID, APP_SECRET)

        print(f"准备从 '{csv_path}' 读取 CSV 数据...")
        try:
            with open(csv_path, 'r', encoding='utf-8-sig') as f:
                csv_data = list(csv.reader(f))
            if not csv_data:
                print("CSV文件为空，脚本执行结束。")
                return
            print(f"CSV文件读取成功，共 {len(csv_data)} 行。")
        except FileNotFoundError:
            print(f"脚本判定失败：CSV文件未找到，路径: {csv_path}")
            return
        except Exception as e:
            print(f"脚本判定失败：读取CSV文件时发生错误: {e}")
            return

        data_to_write = csv_data

        all_sheets = client.list_worksheets(sheet_token)
        if all_sheets is None:
            print("脚本判定失败：无法获取工作表列表。")
            return

        config_worksheet, target_worksheet = None, None
        for sheet in all_sheets:
            if sheet.get("title") == worksheet_name_b:
                target_worksheet = sheet
                print(f"成功找到已存在的目标工作表 '{worksheet_name_b}'。")
            if sheet.get("title") == "配置表格":
                config_worksheet = sheet
                print("成功找到 '配置表格'。")

        if update_config_c:
            if not config_worksheet:
                print("脚本判定失败：参数 C (update_config) 为 True，但未找到名为 '配置表格' 的工作表。")
                return
            config_sheet_id = config_worksheet.get('sheet_id') or config_worksheet.get('sheetId')
            if not config_sheet_id:
                 print("脚本判定失败：无法获取 '配置表格' 的 Sheet ID。")
                 return
            print(f"正在更新 '配置表格'(ID: {config_sheet_id}) A2 单元格的值为 '{worksheet_name_b}'...")
            update_range = f"{config_sheet_id}!A2:A2"
            if not client.write_data(sheet_token, update_range, [[worksheet_name_b]]):
                print("脚本判定失败：更新 '配置表格' 失败。")
                return

        if target_worksheet:
            print(f"\n工作表 '{worksheet_name_b}' 已存在，执行合并更新流程。")
            sheet_id_to_process = target_worksheet.get('sheet_id')
            properties = target_worksheet.get('grid_properties', {})
            row_count, col_count = properties.get('row_count', 0), properties.get('column_count', 0)

            if row_count > 0 and col_count > 0:
                read_range = f"{sheet_id_to_process}!A1:{get_column_letter(col_count)}{row_count}"
                existing_data = client.read_data(sheet_token, read_range)
                if existing_data is None:
                    print("脚本判定失败：读取现有工作表数据失败。")
                    return

                data_to_write = merge_data(csv_data, existing_data)

                global_max_cols = max((len(row) for row in existing_data), default=0)
                global_max_cols = max(global_max_cols, max((len(row) for row in data_to_write), default=0))

                if normalize_data_for_comparison(data_to_write, global_max_cols) == normalize_data_for_comparison(existing_data, global_max_cols):
                    print("\n比较完成：合并后的数据与工作表中的现有数据完全一致。")
                    print("--- 脚本执行成功（无需写入） ---")
                    target_sheet_id = sheet_id_to_process
                    return
                else:
                    # --- 【核心修改】根据调试模式决定是否打印详细差异 ---
                    if is_debug_mode:
                        print_data_diff(existing_data, data_to_write)
                    else:
                        print("\n数据有变更，将执行更新操作...")

                print("准备清空工作表内容以便写入新数据...")
                if not client.clear_range(sheet_token, read_range, row_count, col_count):
                    print("脚本判定失败：清空工作表内容失败。")
                    return
            else:
                print("工作表存在但为空，将直接写入CSV数据。")

            target_sheet_id = target_worksheet.get('sheet_id')

        if not target_worksheet:
            created_sheet = client.create_worksheet(sheet_token, worksheet_name_b, len(all_sheets))
            if not created_sheet:
                print("脚本判定失败：创建新工作表失败。")
                return
            target_sheet_id = created_sheet.get('sheetId')

        if not target_sheet_id:
            print("脚本判定失败：无法从工作表对象中获取 Sheet ID。")
            return

        print(f"\n后续操作将在工作表 '{worksheet_name_b}' (ID: {target_sheet_id}) 中进行。\n")
        print(f"准备写入最终数据到工作表...")

        num_rows = len(data_to_write)
        num_cols = len(data_to_write[0]) if num_rows > 0 and data_to_write[0] else 1
        print(f"最终待写入数据共 {num_rows} 行, {num_cols} 列。")

        MAX_ROWS_PER_WRITE = 5000
        for i in range(0, num_rows, MAX_ROWS_PER_WRITE):
            chunk = data_to_write[i:i + MAX_ROWS_PER_WRITE]
            start_row, end_row = i + 1, i + len(chunk)
            write_range = f"{target_sheet_id}!A{start_row}:{get_column_letter(num_cols)}{end_row}"
            print(f"正在写入数据到范围 {write_range} (行 {start_row} 到 {end_row})...")
            write_result = client.write_data(sheet_token, write_range, chunk)
            if write_result:
                print(f"写入成功。更新详情: {write_result.get('updatedRows', 'N/A')}行, {write_result.get('updatedColumns', 'N/A')}列, {write_result.get('updatedCells', 'N/A')}个单元格。")
            else:
                print(f"脚本判定失败：在写入行 {start_row}-{end_row} 时发生错误。")
                return
        print("\n--- 脚本执行成功 ---")
    finally:
        print("\n--- 脚本执行结束 ---")
        if sheet_token and target_sheet_id:
            print(f"操作的工作表URL: https://{FEISHU_DOMAIN}/sheets/{sheet_token}?sheet={target_sheet_id}")
        else:
            print("未能确定目标工作表，无法生成URL。")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="一个通过飞书API将CSV数据写入电子表格的脚本。")
    parser.add_argument("-w", "--worksheet-name", required=True, help="目标工作表的名称 (参数B)，此参数为必需")
    parser.add_argument("-f", "--csv-path", default="l10n/translations_comparison.csv", help="要读取的CSV文件路径 (参数A)，默认为: l10n/translations_comparison.csv")
    parser.add_argument("-s", "--sheet-token", default="OinnsRvWNhjOYGt2560cHc82nmh", help="目标飞书电子表格的Sheet Token，默认为: OinnsRvWNhjOYGt2560cHc82nmh")
    parser.add_argument("-c", "--update-config", action="store_true", default=False, help="是否更新'配置表格' (参数C)，如果设置此项则为True")
    # --- 【核心修改】新增命令行参数 ---
    parser.add_argument("--debug", action="store_true", default=False, help="启用调试模式，打印详细的数据差异信息")

    args = parser.parse_args()
    # --- 【核心修改】将新的参数传递给主函数 ---
    run_script(args.csv_path, args.sheet_token, args.worksheet_name, args.update_config, args.debug)