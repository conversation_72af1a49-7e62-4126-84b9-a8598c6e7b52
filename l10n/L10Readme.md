# L10n 国际化工具集

本目录包含用于Android项目国际化（L10n）的完整工具链，主要用于扫描代码中的翻译字符串、生成基准翻译文件，并与远程翻译服务进行对比。

## 🎯 快速开始（推荐）

使用自动化脚本一键完成整个L10n流程：

```bash
./l10n/run_l10n.sh
```

**自动化脚本功能：**
- ✅ 自动检查运行环境和必要文件
- ✅ 执行代码扫描生成基准翻译文件
- ✅ 生成多语言翻译对比CSV文件
- ✅ 提供彩色输出和详细的执行状态
- ✅ 自动检测并提醒可疑代码和翻译冲突

**输出示例：**
```
🚀 步骤1: 扫描代码生成基准翻译文件
✅ 基准翻译文件生成完成

🚀 步骤2: 生成多语言翻译对比文件
✅ 多语言翻译对比文件生成完成

🎉 L10n国际化工具执行完成！
```

## 📁 文件结构

```
l10n/
├── run_l10n.sh                   # 🚀 自动化脚本：一键执行完整L10n流程
├── generate_and_sync_to_feishu.sh # 🚀 生成翻译文件并同步到飞书脚本
├── deploy_from_feishu.sh         # 🚀 从飞书部署脚本：从飞书导出并部署到后台
├── code_scanner.py               # 核心脚本：代码扫描器，生成基准翻译文件
├── translation_comparator.py     # 翻译对比器：生成多语言对比CSV文件
├── api_client.py                 # API客户端，用于获取远程翻译数据
├── json_diff_analyzer.py         # JSON差异分析器：对比两个JSON文件的Key差异
├── sheet_updater.py              # 飞书电子表格更新工具
├── doc_exporter.py               # 飞书文档导出工具
├── post_l10n_base_json_file.py   # 后台文件上传工具
├── localizable.json               # 基准中文翻译文件（自动生成）
├── translations_comparison.csv    # 多语言对比表格（自动生成）
├── suspicious_l10n.txt           # 可疑翻译代码记录（自动生成）
├── translation_conflicts.log     # 翻译冲突日志（自动生成）
├── key_differences.txt           # JSON Key差异报告（自动生成）
├── android_en_vs_source_comparison.txt  # Android英语数据对比报告（自动生成）
├── android_ja_vs_source_comparison.txt  # Android日语数据对比报告（自动生成）
└── L10Readme.md                  # 本说明文档
```

## 🚀 主要工作流程

### 第一步：扫描代码生成基准翻译文件

使用 `code_scanner.py` 扫描项目代码，提取所有翻译字符串并生成基准中文翻译文件：

```bash
python l10n/code_scanner.py
```

**功能说明：**
- 扫描 `app/src/main/kotlin` 和 `app/src/main/java` 目录
- 支持多种翻译模式识别：
  - `if/else` 表达式：`if(condition) {"文本1"} else {"文本2"}.localized`
  - 简单调用：`"文本".localized` 或 `"文本".localizedFormat`
  - 带Key调用：`"文本".localizedWithKey(key = "key_name")` 或 `localizedFormatWithKey`
  - 多行字符串：`""".trimIndent().localizedWithKey("key")`
- 智能过滤机制，减少误报：
  - 自动跳过导入语句、函数定义、注释行
  - 排除包含特定关键词的非翻译代码
  - 正确处理多行字符串的行号标记
- 自动转义JSON中的特殊字符（如 `$` 符号转义为 `\\$`）
- 生成 `localizable.json` 基准翻译文件
- 记录无法解析的可疑代码到 `suspicious_l10n.txt`

### 第二步：生成多语言对比CSV文件

使用 `translation_comparator.py` 获取远程翻译数据并生成对比表格：

```bash
python l10n/translation_comparator.py
```

**功能说明：**
- 基于 `localizable.json` 作为基准
- 通过API获取指定语言的翻译数据（默认：英语、日语）
- **新增功能**：自动对比Android数据与基准JSON文件的Key差异
  - 为每种语言生成独立的对比报告：`android_{lang_code}_vs_source_comparison.txt`
  - 显示基准文件和Android数据中各自独有的Key
- 合并Android和iOS平台的翻译数据，以Android为准
- 生成 `translations_comparison.csv` 多语言对比表格
- 记录平台间翻译冲突到 `translation_conflicts.log`
- 按翻译完成度排序输出

## 🚀 飞书集成工具

### 生成翻译文件并同步到飞书

使用 `generate_and_sync_to_feishu.sh` 生成翻译文件并同步到飞书电子表格：

```bash
./l10n/generate_and_sync_to_feishu.sh "工作表名称" "飞书表格Token" [是否更新配置表格]
```

**功能说明：**
- 包含完整的 `run_l10n.sh` 流程（扫描代码、生成翻译对比文件）
- 自动将生成的 `l10n/translations_comparison.csv` 上传到指定的飞书工作表
- 支持创建新工作表或更新现有工作表
- 可选择是否同时更新配置表格
- 提供详细的执行状态和错误处理

**参数说明：**
- `工作表名称`：必需，目标工作表的名称
- `飞书表格Token`：必需，飞书电子表格的Token
- `是否更新配置表格`：可选，true/false，默认为false

**使用示例：**
```bash
# 基本用法
./l10n/generate_and_sync_to_feishu.sh "翻译对比表" "OinnsRvWNhjOYGt2560cHc82nmh"

# 同时更新配置表格
./l10n/generate_and_sync_to_feishu.sh "翻译对比表" "OinnsRvWNhjOYGt2560cHc82nmh" true
```

### 从飞书部署到后台系统

使用 `deploy_from_feishu.sh` 从飞书电子表格导出CSV文件并部署到后台系统：

```bash
./l10n/deploy_from_feishu.sh <config-value> [飞书表格Token] [工作表名称] [输出CSV文件路径] [第二个文件路径]
```

**功能说明：**
- 从指定的飞书工作表导出CSV格式文件
- 自动上传文件到后台翻译系统
- 支持指定第一个和第二个上传文件
- 提供完整的错误处理和状态反馈
- 使用默认参数简化操作

**参数说明：**
- `config-value`：必需，配置版本号（如：1.0.9）
- `飞书表格Token`：可选，默认为 "OinnsRvWNhjOYGt2560cHc82nmh"
- `工作表名称`：可选，默认为 "同步到服务端的数据表"
- `输出CSV文件路径`：可选，默认为 "./l10n/translate.csv"
- `第二个文件路径`：可选，如果不提供将使用导出的CSV文件

**使用示例：**
```bash
# 最简用法（仅提供版本号，其他使用默认值）
./l10n/deploy_from_feishu.sh "1.0.9"

# 自定义飞书表格和工作表
./l10n/deploy_from_feishu.sh "1.0.9" "OinnsRvWNhjOYGt2560cHc82nmh" "翻译表"

# 完整参数
./l10n/deploy_from_feishu.sh "1.0.9" "OinnsRvWNhjOYGt2560cHc82nmh" "翻译表" "./exported.csv" "./localizable.po"
```

## 🛠️ 辅助工具

### JSON Key差异对比

使用 `json_diff_analyzer.py` 对比两个JSON文件的Key差异：

```bash
python l10n/json_diff_analyzer.py
```

**功能特性：**
- **独立脚本模式**：直接运行脚本，使用预设配置文件路径
- **函数调用模式**：支持其他脚本调用 `compare_json_keys()` 函数
- **灵活输入**：支持文件路径或直接传入数据字典
- **自定义输出**：可指定输出文件路径，默认为 `key_differences.txt`
- **详细报告**：分别显示两个数据源中独有的Key列表

**配置说明：**
- 修改脚本中的 `FILE_1` 和 `FILE_2` 变量指定要对比的文件
- 结果输出到 `key_differences.txt`

### API客户端

`api_client.py` 提供了与远程翻译服务的接口：
- 用户认证和登录
- 获取指定语言和平台的翻译数据
- 处理网络请求和JSON解析错误

## ⚙️ 配置说明

### code_scanner.py 配置

```python
PROJECT_ROOT = '.'                                    # 项目根目录
SCAN_DIRECTORIES = ['app/src/main/kotlin', 'app/src/main/java']  # 扫描目录
OUTPUT_FILE = 'l10n/localizable.json'               # 输出文件
SUSPICIOUS_OUTPUT_FILE = 'l10n/suspicious_l10n.txt' # 可疑代码记录
```

### translation_comparator.py 配置

```python
USER_TOKEN = "18211110007"        # 用户认证令牌
VERIFY_CODE = "555555"            # 验证码
SOURCE_JSON_FILE = 'l10n/localizable.json'  # 基准文件
LANGUAGES_TO_COMPARE = ['en', 'ja']         # 要对比的语言列表
OUTPUT_CSV_FILE = 'l10n/translations_comparison.csv'  # 输出CSV文件
```

### api_client.py 配置

```python
TEST_SERVER = "https://fastapi.wakooclub.com"        # API服务器地址
ACCESS_KEY = "aa8396d1c7bc42bc89462a196879f9bd"       # 访问密钥
SECRET_KEY = "dd1ad7ddb8d84801ae182794732003e5"       # 签名密钥
```

### sheet_updater.py 配置

```python
APP_ID = "cli_a65fcc556b781013"                     # 飞书应用ID
APP_SECRET = "67CSuu7sFzCU3YM8ogBQkQbT20MYE2Yz"     # 飞书应用密钥
FEISHU_DOMAIN = "feishu.cn"                         # 飞书域名
```

### doc_exporter.py 配置

```python
APP_ID = "cli_a65fcc556b781013"                     # 飞书应用ID
APP_SECRET = "67CSuu7sFzCU3YM8ogBQkQbT20MYE2Yz"     # 飞书应用密钥
```

### post_l10n_base_json_file.py 配置

```python
ACCOUNT_NAME = "xiejing"                           # 后台系统用户名
ACCOUNT_PASSWORD = "@WSX3edc123"                   # 后台系统密码
PLATFORM_ID_TASK1 = "42"                          # 平台ID（版本化资源）
PLATFORM_ID_TASK2 = "42"                          # 平台ID（翻译CSV）
```

## 📋 使用示例

### 方式一：完整的飞书集成工作流程（推荐）

**步骤1：生成翻译文件并同步到飞书**
```bash
./l10n/generate_and_sync_to_feishu.sh "翻译对比表" "OinnsRvWNhjOYGt2560cHc82nmh"
```

**步骤2：从飞书部署到后台系统**
```bash
./l10n/deploy_from_feishu.sh "1.0.9"
```

### 方式二：分步执行

**步骤1：仅生成翻译对比文件**
```bash
./l10n/run_l10n.sh
```

**步骤2：手动同步到飞书**
```bash
./l10n/generate_and_sync_to_feishu.sh "翻译对比表" "OinnsRvWNhjOYGt2560cHc82nmh"
```

**步骤3：从飞书部署到后台**
```bash
./l10n/deploy_from_feishu.sh "1.0.9" "OinnsRvWNhjOYGt2560cHc82nmh" "翻译对比表"
```

### 方式三：手动执行各个步骤

1. **扫描代码生成基准文件：**
   ```bash
   python l10n/code_scanner.py
   ```

2. **生成多语言对比表格：**
   ```bash
   python l10n/translation_comparator.py
   ```

3. **检查可疑代码（可选）：**
   ```bash
   cat l10n/suspicious_l10n.txt
   ```

4. **查看翻译冲突（可选）：**
   ```bash
   cat l10n/translation_conflicts.log
   ```

### 对比不同版本的翻译文件

```bash
# 修改 json_diff_analyzer.py 中的文件路径
# FILE_1 = 'l10n/localizable.json'
# FILE_2 = 'l10n/zh-Tw.json'
python l10n/json_diff_analyzer.py
```

## 📊 输出文件说明

- **localizable.json**: 基准中文翻译文件，包含所有从代码中提取的翻译字符串
- **translations_comparison.csv**: 多语言对比表格，按翻译完成度排序
- **suspicious_l10n.txt**: 包含无法自动解析的翻译相关代码，需要人工检查
- **translation_conflicts.log**: 记录Android和iOS平台间的翻译差异
- **key_differences.txt**: 两个JSON文件间的Key差异报告
- **android_{lang_code}_vs_source_comparison.txt**: Android各语言数据与基准文件的Key差异对比报告
  - 显示基准文件中独有的Key（Android版本缺失的翻译）
  - 显示Android数据中独有的Key（基准文件未包含的翻译）

## 🔧 故障排除

### 常见问题

1. **扫描结果为空**
   - 检查 `SCAN_DIRECTORIES` 配置是否正确
   - 确认代码中使用了支持的翻译调用模式

2. **API请求失败**
   - 检查网络连接
   - 验证 `USER_TOKEN` 和 `VERIFY_CODE` 是否正确
   - 确认API服务器地址和密钥配置

3. **JSON解析错误**
   - 检查生成的JSON文件格式是否正确
   - 确认文件编码为UTF-8

### 调试建议

- 查看控制台输出的详细日志信息
- 检查生成的 `suspicious_l10n.txt` 文件
- 验证API返回的数据格式

## 📝 注意事项

1. 确保项目代码使用标准的翻译调用模式
2. 定期更新API配置和认证信息
3. 检查可疑代码文件，手动处理无法自动识别的翻译字符串
4. 翻译冲突需要人工审核和决策
5. 建议在代码变更后重新运行扫描流程

## 🔄 版本更新记录

### v2.1 (2025-01-21)
- **Python脚本重命名优化**：
  - `scan_l10n_to_json.py` → `code_scanner.py`（代码扫描器）
  - `generate_translation_csv.py` → `translation_comparator.py`（翻译对比器）
  - `compare_json_keys.py` → `json_diff_analyzer.py`（JSON差异分析器）
  - 更新所有相关引用和文档说明

### v2.0 (2025-01-21)
- **code_scanner.py 优化**：
  - 增强正则表达式，支持 `localizedWithKey` 和 `localizedFormatWithKey`
  - 完善多行字符串处理逻辑，正确标记已处理的行号范围
  - 优化可疑代码过滤机制，减少误报
  - 自动转义JSON特殊字符（如 `$` 符号）
- **json_diff_analyzer.py 增强**：
  - 新增函数化支持，可被其他脚本调用
  - 支持直接传入数据字典，无需文件路径
  - 自定义输出文件路径功能
- **translation_comparator.py 新功能**：
  - 集成自动对比功能，对比Android数据与基准JSON文件
  - 为每种语言生成独立的Key差异报告
  - 提供详细的对比统计信息

### v1.0 (2025-08-12)
- 初始版本发布
- 基础的代码扫描和翻译对比功能

---

*最后更新：2025-08-22*