#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
扫描代码中包含中文但没有调用本地化方法的字符串
"""

import os
import re
import json
from collections import defaultdict

# --- 配置 ---
PROJECT_ROOT = '.'
SCAN_DIRECTORIES = ['app/src/main/java', 'lib-webview/src']
OUTPUT_FILE = 'l10n/chinese_strings_without_l10n.json'
REPORT_FILE = 'l10n/chinese_strings_report.txt'

# --- 正则表达式 ---

# 匹配包含中文字符的字符串字面量
PATTERN_CHINESE_STRING = re.compile(r'"([^"]*[\u4e00-\u9fff][^"]*)"')

# 匹配三引号字符串中的中文
PATTERN_CHINESE_MULTILINE = re.compile(r'"""([\s\S]*?[\u4e00-\u9fff][\s\S]*?)"""')

# 匹配本地化方法调用（这些字符串应该被排除）
LOCALIZATION_METHODS = [
    r'\.localized\b',
    r'\.localizedFormat\b', 
    r'\.localizedWithKey\b',
    r'\.localizedFormatWithKey\b'
]

# 组合所有本地化方法的正则表达式
PATTERN_LOCALIZED_CALL = re.compile('|'.join(LOCALIZATION_METHODS))

# 匹配字符串字面量后跟本地化方法调用
PATTERN_STRING_WITH_L10N = re.compile(
    r'"([^"]*[\u4e00-\u9fff][^"]*)"' +  # 包含中文的字符串
    r'\s*(?:\.trimIndent\(\))?\s*' +    # 可选的trimIndent调用
    r'(?:' + '|'.join(LOCALIZATION_METHODS) + ')'  # 本地化方法调用
)

# 匹配三引号字符串后跟本地化方法调用
PATTERN_MULTILINE_WITH_L10N = re.compile(
    r'"""([\s\S]*?[\u4e00-\u9fff][\s\S]*?)"""\s*' +  # 包含中文的三引号字符串
    r'(?:\.trimIndent\(\))?\s*' +                      # 可选的trimIndent调用
    r'(?:' + '|'.join(LOCALIZATION_METHODS) + ')'      # 本地化方法调用
)

# 需要排除的模式
EXCLUDE_PATTERNS = [
    r'//.*',           # 单行注释
    r'/\*[\s\S]*?\*/', # 多行注释
    r'\*.*',           # 多行注释中的行
    r'@\w+',           # 注解
    r'import\s+',      # import语句
    r'package\s+',     # package语句
    r'Log\.[deiw]',    # 日志输出
    r'println',        # 打印语句
    r'print\(',        # 打印函数
    r'assert',         # 断言
    r'TODO',           # TODO注释
    r'FIXME',          # FIXME注释
    r'@Preview',       # Compose预览
    r'@Composable',    # Compose组件
    r'fun\s+\w*[Pp]review',  # 预览函数
    r'fun\s+\w*[Dd]emo',     # 演示函数
    r'fun\s+\w*[Tt]est',     # 测试函数
    r'class\s+\w*[Tt]est',   # 测试类
    r'object\s+\w*[Tt]est',  # 测试对象
    r'\.debug\(',      # 调试相关
    r'BuildConfig\.DEBUG', # 调试构建
]

EXCLUDE_PATTERN = re.compile('|'.join(EXCLUDE_PATTERNS), re.IGNORECASE)

# 需要排除的文件路径模式
EXCLUDE_FILE_PATTERNS = [
    r'/demo/',         # 演示文件夹
    r'/test/',         # 测试文件夹
    r'/debug/',        # 调试文件夹
    r'Test\.kt$',      # 测试文件
    r'Demo\.kt$',      # 演示文件
    r'Preview\.kt$',   # 预览文件
    r'Sample\.kt$',    # 示例文件
]

EXCLUDE_FILE_PATTERN = re.compile('|'.join(EXCLUDE_FILE_PATTERNS), re.IGNORECASE)


def contains_chinese(text):
    """检查文本是否包含中文字符"""
    return bool(re.search(r'[\u4e00-\u9fff]', text))


def is_excluded_file(file_path):
    """检查是否应该排除这个文件"""
    return EXCLUDE_FILE_PATTERN.search(file_path) is not None


def is_excluded_line(line):
    """检查是否应该排除这一行"""
    stripped = line.strip()

    # 排除空行
    if not stripped:
        return True

    # 排除匹配排除模式的行
    if EXCLUDE_PATTERN.search(line):
        return True

    # 排除看起来像数据定义的行（JSON格式）
    if re.match(r'^\s*"[^"]*":\s*"[^"]*"[,}]?\s*$', stripped):
        return True

    # 排除看起来像变量赋值的行
    if re.match(r'^\s*\w+\s*=\s*"[^"]*"[,;]?\s*$', stripped):
        return True

    return False


def is_likely_ui_text(text, context):
    """判断是否可能是需要本地化的UI文本"""
    # 排除过短的文本
    if len(text.strip()) < 2:
        return False

    # 排除纯标点符号
    if re.match(r'^[^\u4e00-\u9fff]*$', text.replace(' ', '')):
        return False

    # 排除看起来像变量名或键名的文本
    if re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', text):
        return False

    # 排除包含特殊格式的文本（如日期格式、URL等）
    if any(pattern in text for pattern in ['yyyy', 'MM', 'dd', 'http', 'www', '.com', '.json']):
        return False

    # 排除包含代码相关关键词的文本
    code_keywords = ['null', 'true', 'false', 'void', 'return', 'if', 'else', 'for', 'while']
    if any(keyword in text.lower() for keyword in code_keywords):
        return False

    # 如果上下文包含特定模式，可能是UI文本
    ui_indicators = ['text =', 'title =', 'label =', 'hint =', 'message =', 'content =', 'description =']
    if any(indicator in context.lower() for indicator in ui_indicators):
        return True

    # 如果文本长度适中且包含常见UI词汇，可能是UI文本
    ui_words = ['按钮', '点击', '确认', '取消', '保存', '删除', '编辑', '设置', '登录', '注册', '退出']
    if len(text) >= 2 and len(text) <= 20 and any(word in text for word in ui_words):
        return True

    return False


def extract_chinese_strings_from_file(file_path):
    """从单个文件中提取包含中文但没有本地化的字符串"""
    chinese_strings = []

    # 检查是否应该排除这个文件
    if is_excluded_file(file_path):
        return chinese_strings

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')

        # 首先找出所有已经本地化的中文字符串（需要排除的）
        localized_strings = set()

        # 查找普通字符串的本地化调用
        for match in PATTERN_STRING_WITH_L10N.finditer(content):
            localized_strings.add(match.group(1))

        # 查找三引号字符串的本地化调用
        for match in PATTERN_MULTILINE_WITH_L10N.finditer(content):
            localized_strings.add(match.group(1))

        # 然后找出所有包含中文的字符串
        for i, line in enumerate(lines, 1):
            if is_excluded_line(line):
                continue

            # 查找普通字符串中的中文
            for match in PATTERN_CHINESE_STRING.finditer(line):
                chinese_text = match.group(1)

                # 跳过已经本地化的字符串
                if chinese_text in localized_strings:
                    continue

                # 检查这个字符串是否在同一行有本地化调用
                line_after_string = line[match.end():]
                if PATTERN_LOCALIZED_CALL.search(line_after_string):
                    continue

                # 使用智能过滤判断是否可能是UI文本
                if not is_likely_ui_text(chinese_text, line):
                    continue

                chinese_strings.append({
                    'file': file_path,
                    'line': i,
                    'text': chinese_text,
                    'context': line.strip(),
                    'type': 'string_literal',
                    'priority': 'high' if is_likely_ui_text(chinese_text, line) else 'low'
                })

        # 查找三引号字符串中的中文
        for match in PATTERN_CHINESE_MULTILINE.finditer(content):
            chinese_text = match.group(1)

            # 跳过已经本地化的字符串
            if chinese_text in localized_strings:
                continue

            # 检查这个字符串是否有本地化调用
            content_after_string = content[match.end():]
            if PATTERN_LOCALIZED_CALL.search(content_after_string[:100]):  # 检查后面100个字符
                continue

            # 计算行号
            line_num = content[:match.start()].count('\n') + 1

            # 使用智能过滤判断是否可能是UI文本
            context = f'"""多行字符串 (第{line_num}行)"""'
            if not is_likely_ui_text(chinese_text.strip(), context):
                continue

            chinese_strings.append({
                'file': file_path,
                'line': line_num,
                'text': chinese_text.strip(),
                'context': context,
                'type': 'multiline_string',
                'priority': 'high' if is_likely_ui_text(chinese_text.strip(), context) else 'low'
            })

    except Exception as e:
        print(f"❌ 读取文件时出错: {file_path}, 错误: {e}")

    return chinese_strings


def scan_project_for_chinese_strings(root_path, directories):
    """扫描整个项目，查找包含中文但没有本地化的字符串"""
    all_chinese_strings = []
    file_count = 0
    
    print("\n🔍 开始扫描项目中包含中文但没有本地化的字符串...")
    
    for directory in directories:
        scan_path = os.path.join(root_path, directory)
        if not os.path.exists(scan_path):
            print(f"⚠️  目录不存在: {scan_path}")
            continue
        
        print(f"📁 正在扫描目录: {scan_path}")
        
        for dirpath, _, filenames in os.walk(scan_path):
            for filename in filenames:
                if filename.endswith(('.kt', '.java')):
                    file_path = os.path.join(dirpath, filename)
                    relative_path = os.path.relpath(file_path, root_path)
                    
                    chinese_strings = extract_chinese_strings_from_file(file_path)
                    if chinese_strings:
                        all_chinese_strings.extend(chinese_strings)
                        print(f"  📄 {relative_path}: 发现 {len(chinese_strings)} 个未本地化的中文字符串")
                    
                    file_count += 1
    
    print(f"\n✅ 扫描完成！")
    print(f"📊 总共扫描了 {file_count} 个文件")
    print(f"🔤 发现 {len(all_chinese_strings)} 个包含中文但没有本地化的字符串")
    
    return all_chinese_strings


def generate_report_files(chinese_strings, json_output_file, report_output_file):
    """生成报告文件"""
    if not chinese_strings:
        print("👍 没有发现未本地化的中文字符串！")
        return
    
    # 按文件分组
    strings_by_file = defaultdict(list)
    for item in chinese_strings:
        strings_by_file[item['file']].append(item)
    
    # 生成JSON文件
    try:
        json_data = {
            'summary': {
                'total_files': len(strings_by_file),
                'total_strings': len(chinese_strings),
                'scan_directories': SCAN_DIRECTORIES
            },
            'strings_by_file': {}
        }
        
        for file_path, strings in strings_by_file.items():
            relative_path = os.path.relpath(file_path, PROJECT_ROOT)
            json_data['strings_by_file'][relative_path] = [
                {
                    'line': s['line'],
                    'text': s['text'],
                    'context': s['context'],
                    'type': s['type']
                }
                for s in strings
            ]
        
        with open(json_output_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2, sort_keys=True)
        
        print(f"📄 JSON报告已保存到: {json_output_file}")
    
    except Exception as e:
        print(f"❌ 生成JSON文件时出错: {e}")
    
    # 生成文本报告
    try:
        with open(report_output_file, 'w', encoding='utf-8') as f:
            f.write("# 包含中文但没有本地化的字符串报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"扫描目录: {', '.join(SCAN_DIRECTORIES)}\n")
            f.write(f"总文件数: {len(strings_by_file)}\n")
            f.write(f"总字符串数: {len(chinese_strings)}\n\n")
            
            for file_path in sorted(strings_by_file.keys()):
                relative_path = os.path.relpath(file_path, PROJECT_ROOT)
                strings = strings_by_file[file_path]
                
                f.write(f"## {relative_path}\n")
                f.write(f"发现 {len(strings)} 个未本地化的中文字符串:\n\n")
                
                for s in sorted(strings, key=lambda x: x['line']):
                    f.write(f"**第 {s['line']} 行** ({s['type']}):\n")
                    f.write(f"```\n{s['context']}\n```\n")
                    f.write(f"中文内容: `{s['text']}`\n\n")
                
                f.write("-" * 40 + "\n\n")
        
        print(f"📄 文本报告已保存到: {report_output_file}")
    
    except Exception as e:
        print(f"❌ 生成文本报告时出错: {e}")


def main():
    """扫描项目的入口点。"""
    chinese_strings = scan_project_for_chinese_strings(PROJECT_ROOT, SCAN_DIRECTORIES)
    generate_report_files(chinese_strings, OUTPUT_FILE, REPORT_FILE)

if __name__ == '__main__':
    main()
