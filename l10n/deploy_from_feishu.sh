#!/bin/bash

# L10n 从飞书部署脚本
# 从飞书电子表格导出CSV文件，然后上传到后台系统

set -e  # 遇到错误时立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_step() {
    echo -e "\n${BLUE}🚀 $1${NC}"
    echo "----------------------------------------"
}

# 检查Python是否可用
check_python() {
    if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
        print_error "Python未找到，请确保已安装Python"
        exit 1
    fi
    
    # 优先使用python3，如果不存在则使用python
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    else
        PYTHON_CMD="python"
    fi
    
    print_info "使用Python命令: $PYTHON_CMD"
}

# 检查必要的文件是否存在
check_files() {
    local missing_files=()
    
    if [ ! -f "l10n/doc_exporter.py" ]; then
        missing_files+=("l10n/doc_exporter.py")
    fi
    
    if [ ! -f "l10n/post_l10n_base_json_file.py" ]; then
        missing_files+=("l10n/post_l10n_base_json_file.py")
    fi
    
    if [ ${#missing_files[@]} -ne 0 ]; then
        print_error "缺少必要的脚本文件:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        exit 1
    fi

    print_success "所有必要的脚本文件检查通过"
}

# 显示使用说明
show_usage() {
    echo "使用方法:"
    echo "  $0 <config-value> [飞书表格Token] [工作表名称] [输出CSV文件路径]"
    echo ""
    echo "参数说明:"
    echo "  config-value      - 必需，配置版本号（如：1.0.9）"
    echo "  飞书表格Token     - 可选，默认为: OinnsRvWNhjOYGt2560cHc82nmh"
    echo "  工作表名称        - 可选，默认为: 同步到服务端的数据表"
    echo "  输出CSV文件路径   - 可选，默认为: ./l10n/translate.csv"
    echo ""
    echo "示例:"
    echo "  $0 \"1.0.9\""
    echo "  $0 \"1.0.9\" \"OinnsRvWNhjOYGt2560cHc82nmh\" \"翻译表\""
    echo "  $0 \"1.0.9\" \"OinnsRvWNhjOYGt2560cHc82nmh\" \"翻译表\" \"./exported.csv\""
}

# 清理临时文件
cleanup() {
    if [ -n "$TEMP_CSV_FILE" ] && [ -f "$TEMP_CSV_FILE" ]; then
        print_info "清理临时文件: $TEMP_CSV_FILE"
        rm -f "$TEMP_CSV_FILE"
    fi
}

# 设置退出时清理
trap cleanup EXIT

# 主函数
main() {
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                   L10n 从飞书部署脚本                          ║"
    echo "║                                                              ║"
    echo "║  此脚本将自动执行以下步骤：                                      ║"
    echo "║  1. 检查运行环境和必要文件                                       ║"
    echo "║  2. 从飞书电子表格导出CSV文件                                   ║"
    echo "║  3. 上传文件到后台系统                                         ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}\n"

    # 检查参数
    if [ $# -lt 1 ]; then
        print_error "参数不足，至少需要提供config-value参数"
        show_usage
        exit 1
    fi

    # 解析参数
    CONFIG_VALUE="$1"
    SHEET_TOKEN="${2:-OinnsRvWNhjOYGt2560cHc82nmh}"
    WORKSHEET_NAME="${3:-同步到服务端的数据表}"
    OUTPUT_CSV_PATH="${4:-./l10n/translate.csv}"

    print_info "配置版本号: $CONFIG_VALUE"
    print_info "飞书表格Token: $SHEET_TOKEN"
    print_info "工作表名称: $WORKSHEET_NAME"
    print_info "输出CSV文件路径: $OUTPUT_CSV_PATH"

    # 检查环境
    print_step "检查运行环境"
    check_python
    check_files

    # 步骤1: 从飞书导出CSV文件
    print_step "步骤1: 从飞书电子表格导出CSV文件"
    print_info "正在执行: $PYTHON_CMD l10n/doc_exporter.py -t \"$SHEET_TOKEN\" -e sheet -o \"$OUTPUT_CSV_PATH\" --sub-id \"$WORKSHEET_NAME\" --config-value \"$CONFIG_VALUE\" --ext csv"

    if $PYTHON_CMD l10n/doc_exporter.py -t "$SHEET_TOKEN" -e sheet -o "$OUTPUT_CSV_PATH" --sub-id "$WORKSHEET_NAME" --config-value "$CONFIG_VALUE" --ext csv; then
        print_success "CSV文件导出完成: $OUTPUT_CSV_PATH"
    else
        print_error "CSV文件导出失败"
        exit 1
    fi

    # 检查导出的文件是否存在
    if [ ! -f "$OUTPUT_CSV_PATH" ]; then
        print_error "导出的CSV文件不存在: $OUTPUT_CSV_PATH"
        exit 1
    fi

    # 步骤2: 准备上传文件
    print_step "步骤2: 准备上传文件"

    # 定义要上传的两个文件
    FIRST_FILE_PATH="l10n/localizable.json"
    SECOND_FILE_PATH="$OUTPUT_CSV_PATH"

    # 检查第一个文件是否存在
    if [ ! -f "$FIRST_FILE_PATH" ]; then
        print_error "上传所需文件不存在: $FIRST_FILE_PATH"
        print_warning "请确保基础JSON文件已放置在正确位置。"
        exit 1
    fi

    print_info "第一个文件 (基础JSON): $FIRST_FILE_PATH"
    print_info "第二个文件 (飞书CSV): $SECOND_FILE_PATH"
    
    # 步骤3: 上传文件到后台系统
    print_step "步骤3: 上传文件到后台系统"
    print_info "正在执行: $PYTHON_CMD l10n/post_l10n_base_json_file.py \"$FIRST_FILE_PATH\" \"$SECOND_FILE_PATH\""
    
    if $PYTHON_CMD l10n/post_l10n_base_json_file.py "$FIRST_FILE_PATH" "$SECOND_FILE_PATH"; then
        print_success "文件上传到后台系统完成"
    else
        print_error "文件上传到后台系统失败"
        exit 1
    fi
    
    # 完成总结
    print_step "执行完成"
    echo -e "${GREEN}"
    echo "🎉 L10n从飞书部署脚本执行完成！"
    echo ""
    echo "操作结果："
    echo "  📥 已从飞书工作表 '$WORKSHEET_NAME' 导出CSV文件: $OUTPUT_CSV_PATH"
    echo "  🔧 已更新配置版本号为: $CONFIG_VALUE"
    echo "  📤 已将文件上传到后台系统"
    echo "    - 第一个文件: $FIRST_FILE_PATH"
    echo "    - 第二个文件: $SECOND_FILE_PATH"
    echo -e "${NC}"
}

# 脚本入口
main "$@"