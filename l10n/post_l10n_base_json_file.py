# -*- coding: utf-8 -*-
# -----------------------------------------------------------------------------
# 脚本功能: (最终版 - 二合一)
#   1. 自动登录。
#   2. 查询最新版本号并准备提交版本+1的第一个文件 (.po/.strings)。
#   3. 提交第一个文件。
#   4. 提交第二个翻译文件 (.csv)。
#
# 使用方法:
#   python run_upload_all.py <第一个文件路径> <第二个文件路径>
#
# 示例:
#   python run_upload_all.py ./localizable.po ./translations.csv
# -----------------------------------------------------------------------------
import requests
from bs4 import BeautifulSoup
import sys
import os
import traceback
import time

# --- 可配置参数 ---
ACCOUNT_NAME = "xiejing"
ACCOUNT_PASSWORD = "@WSX3edc123"

# --- 固定参数 ---
# 任务1: 上传版本化资源
LOGIN_URL = "https://api.gray.ucoofun.com/qyqyadmin/login/"
QUERY_URL_BASE = "https://api.gray.ucoofun.com/qyqyadmin/translator/localeconfig/"
SUBMIT_URL_TASK1 = "https://api.gray.ucoofun.com/qyqyadmin/translator/localeconfig/add/"
PLATFORM_ID_TASK1 = "42"
LANGUAGE_CODE_TASK1 = "zh-CN"

# 任务2: 上传翻译CSV
SUBMIT_URL_TASK2 = "https://api.gray.ucoofun.com/qyqyadmin/translator/translatedresource/upload_csv/"
PLATFORM_ID_TASK2 = "42" # WAKOO_ANDROID


def upload_all_files(po_file_path: str, csv_file_path: str):
    """
    执行完整的两步文件上传流程。

    Args:
        po_file_path (str): 第一个任务要上传的文件路径 (.po, .strings etc.)
        csv_file_path (str): 第二个任务要上传的文件路径 (.csv)
    """
    start_time = time.time()
    print(f"--- 任务启动于: {time.strftime('%Y-%m-%d %H:%M:%S')} ---")

    # 检查两个文件是否存在
    for path in [po_file_path, csv_file_path]:
        if not os.path.exists(path):
            print(f"错误: 文件不存在 -> {os.path.abspath(path)}")
            return
    print(f"文件1 (版本化资源): {os.path.abspath(po_file_path)}")
    print(f"文件2 (翻译CSV):    {os.path.abspath(csv_file_path)}")

    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0',
    })

    try:
        # 1. 登录
        print("\n[步骤 1/4] 正在登录...")
        login_page_res = session.get(LOGIN_URL, timeout=30, verify=False)
        login_page_res.raise_for_status()
        soup = BeautifulSoup(login_page_res.text, "html.parser")
        csrf_token = soup.find("input", {"name": "csrfmiddlewaretoken"})["value"]

        login_data = {"csrfmiddlewaretoken": csrf_token, "username": ACCOUNT_NAME, "password": ACCOUNT_PASSWORD, "next": "/qyqyadmin/"}
        login_post_res = session.post(LOGIN_URL, data=login_data, headers={'Referer': LOGIN_URL}, timeout=30, verify=False)
        login_post_res.raise_for_status()
        if "login" in login_post_res.url: raise Exception("登录失败，请检查用户名和密码。")
        print("  -> 登录成功！")

        # 2. 查询最新版本号
        print("\n[步骤 2/4] 正在查询最新版本号...")
        query_url = f"{QUERY_URL_BASE}?language={LANGUAGE_CODE_TASK1}&o=-3&platform__exact={PLATFORM_ID_TASK1}"
        query_res = session.get(query_url, timeout=30, headers={'Referer': LOGIN_URL}, verify=False)
        query_res.raise_for_status()
        query_soup = BeautifulSoup(query_res.text, 'html.parser')

        selector = "#result_list tbody tr:first-child td.field-version"
        version_cell = query_soup.select_one(selector)
        latest_version = 0
        if version_cell and version_cell.text.strip().isdigit():
            latest_version = int(version_cell.text.strip())
        new_version = latest_version + 1
        print(f"  -> 查询完成。最新版本为 {latest_version}，本次将提交版本 {new_version}。")

        # 3. 提交第一个文件
        print(f"\n[步骤 3/4] 正在提交版本 {new_version} 的资源文件...")
        submit_page_res1 = session.get(SUBMIT_URL_TASK1, timeout=30, verify=False)
        submit_page_res1.raise_for_status()
        submit_soup1 = BeautifulSoup(submit_page_res1.text, "html.parser")
        form_csrf_token1 = submit_soup1.find("input", {"name": "csrfmiddlewaretoken"})["value"]

        form_data1 = {'csrfmiddlewaretoken': form_csrf_token1, 'platform': PLATFORM_ID_TASK1, 'language': LANGUAGE_CODE_TASK1, 'version': str(new_version), '_save': '保存'}
        with open(po_file_path, "rb") as f1:
            files_to_submit1 = {'link': (os.path.basename(po_file_path), f1, 'application/octet-stream')}
            submit_res1 = session.post(SUBMIT_URL_TASK1, data=form_data1, files=files_to_submit1, headers={'Referer': SUBMIT_URL_TASK1}, timeout=180, verify=False)
            submit_res1.raise_for_status()

        # 验证结果1
        if "changelist" not in submit_res1.url and not BeautifulSoup(submit_res1.text, 'html.parser').find('li', class_='success'):
             raise Exception("第一个文件提交失败，未跳转或未找到成功消息。")
        print("  -> 第一个文件提交成功！")

        # 4. 提交第二个文件 (CSV)
        print("\n[步骤 4/4] 正在提交翻译 CSV 文件...")
        submit_page_res2 = session.get(SUBMIT_URL_TASK2, timeout=30, verify=False)
        submit_page_res2.raise_for_status()
        submit_soup2 = BeautifulSoup(submit_page_res2.text, "html.parser")
        form_csrf_token2 = submit_soup2.find("input", {"name": "csrfmiddlewaretoken"})["value"]

        form_data2 = {'csrfmiddlewaretoken': form_csrf_token2, 'platform': PLATFORM_ID_TASK2}
        # 注意：'skip_same_line' 复选框不勾选，所以不提交该字段
        with open(csv_file_path, "rb") as f2:
            files_to_submit2 = {'csv_file': (os.path.basename(csv_file_path), f2, 'text/csv')}
            submit_res2 = session.post(SUBMIT_URL_TASK2, data=form_data2, files=files_to_submit2, headers={'Referer': SUBMIT_URL_TASK2}, timeout=180, verify=False)
            submit_res2.raise_for_status()

        # 验证结果2
        # 这个页面的成功响应可能只是一个消息，需要根据实际情况调整
        # 这里我们假设成功后页面会包含 "成功" 或 "Success" 字样
        if "成功" in submit_res2.text or "Success" in submit_res2.text.lower() or "completed" in submit_res2.text.lower():
             print("  -> 第二个文件提交成功！")
        else:
            # 尝试解析是否有明确错误
            result_soup2 = BeautifulSoup(submit_res2.text, 'html.parser')
            error_node = result_soup2.find(class_='errornote') or result_soup2.find(class_='errorlist')
            if error_node:
                raise Exception(f"第二个文件提交失败: {error_node.get_text(strip=True)}")
            else:
                 # 如果没有明确的成功或失败标志，打印警告
                 print("  -> 第二个文件提交请求已发送，但未检测到明确的成功标志。请手动后台核实。")


    except Exception as e:
        print("\n--- 任务执行失败 ---")
        print(f"错误详情: {e}")
        traceback.print_exc()
    finally:
        duration = time.time() - start_time
        print(f"\n--- 任务结束，总耗时: {duration:.2f} 秒 ---")


if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("错误: 参数数量不正确。需要提供两个文件路径。")
        print(f"用法: python {os.path.basename(__file__)} <第一个文件路径> <第二个文件路径>")
        sys.exit(1)

    requests.packages.urllib3.disable_warnings(requests.packages.urllib3.exceptions.InsecureRequestWarning)
    file_path_arg1 = sys.argv[1]
    file_path_arg2 = sys.argv[2]
    upload_all_files(file_path_arg1, file_path_arg2)