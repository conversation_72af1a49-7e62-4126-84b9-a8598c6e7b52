import json
import os

# --- 配置区域 ---
# 请在这里配置你的文件名（当作为独立脚本运行时使用）

# 第一个JSON文件
FILE_1 = 'l10n/localizable.json'
# 第二个JSON文件
FILE_2 = 'l10n/zh-Tw.json'
# 输出的TXT文件名
OUTPUT_FILE = 'l10n/key_differences.txt'

# --- 脚本主逻辑 ---

def load_json_safely(filepath):
    """
    安全地加载一个JSON文件。
    如果文件不存在或格式无效，则打印错误并返回None。
    """
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 错误: 文件未找到 '{filepath}'。请检查文件名和路径。")
        return None
    except json.JSONDecodeError:
        print(f"❌ 错误: 文件 '{filepath}' 不是一个有效的JSON格式。请检查文件内容。")
        return None
    except Exception as e:
        print(f"❌ 加载文件 '{filepath}' 时发生未知错误: {e}")
        return None

def compare_json_keys(file1_path: str, file2_path: str, output_file: str = None, file1_data: dict = None, file2_data: dict = None) -> bool:
    """
    对比两个JSON文件的Key差异，支持文件路径或直接传入数据。
    
    Args:
        file1_path: 第一个JSON文件路径（用于显示名称）
        file2_path: 第二个JSON文件路径（用于显示名称）
        output_file: 输出文件路径，如果为None则使用默认路径
        file1_data: 第一个JSON的数据字典，如果提供则不从文件加载
        file2_data: 第二个JSON的数据字典，如果提供则不从文件加载
    
    Returns:
        bool: 成功返回True，失败返回False
    """
    print(f"🚀 开始对比JSON文件的Key: '{os.path.basename(file1_path)}' vs '{os.path.basename(file2_path)}'...")
    
    # 1. 加载JSON数据
    if file1_data is None:
        data1 = load_json_safely(file1_path)
        if data1 is None:
            return False
    else:
        data1 = file1_data
        
    if file2_data is None:
        data2 = load_json_safely(file2_path)
        if data2 is None:
            return False
    else:
        data2 = file2_data

    # 2. 提取Key并转换为集合(Set)以便高效对比
    keys1 = set(data1.keys())
    keys2 = set(data2.keys())

    # 3. 计算差集，找出各自独有的Key
    # 使用 sorted() 确保每次输出的顺序一致
    unique_keys_in_file1 = sorted(list(keys1 - keys2))
    unique_keys_in_file2 = sorted(list(keys2 - keys1))

    print(f"  - '{os.path.basename(file1_path)}' 中独有的Key数量: {len(unique_keys_in_file1)}")
    print(f"  - '{os.path.basename(file2_path)}' 中独有的Key数量: {len(unique_keys_in_file2)}")

    # 4. 将独有的Key列表转换为格式化的JSON数组字符串
    # ensure_ascii=False 确保中文字符等能正确显示
    # indent=4 美化输出，使其更易读
    json_str_unique_to_1 = json.dumps(unique_keys_in_file1, ensure_ascii=False, indent=4)
    json_str_unique_to_2 = json.dumps(unique_keys_in_file2, ensure_ascii=False, indent=4)

    # 5. 确定输出文件路径
    if output_file is None:
        output_file = OUTPUT_FILE

    # 6. 将结果写入TXT文件
    print(f"  - ✍️  正在将结果写入到 '{output_file}'...")
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"--- Keys unique to '{os.path.basename(file1_path)}' ---\n\n")
            f.write(json_str_unique_to_1)
            f.write("\n\n\n")  # 添加一些空行作为分隔
            f.write(f"--- Keys unique to '{os.path.basename(file2_path)}' ---\n\n")
            f.write(json_str_unique_to_2)
            f.write("\n")

        print(f"  - 🎉 成功！对比结果已保存到 '{output_file}'。")
        return True

    except Exception as e:
        print(f"  - ❌ 写入文件时发生错误: {e}")
        return False


def main():
    """主函数，执行整个对比和生成流程（独立脚本模式）。"""
    return compare_json_keys(FILE_1, FILE_2)

if __name__ == '__main__':
    main()