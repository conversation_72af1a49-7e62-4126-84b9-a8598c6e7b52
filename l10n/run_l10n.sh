#!/bin/bash

# L10n 国际化工具自动化脚本
# 自动执行扫描代码和生成翻译对比文件的完整流程

set -e  # 遇到错误时立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_step() {
    echo -e "\n${BLUE}🚀 $1${NC}"
    echo "----------------------------------------"
}

# 检查Python是否可用
check_python() {
    if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
        print_error "Python未找到，请确保已安装Python"
        exit 1
    fi
    
    # 优先使用python3，如果不存在则使用python
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    else
        PYTHON_CMD="python"
    fi
    
    print_info "使用Python命令: $PYTHON_CMD"
}

# 检查必要的文件是否存在
check_files() {
    local missing_files=()
    
    if [ ! -f "l10n/code_scanner.py" ]; then
        missing_files+=("l10n/code_scanner.py")
    fi
    
    if [ ! -f "l10n/translation_comparator.py" ]; then
        missing_files+=("l10n/translation_comparator.py")
    fi
    
    if [ ! -f "l10n/api_client.py" ]; then
        missing_files+=("l10n/api_client.py")
    fi
    
    if [ ${#missing_files[@]} -ne 0 ]; then
        print_error "缺少必要文件:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        exit 1
    fi
    
    print_success "所有必要文件检查通过"
}

# 主函数
main() {
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    L10n 国际化工具自动化脚本                    ║"
    echo "║                                                              ║"
    echo "║  此脚本将自动执行以下步骤：                                      ║"
    echo "║  1. 扫描代码生成基准中文翻译文件 (localizable.json)              ║"
    echo "║  2. 生成多语言翻译对比CSV文件 (translations_comparison.csv)     ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}\n"
    
    # 检查环境
    print_step "检查运行环境"
    check_python
    check_files
    
    # 步骤1: 扫描代码生成基准翻译文件
    print_step "步骤1: 扫描代码生成基准翻译文件"
    print_info "正在执行: $PYTHON_CMD l10n/code_scanner.py"
    
    if $PYTHON_CMD l10n/code_scanner.py; then
        print_success "基准翻译文件生成完成"
        
        # 检查是否生成了可疑代码文件
        if [ -f "l10n/suspicious_l10n.txt" ] && [ -s "l10n/suspicious_l10n.txt" ]; then
            print_warning "发现可疑翻译代码，请检查 l10n/suspicious_l10n.txt 文件"
        fi
    else
        print_error "基准翻译文件生成失败"
        exit 1
    fi
    
    # 步骤2: 生成多语言翻译对比文件
    print_step "步骤2: 生成多语言翻译对比文件"
    print_info "正在执行: $PYTHON_CMD l10n/translation_comparator.py"
    
    if $PYTHON_CMD l10n/translation_comparator.py; then
        print_success "多语言翻译对比文件生成完成"
        
        # 检查是否生成了冲突日志
        if [ -f "l10n/translation_conflicts.log" ] && [ -s "l10n/translation_conflicts.log" ]; then
            print_warning "发现翻译冲突，请检查 l10n/translation_conflicts.log 文件"
        fi
    else
        print_error "多语言翻译对比文件生成失败"
        exit 1
    fi
    
    # 完成总结
    print_step "执行完成"
    echo -e "${GREEN}"
    echo "🎉 L10n国际化工具执行完成！"
    echo ""
    echo "生成的文件："
    echo "  📄 l10n/localizable.json - 基准中文翻译文件"
    echo "  📊 l10n/translations_comparison.csv - 多语言翻译对比表格"
    echo ""
    echo "可选检查文件："
    if [ -f "l10n/suspicious_l10n.txt" ] && [ -s "l10n/suspicious_l10n.txt" ]; then
        echo "  ⚠️  l10n/suspicious_l10n.txt - 可疑翻译代码（需要人工检查）"
    fi
    if [ -f "l10n/translation_conflicts.log" ] && [ -s "l10n/translation_conflicts.log" ]; then
        echo "  ⚠️  l10n/translation_conflicts.log - 翻译冲突日志（需要人工审核）"
    fi
    echo -e "${NC}"
}

# 脚本入口
main "$@"
