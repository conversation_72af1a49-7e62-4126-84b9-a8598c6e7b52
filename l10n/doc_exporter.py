# -*- coding: utf-8 -*-

import requests
import time
import argparse
import sys
import os

# --- 配置区域 ---
# 请在此处填写您的飞书应用的 App ID 和 App Secret
# 警告：请勿将这些凭证公开分享。如果已泄露，请立即重置。
APP_ID = "cli_a65fcc556b781013"
APP_SECRET = "67CSuu7sFzCU3YM8ogBQkQbT20MYE2Yz"
# --- 配置区域结束 ---


class FeishuDocExporter:
    """
    一个用于通过飞书开放平台API导出并下载云文档，并可选择性更新配置表格的类。
    """
    BASE_URL = "https://open.feishu.cn/open-apis"

    def __init__(self, app_id, app_secret):
        self.app_id = app_id
        self.app_secret = app_secret
        self.tenant_access_token = None
        self.token_expire_time = 0

        if self.app_id == "在此处填写你的APP_ID" or self.app_secret == "在此处填写你的APP_SECRET":
            print("错误: 请在脚本的配置区域填写您的 APP_ID 和 APP_SECRET。")
            sys.exit(1)

    def get_tenant_access_token(self):
        """获取或刷新 tenant_access_token。"""
        if self.tenant_access_token and self.token_expire_time > time.time():
            return True
        print("正在获取 tenant_access_token...")
        url = f"{self.BASE_URL}/auth/v3/tenant_access_token/internal"
        headers = {"Content-Type": "application/json; charset=utf-8"}
        data = {"app_id": self.app_id, "app_secret": self.app_secret}
        try:
            response = requests.post(url, headers=headers, json=data, timeout=10)
            response.raise_for_status()
            result = response.json()
            if result.get("code") == 0:
                self.tenant_access_token = result["tenant_access_token"]
                self.token_expire_time = time.time() + (result.get("expire", 7200) - 120)
                print("成功获取 tenant_access_token。")
                return True
            else:
                print(f"获取 tenant_access_token 失败，服务器响应: {result}")
                return False
        except requests.RequestException as e:
            print(f"请求 tenant_access_token 异常: {e}")
            return False

    def _get_auth_headers(self):
        """获取带授权信息的请求头。"""
        if not self.get_tenant_access_token():
            raise Exception("无法获取 tenant_access_token，请求中断。")
        return {
            "Authorization": f"Bearer {self.tenant_access_token}",
            "Content-Type": "application/json; charset=utf-8"
        }

    def get_sheet_id_by_name(self, spreadsheet_token, sheet_name):
        """根据电子表格 token 和工作表名称查询并返回 sheet_id。"""
        print(f"\n正在为电子表格 (token: {spreadsheet_token}) 查询工作表 '{sheet_name}' 的 ID...")
        url = f"{self.BASE_URL}/sheets/v3/spreadsheets/{spreadsheet_token}/sheets/query"
        try:
            if not self.get_tenant_access_token(): return None
            headers = {"Authorization": f"Bearer {self.tenant_access_token}"}
            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status()
            result = response.json()
            if result.get("code") == 0:
                sheets = result.get("data", {}).get("sheets", [])
                for sheet in sheets:
                    if sheet.get("title") == sheet_name:
                        sheet_id = sheet.get("sheet_id")
                        print(f"查询成功！工作表 '{sheet_name}' 的 ID 是: {sheet_id}")
                        return sheet_id
                print(f"错误：在电子表格中未找到名为 '{sheet_name}' 的工作表。")
                return None
            else:
                print(f"查询工作表列表失败，服务器响应: {result}")
                return None
        except requests.exceptions.HTTPError as e:
            print(f"查询工作表列表时发生 HTTP 错误: {e}")
            print(f"服务器返回的详细错误: {e.response.text}")
            return None
        except Exception as e:
            print(f"查询工作表列表时发生未知异常: {e}")
            return None

    def write_sheet_data(self, spreadsheet_token, range_str, values):
        """向指定电子表格的指定范围写入数据。"""
        print(f"正在向电子表格 (token: {spreadsheet_token}) 的范围 {range_str} 写入数据...")
        url = f"{self.BASE_URL}/sheets/v2/spreadsheets/{spreadsheet_token}/values"
        payload = {"valueRange": {"range": range_str, "values": values}}
        try:
            headers = self._get_auth_headers()
            response = requests.put(url, headers=headers, json=payload, timeout=60)
            response.raise_for_status()
            result = response.json()
            if result.get("code") == 0:
                print("数据写入成功。")
                return True
            else:
                print(f"写入数据失败，服务器响应: {result}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"写入数据时发生异常: {e}")
            return False

    def create_export_task(self, file_token, export_type, sub_id=None, file_extension=None):
        """步骤一：创建导出任务"""
        print(f"\n步骤一：为文档 (token: {file_token}) 创建 '{export_type}' 类型的导出任务...")
        url = f"{self.BASE_URL}/drive/v1/export_tasks"
        payload = {"token": file_token, "type": export_type}
        if sub_id:
            payload["sub_id"] = sub_id
            print(f"  > 使用了子ID (sub_id): {sub_id}")
        if file_extension:
            payload["file_extension"] = file_extension
            print(f"  > 指定了文件扩展名: {file_extension}")
        try:
            headers = self._get_auth_headers()
            response = requests.post(url, headers=headers, json=payload, timeout=15)
            response.raise_for_status()
            result = response.json()
            if result.get("code") == 0:
                ticket = result.get("data", {}).get("ticket")
                if ticket:
                    print(f"导出任务创建成功，任务 Ticket: {ticket}")
                    return ticket
                else:
                    print(f"错误：任务创建成功，但在响应中未找到 ticket。详细响应: {result}")
                    return None
            else:
                print(f"创建导出任务失败，服务器响应: {result}")
                return None
        except requests.exceptions.HTTPError as e:
            print(f"创建导出任务时发生 HTTP 错误: {e}")
            print(f"服务器返回的详细错误: {e.response.text}")
            return None
        except Exception as e:
            print(f"创建导出任务时发生未知异常: {e}")
            return None

    def query_export_result(self, ticket, original_file_token, max_wait_seconds=300):
        """步骤二：查询导出任务结果"""
        print(f"\n步骤二：查询任务 (ticket: {ticket}) 的导出结果...")
        url = f"{self.BASE_URL}/drive/v1/export_tasks/{ticket}"
        params = {"token": original_file_token}
        start_time = time.time()
        
        status_messages = {
            0: "成功", 1: "初始化", 2: "处理中", 3: "内部错误", 107: "文档过大",
            108: "处理超时", 109: "无权限访问内容块", 110: "无权限", 111: "文档已删除",
            122: "创建副本中禁止导出", 123: "文档不存在", 6000: "图片过多导致导出失败"
        }
        
        while time.time() - start_time < max_wait_seconds:
            try:
                headers = self._get_auth_headers()
                response = requests.get(url, headers=headers, params=params, timeout=15)
                response.raise_for_status()
                result = response.json()
                
                if result.get("code") == 0:
                    data = result.get("data", {})
                    export_result = data.get("result", {})
                    job_status = export_result.get("job_status")
                    job_error_msg = export_result.get("job_error_msg", "")
                    
                    if job_status == 0:
                        file_token = export_result.get("file_token")
                        file_name = export_result.get("file_name")
                        file_size = export_result.get("file_size", 0)
                        print(f"导出成功！文件名: '{file_name}'，文件大小: {file_size} 字节")
                        return file_token
                    elif job_status in [1, 2]:
                        status_msg = status_messages.get(job_status, f"未知状态({job_status})")
                        print(f"任务{status_msg}，2秒后重试...")
                        time.sleep(2)
                    else:
                        status_msg = status_messages.get(job_status, f"未知错误状态({job_status})")
                        error_detail = f"：{job_error_msg}" if job_error_msg and job_error_msg != "success" else ""
                        print(f"导出任务失败 - {status_msg}{error_detail}")
                        return None
                else:
                    print(f"查询任务结果失败，服务器响应: {result}，2秒后重试...")
                    time.sleep(2)
            except requests.exceptions.HTTPError as e:
                print(f"查询任务结果时发生 HTTP 错误: {e}，2秒后重试...")
                print(f"服务器返回的详细错误: {e.response.text}")
                time.sleep(2)
            except Exception as e:
                print(f"查询任务结果时发生未知异常: {e}，2秒后重试...")
                time.sleep(2)
                
        print(f"查询超时（超过 {max_wait_seconds} 秒），导出失败。")
        return None

    def download_export_file(self, file_token, save_path):
        """步骤三：下载导出文件"""
        print(f"\n步骤三：正在从 (file_token: {file_token}) 下载文件...")
        url = f"{self.BASE_URL}/drive/v1/export_tasks/file/{file_token}/download"
        try:
            headers = {"Authorization": f"Bearer {self.tenant_access_token}"}
            with requests.get(url, headers=headers, stream=True, timeout=180) as r:
                r.raise_for_status()
                if os.path.dirname(save_path):
                    os.makedirs(os.path.dirname(save_path), exist_ok=True)
                with open(save_path, 'wb') as f:
                    for chunk in r.iter_content(chunk_size=8192):
                        f.write(chunk)
            print(f"文件下载成功，已保存至: {os.path.abspath(save_path)}")
            return True
        except requests.exceptions.HTTPError as e:
            print(f"下载文件时发生 HTTP 错误: {e}")
            print(f"服务器返回的详细错误: {e.response.text}")
            return False
        except Exception as e:
            print(f"下载文件时发生未知异常: {e}")
            return False

def run_script(doc_token, doc_type, save_path, sub_id_or_name=None, file_extension=None, config_value=None):
    """执行完整导出流程的主函数。"""
    print("--- 导出脚本开始执行 ---")

    exporter = FeishuDocExporter(APP_ID, APP_SECRET)

    # --- 更新配置表格逻辑 ---
    if config_value:
        print("\n--- 开始更新配置表格 ---")
        config_sheet_name = "配置表格"
        
        # 1. 获取配置表格的 sheet_id (使用主 doc_token)
        config_sheet_id = exporter.get_sheet_id_by_name(doc_token, config_sheet_name)
        if not config_sheet_id:
            print(f"错误：在电子表格 (token: {doc_token}) 中未找到名为 '{config_sheet_name}' 的工作表。")
            print("\n--- 脚本执行失败 ---")
            sys.exit(1) 

        # 2. 准备写入数据
        write_range = f"{config_sheet_id}!A2:A2"
        values_to_write = [[config_value]]

        # 3. 执行写入操作 (使用主 doc_token)
        if not exporter.write_sheet_data(doc_token, write_range, values_to_write):
            print("错误：更新配置表格失败。")
            print("\n--- 脚本执行失败 ---")
            sys.exit(1) 
        
        print("--- 配置表格更新成功 ---")
    # --- 逻辑结束 ---

    actual_sub_id = sub_id_or_name

    if doc_type == 'sheet' and sub_id_or_name:
        # 同样使用主 doc_token 来查找要导出的 sheet id
        sheet_id = exporter.get_sheet_id_by_name(doc_token, sub_id_or_name)
        if not sheet_id:
            print(f"\n--- 脚本执行失败：未能找到名为 '{sub_id_or_name}' 的工作表 ---")
            sys.exit(1)
        actual_sub_id = sheet_id

    ticket = exporter.create_export_task(doc_token, doc_type, actual_sub_id, file_extension)
    if not ticket:
        print("\n--- 脚本执行失败 ---")
        sys.exit(1)

    file_token = exporter.query_export_result(ticket, doc_token)
    if not file_token:
        print("\n--- 脚本执行失败 ---")
        sys.exit(1)

    success = exporter.download_export_file(file_token, save_path)
    if success:
        print("\n--- 脚本执行成功 ---")
    else:
        print("\n--- 脚本执行失败 ---")
        sys.exit(1)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="一个用于导出并下载飞书云文档的脚本。")
    parser.add_argument("-t", "--token", required=True, help="要导出的云文档的 file token (必需)。此 token 也将用于更新配置表。")
    parser.add_argument("-e", "--type", required=True, choices=['docx', 'pdf', 'sheet', 'bitable'], help="要导出的文件类型 (必需)。可选: docx, pdf, sheet, bitable")
    parser.add_argument("-o", "--output", required=True, help="文件保存的本地路径，包含文件名，例如 './mydoc.pdf' (必需)。")
    parser.add_argument("--sub-id", dest="sub_id_or_name", help="当导出类型为 'sheet' 时，可提供 sheetId 或工作表名称；为 'bitable' 时，需提供 tableId。")
    parser.add_argument("--ext", dest="file_extension", choices=['xlsx', 'csv'], help="当导出类型为 'sheet' 或 'bitable' 时，可指定导出格式 (默认为 xlsx)。")
    
    # --- 修改后的命令行参数 ---
    parser.add_argument("--config-value", help="[可选] 提供此参数可触发配置更新。脚本将在指定 token 的电子表格中查找'配置表格'并将其 A2 单元格更新为此处提供的值。")
    
    args = parser.parse_args()

    if args.type in ['sheet', 'bitable'] and not args.sub_id_or_name:
        print(f"错误：导出类型为 '{args.type}' 时，必须使用 '--sub-id' 参数提供子ID或名称。")
        sys.exit(1)
    if args.file_extension and args.type not in ['sheet', 'bitable']:
        print(f"错误：'--ext' 参数仅在导出类型为 'sheet' 或 'bitable' 时可用。")
        sys.exit(1)
    if os.path.isdir(args.output):
        print(f"错误：输出路径 '-o' 不能是一个目录，需要指定完整的文件名。")
        sys.exit(1)

    run_script(args.token, args.type, args.output, args.sub_id_or_name, args.file_extension, args.config_value)