#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境UI中文字符串扫描器
专门识别真正需要在生产环境中本地化的UI文本
排除预览函数、日志、测试代码等开发相关内容
"""

import os
import re
import json
from collections import defaultdict

# --- 配置 ---
PROJECT_ROOT = '.'
SCAN_DIRECTORIES = ['app/src/main/java']
OUTPUT_FILE = 'l10n/production_ui_strings.json'
REPORT_FILE = 'l10n/production_ui_strings_report.txt'

# --- 正则表达式 ---

# 匹配包含中文字符的字符串字面量
PATTERN_CHINESE_STRING = re.compile(r'"([^"]*[\u4e00-\u9fff][^"]*)"')

# 本地化方法调用
LOCALIZATION_METHODS = [
    r'\.localized\b',
    r'\.localizedFormat\b', 
    r'\.localizedWithKey\b',
    r'\.localizedFormatWithKey\b'
]

PATTERN_LOCALIZED_CALL = re.compile('|'.join(LOCALIZATION_METHODS))

# 匹配已经本地化的字符串 - 更精确的模式
PATTERN_STRING_WITH_L10N = re.compile(
    r'"([^"]*[\u4e00-\u9fff][^"]*)"' +
    r'\s*(?:\.trimIndent\(\))?\s*' +
    r'(?:' + '|'.join(LOCALIZATION_METHODS) + ')'
)

# 匹配本地化方法调用中的字符串参数
PATTERN_L10N_WITH_KEY = re.compile(
    r'(?:' + '|'.join(LOCALIZATION_METHODS) + r')\s*\(\s*"([^"]*[\u4e00-\u9fff][^"]*)"'
)

# 需要排除的文件路径
EXCLUDE_FILE_PATTERNS = [
    r'/demo/',
    r'/test/',
    r'/debug/',
    r'Test\.kt$',
    r'Demo\.kt$',
    r'Preview\.kt$',
    r'Sample\.kt$',
    r'Example\.kt$',
]

EXCLUDE_FILE_PATTERN = re.compile('|'.join(EXCLUDE_FILE_PATTERNS), re.IGNORECASE)


def is_excluded_file(file_path):
    """检查是否应该排除这个文件"""
    return EXCLUDE_FILE_PATTERN.search(file_path) is not None


def find_preview_function_ranges(lines):
    """找出文件中所有预览函数的行号范围"""
    preview_ranges = []

    i = 0
    while i < len(lines):
        line = lines[i].strip()

        # 查找@Preview注解
        if '@Preview' in line:
            # 向下查找对应的函数定义
            j = i + 1
            while j < len(lines) and j < i + 10:  # 最多向下查找10行
                func_line = lines[j].strip()

                # 找到预览函数定义
                if re.match(r'^\s*(private\s+)?fun\s+\w*[Pp]review.*\(\s*\)\s*\{?\s*$', func_line):
                    # 找到函数结束位置
                    brace_count = 0
                    start_line = j

                    # 如果函数定义行没有开括号，继续查找
                    if '{' not in func_line:
                        j += 1
                        while j < len(lines) and '{' not in lines[j]:
                            j += 1

                    # 从函数开始计算括号匹配
                    for k in range(j, len(lines)):
                        line_content = lines[k]
                        brace_count += line_content.count('{')
                        brace_count -= line_content.count('}')

                        if brace_count == 0 and '{' in ''.join(lines[j:k+1]):
                            preview_ranges.append((start_line, k))
                            break
                    break
                j += 1
        i += 1

    return preview_ranges


def find_companion_object_ranges(lines):
    """找出文件中所有companion object的行号范围"""
    companion_ranges = []

    i = 0
    while i < len(lines):
        line = lines[i].strip()

        # 查找companion object
        if re.match(r'^\s*companion\s+object', line, re.IGNORECASE):
            start_line = i
            brace_count = 0
            found_opening_brace = False

            # 从companion object开始计算括号匹配
            for j in range(i, len(lines)):
                line_content = lines[j]
                brace_count += line_content.count('{')
                brace_count -= line_content.count('}')

                if '{' in line_content:
                    found_opening_brace = True

                if found_opening_brace and brace_count == 0:
                    companion_ranges.append((start_line, j))
                    break
        i += 1

    return companion_ranges


def is_in_preview_function(line_index, preview_ranges):
    """检查指定行是否在预览函数内部"""
    for start, end in preview_ranges:
        if start <= line_index <= end:
            return True
    return False


def is_in_companion_object(line_index, companion_ranges):
    """检查指定行是否在companion object内部"""
    for start, end in companion_ranges:
        if start <= line_index <= end:
            return True
    return False


def is_log_statement(line):
    """检查是否是日志语句"""
    log_patterns = [
        r'Log\.[deiw]\s*\(',           # Android Log
        r'LogUtils\.[deiw]\s*\(',      # 自定义LogUtils
        r'LogUtils\.[deiw]Tag\s*\(',   # 自定义LogUtils带Tag
        r'Logger\.',                   # Logger类
        r'println\s*\(',               # println
        r'print\s*\(',                 # print
        r'System\.out\.print',         # System.out.print
        r'\.log\s*\(',                 # 通用log方法
        r'console\.log',               # console.log
        r'Timber\.',                   # Timber日志库
        r'logE\s*\(',                  # 自定义logE方法
        r'logD\s*\(',                  # 自定义logD方法
        r'logI\s*\(',                  # 自定义logI方法
        r'logW\s*\(',                  # 自定义logW方法
    ]

    for pattern in log_patterns:
        if re.search(pattern, line, re.IGNORECASE):
            return True

    return False


def is_in_comment(line):
    """检查字符串是否在注释中"""
    # 检查单行注释
    comment_start = line.find('//')
    if comment_start != -1:
        # 检查字符串是否在注释部分
        quote_positions = []
        i = 0
        while i < len(line):
            if line[i] == '"' and (i == 0 or line[i-1] != '\\'):
                quote_positions.append(i)
            i += 1

        # 如果有引号且都在注释后面，说明字符串在注释中
        if quote_positions and all(pos > comment_start for pos in quote_positions):
            return True

    return False


def is_accessibility_description(line):
    """检查是否是辅助功能描述"""
    accessibility_patterns = [
        r'contentDescription\s*=',
        r'semantics\s*\{.*contentDescription',
        r'\.semantics\s*\{',
    ]

    for pattern in accessibility_patterns:
        if re.search(pattern, line, re.IGNORECASE):
            return True

    return False


def is_debug_or_test_context(line):
    """检查是否是调试或测试上下文"""
    debug_patterns = [
        r'BuildConfig\.DEBUG',
        r'if\s*\(\s*DEBUG',
        r'\.debug\(',
        r'\.test\(',
        r'@Test',
        r'@Before',
        r'@After',
        r'assertEquals',
        r'assertTrue',
        r'assertFalse',
        r'MockK',
        r'Mockito',
        r'throw\s+\w*Exception',        # 异常抛出
        r'IllegalArgumentException',    # 非法参数异常
        r'IllegalStateException',       # 非法状态异常
        r'RuntimeException',            # 运行时异常
        r'Exception\s*\(',              # 异常构造
    ]

    for pattern in debug_patterns:
        if re.search(pattern, line, re.IGNORECASE):
            return True

    return False


def is_data_model_or_config(line, context_lines):
    """检查是否是数据模型或配置文件中的示例数据"""
    # 检查是否在数据类、配置对象或companion object中
    in_companion_object = False
    in_data_class = False
    in_preview_context = False

    for ctx_line in context_lines:
        ctx_stripped = ctx_line.strip()

        # 检查是否在companion object中
        if re.search(r'companion\s+object', ctx_stripped, re.IGNORECASE):
            in_companion_object = True

        # 检查是否在数据类中
        if re.search(r'data\s+class', ctx_stripped, re.IGNORECASE):
            in_data_class = True

        # 检查是否有preview相关的命名
        if re.search(r'(preview|sample|mock|test|demo|example)', ctx_stripped, re.IGNORECASE):
            in_preview_context = True

        # 检查是否是对象构造调用
        if re.search(r'\w+\s*\(', ctx_stripped):
            # 如果在companion object中且是构造调用，很可能是示例数据
            if in_companion_object:
                in_preview_context = True

    # 如果是简单的属性赋值
    line_stripped = line.strip()
    if re.match(r'^\s*\w+\s*=\s*"[^"]*"[,}]?\s*$', line_stripped):
        # 在以下情况下认为是示例数据
        if in_companion_object or in_preview_context or in_data_class:
            return True

        # 检查属性名是否暗示是示例数据
        property_match = re.match(r'^\s*(\w+)\s*=', line_stripped)
        if property_match:
            property_name = property_match.group(1).lower()
            if any(keyword in property_name for keyword in ['preview', 'sample', 'mock', 'test', 'demo', 'example']):
                return True

    return False


def is_production_ui_text(text, line, context_lines):
    """判断是否是生产环境需要的UI文本"""
    # 基本过滤
    if len(text.strip()) < 2:
        return False
    
    # 排除纯英文数字符号
    if not re.search(r'[\u4e00-\u9fff]', text):
        return False
    
    # 排除包含代码特征的文本
    code_patterns = [
        r'^\w+\.\w+',           # 类似 object.property
        r'[{}()\[\]]',          # 包含代码符号
        r'^\d+$',               # 纯数字
        r'^[a-zA-Z_]\w*$',      # 纯变量名
        r'http[s]?://',         # URL
        r'\.(com|org|net)',     # 域名
        r'yyyy|MM|dd|HH|mm|ss', # 日期格式
        r'%[sd]',               # 格式化占位符
    ]
    
    for pattern in code_patterns:
        if re.search(pattern, text):
            return False
    
    # 检查是否是数据模型中的示例数据
    if is_data_model_or_config(line, context_lines):
        return False
    
    # 强UI指示器 - 明确的UI组件调用
    strong_ui_indicators = [
        r'Text\s*\(',
        r'Button\s*\(',
        r'TextField\s*\(',
        r'OutlinedTextField\s*\(',
        r'AlertDialog\s*\(',
        r'Toast\.makeText',
        r'Snackbar\.',
        r'Dialog\s*\(',
    ]
    
    for indicator in strong_ui_indicators:
        if re.search(indicator, line):
            return True
    
    # UI属性赋值
    ui_property_patterns = [
        r'\btext\s*=\s*"[^"]*' + re.escape(text),
        r'\btitle\s*=\s*"[^"]*' + re.escape(text),
        r'\blabel\s*=\s*"[^"]*' + re.escape(text),
        r'\bhint\s*=\s*"[^"]*' + re.escape(text),
        r'\bmessage\s*=\s*"[^"]*' + re.escape(text),
        r'\bplaceholder\s*=\s*"[^"]*' + re.escape(text),
    ]
    
    for pattern in ui_property_patterns:
        if re.search(pattern, line):
            return True
    
    # 常见UI文本关键词
    ui_keywords = [
        '确认', '取消', '保存', '删除', '编辑', '设置', '登录', '注册', '退出',
        '提交', '重置', '刷新', '加载', '搜索', '完成', '跳过', '继续', '返回',
        '成功', '失败', '错误', '提示', '关闭', '打开', '选择', '上传', '下载',
        '发送', '接收', '分享', '收藏', '点赞', '评论', '关注', '私信',
        '充值', '支付', '购买', '订单', '退款', '密码', '验证', '绑定',
    ]
    
    # 如果包含UI关键词且长度合理，可能是UI文本
    if any(keyword in text for keyword in ui_keywords) and 2 <= len(text) <= 30:
        return True
    
    # 用户交互相关的文本
    user_interaction_patterns = [
        r'请.*',      # 请开头的提示
        r'.*失败',    # 失败结尾的错误信息
        r'.*成功',    # 成功结尾的成功信息
        r'.*中\.\.\.', # 进行中的状态
    ]
    
    for pattern in user_interaction_patterns:
        if re.match(pattern, text) and len(text) <= 50:
            return True
    
    return False


def find_unused_methods(lines, content):
    """找出可能未被使用的方法"""
    unused_methods = set()

    # 找出所有方法定义
    method_pattern = r'fun\s+(\w+)\s*\('
    for match in re.finditer(method_pattern, content):
        method_name = match.group(1)

        # 检查方法是否被调用（简单检查）
        call_pattern = rf'\b{method_name}\s*\('
        calls = re.findall(call_pattern, content)

        # 如果只有一次匹配（就是定义本身），可能未被使用
        if len(calls) <= 1:
            unused_methods.add(method_name)

    return unused_methods


def is_in_unused_method(line_index, lines, unused_methods):
    """检查指定行是否在未使用的方法中"""
    # 向上查找方法定义
    for i in range(line_index, max(0, line_index - 20), -1):
        line = lines[i].strip()
        method_match = re.match(r'.*fun\s+(\w+)\s*\(', line)
        if method_match:
            method_name = method_match.group(1)
            if method_name in unused_methods:
                return True
            break

    return False


def extract_production_ui_strings_from_file(file_path):
    """从单个文件中提取生产环境需要的UI中文字符串"""
    ui_strings = []

    if is_excluded_file(file_path):
        return ui_strings
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # 找出预览函数的范围
        preview_ranges = find_preview_function_ranges(lines)

        # 找出companion object的范围
        companion_ranges = find_companion_object_ranges(lines)

        # 找出可能未被使用的方法
        unused_methods = find_unused_methods(lines, content)

        # 找出已经本地化的字符串
        localized_strings = set()

        # 匹配直接调用本地化方法的字符串
        for match in PATTERN_STRING_WITH_L10N.finditer(content):
            localized_strings.add(match.group(1))

        # 匹配本地化方法参数中的字符串
        for match in PATTERN_L10N_WITH_KEY.finditer(content):
            localized_strings.add(match.group(1))

        # 扫描每一行
        for i, line in enumerate(lines):
            line_num = i + 1

            # 跳过预览函数内的内容
            if is_in_preview_function(i, preview_ranges):
                continue

            # 跳过companion object中的预览数据
            if is_in_companion_object(i, companion_ranges):
                # 检查是否是预览相关的数据
                context_start = max(0, i - 10)
                context_end = min(len(lines), i + 5)
                context_lines = lines[context_start:context_end]

                # 如果在companion object中且包含preview相关内容，跳过
                has_preview_context = any(
                    re.search(r'(preview|sample|mock|demo|example)', ctx_line, re.IGNORECASE)
                    for ctx_line in context_lines
                )
                if has_preview_context:
                    continue
            
            # 跳过日志语句
            if is_log_statement(line):
                continue

            # 跳过调试和测试相关
            if is_debug_or_test_context(line):
                continue

            # 跳过注释中的字符串
            if is_in_comment(line):
                continue

            # 跳过辅助功能描述
            if is_accessibility_description(line):
                continue

            # 跳过注释
            stripped = line.strip()
            if not stripped or stripped.startswith('//') or stripped.startswith('*'):
                continue

            # 跳过可能未被使用的方法中的字符串
            if is_in_unused_method(i, lines, unused_methods):
                continue

            # 查找包含中文的字符串
            for match in PATTERN_CHINESE_STRING.finditer(line):
                chinese_text = match.group(1)

                # 跳过已经本地化的字符串
                if chinese_text in localized_strings:
                    continue

                # 检查同一行是否有本地化调用
                line_after_string = line[match.end():]
                if PATTERN_LOCALIZED_CALL.search(line_after_string):
                    continue
                
                # 获取上下文（前后5行）
                context_start = max(0, i - 5)
                context_end = min(len(lines), i + 6)
                context_lines = lines[context_start:context_end]
                
                # 判断是否是生产环境UI文本
                if is_production_ui_text(chinese_text, line, context_lines):
                    # 判断置信度
                    confidence = 'high'
                    
                    # 降低置信度的情况
                    if any(keyword in file_path.lower() for keyword in ['test', 'demo', 'sample', 'example']):
                        confidence = 'medium'
                    
                    if any(keyword in line.lower() for keyword in ['todo', 'fixme', 'test', 'debug']):
                        confidence = 'medium'
                    
                    ui_strings.append({
                        'file': file_path,
                        'line': line_num,
                        'text': chinese_text,
                        'context': line.strip(),
                        'confidence': confidence,
                        'type': 'production_ui'
                    })
    
    except Exception as e:
        print(f"❌ 读取文件时出错: {file_path}, 错误: {e}")

    return ui_strings


def scan_project_for_production_ui_strings(root_path, directories):
    """扫描项目中生产环境需要的UI字符串"""
    all_ui_strings = []
    file_count = 0

    print("\n🎯 开始扫描生产环境UI中文字符串...")
    print("📋 排除内容：预览函数、日志语句、测试代码、调试代码、示例数据")

    for directory in directories:
        scan_path = os.path.join(root_path, directory)
        if not os.path.exists(scan_path):
            print(f"⚠️  目录不存在: {scan_path}")
            continue

        print(f"📁 正在扫描目录: {scan_path}")

        for dirpath, _, filenames in os.walk(scan_path):
            for filename in filenames:
                if filename.endswith(('.kt', '.java')):
                    file_path = os.path.join(dirpath, filename)
                    relative_path = os.path.relpath(file_path, root_path)

                    ui_strings = extract_production_ui_strings_from_file(file_path)
                    if ui_strings:
                        all_ui_strings.extend(ui_strings)
                        high_confidence = len([s for s in ui_strings if s['confidence'] == 'high'])
                        print(f"  📄 {relative_path}: 发现 {len(ui_strings)} 个生产UI字符串 (高置信度: {high_confidence})")

                    file_count += 1

    print(f"\n✅ 扫描完成！")
    print(f"📊 总共扫描了 {file_count} 个文件")
    print(f"🎯 发现 {len(all_ui_strings)} 个生产环境UI字符串")

    # 按置信度分类统计
    high_confidence = len([s for s in all_ui_strings if s['confidence'] == 'high'])
    medium_confidence = len([s for s in all_ui_strings if s['confidence'] == 'medium'])

    print(f"   - 🔴 高置信度: {high_confidence} 个")
    print(f"   - 🟡 中等置信度: {medium_confidence} 个")

    return all_ui_strings


def generate_production_report_files(ui_strings, json_output_file, report_output_file):
    """生成生产环境UI字符串报告文件"""
    if not ui_strings:
        print("👍 没有发现需要本地化的生产环境UI字符串！")
        return

    # 按文件和置信度分组
    strings_by_file = defaultdict(lambda: {'high': [], 'medium': []})
    for item in ui_strings:
        strings_by_file[item['file']][item['confidence']].append(item)

    # 生成JSON文件
    try:
        json_data = {
            'summary': {
                'total_files': len(strings_by_file),
                'total_strings': len(ui_strings),
                'high_confidence': len([s for s in ui_strings if s['confidence'] == 'high']),
                'medium_confidence': len([s for s in ui_strings if s['confidence'] == 'medium']),
                'scan_directories': SCAN_DIRECTORIES,
                'excluded_content': [
                    '预览函数 (@Preview)',
                    '日志语句 (Log.*, LogUtils.*, println等)',
                    '测试和调试代码',
                    '数据模型示例数据',
                    '注释内容'
                ]
            },
            'strings_by_file': {}
        }

        for file_path, confidence_groups in strings_by_file.items():
            relative_path = os.path.relpath(file_path, PROJECT_ROOT)
            json_data['strings_by_file'][relative_path] = {
                'high_confidence': [
                    {
                        'line': s['line'],
                        'text': s['text'],
                        'context': s['context']
                    }
                    for s in confidence_groups['high']
                ],
                'medium_confidence': [
                    {
                        'line': s['line'],
                        'text': s['text'],
                        'context': s['context']
                    }
                    for s in confidence_groups['medium']
                ]
            }

        with open(json_output_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2, sort_keys=True)

        print(f"📄 JSON报告已保存到: {json_output_file}")

    except Exception as e:
        print(f"❌ 生成JSON文件时出错: {e}")

    # 生成文本报告
    try:
        with open(report_output_file, 'w', encoding='utf-8') as f:
            f.write("# 生产环境UI中文字符串报告\n")
            f.write("=" * 50 + "\n\n")
            f.write("## 扫描说明\n")
            f.write("本报告专注于识别生产环境中真正需要本地化的UI文本\n\n")
            f.write("### 已排除的内容：\n")
            f.write("- 🚫 预览函数 (@Preview 注解的函数)\n")
            f.write("- 🚫 日志语句 (Log.*, LogUtils.*, println等)\n")
            f.write("- 🚫 测试和调试相关代码\n")
            f.write("- 🚫 数据模型中的示例数据\n")
            f.write("- 🚫 注释内容\n\n")
            f.write(f"扫描目录: {', '.join(SCAN_DIRECTORIES)}\n")
            f.write(f"总文件数: {len(strings_by_file)}\n")
            f.write(f"总字符串数: {len(ui_strings)}\n")
            f.write(f"🔴 高置信度: {len([s for s in ui_strings if s['confidence'] == 'high'])}\n")
            f.write(f"🟡 中等置信度: {len([s for s in ui_strings if s['confidence'] == 'medium'])}\n\n")

            for file_path in sorted(strings_by_file.keys()):
                relative_path = os.path.relpath(file_path, PROJECT_ROOT)
                confidence_groups = strings_by_file[file_path]

                total_strings = len(confidence_groups['high']) + len(confidence_groups['medium'])
                f.write(f"## {relative_path}\n")
                f.write(f"发现 {total_strings} 个生产环境UI字符串:\n\n")

                # 先显示高置信度的
                if confidence_groups['high']:
                    f.write("### 🔴 高置信度 (强烈建议本地化)\n\n")
                    for s in sorted(confidence_groups['high'], key=lambda x: x['line']):
                        f.write(f"**第 {s['line']} 行**:\n")
                        f.write(f"```kotlin\n{s['context']}\n```\n")
                        f.write(f"中文内容: `{s['text']}`\n\n")

                # 再显示中等置信度的
                if confidence_groups['medium']:
                    f.write("### 🟡 中等置信度 (建议检查)\n\n")
                    for s in sorted(confidence_groups['medium'], key=lambda x: x['line']):
                        f.write(f"**第 {s['line']} 行**:\n")
                        f.write(f"```kotlin\n{s['context']}\n```\n")
                        f.write(f"中文内容: `{s['text']}`\n\n")

                f.write("-" * 40 + "\n\n")

        print(f"📄 文本报告已保存到: {report_output_file}")

    except Exception as e:
        print(f"❌ 生成文本报告时出错: {e}")

def main():
    ui_strings = scan_project_for_production_ui_strings(PROJECT_ROOT, SCAN_DIRECTORIES)
    generate_production_report_files(ui_strings, OUTPUT_FILE, REPORT_FILE)

if __name__ == '__main__':
    main()
