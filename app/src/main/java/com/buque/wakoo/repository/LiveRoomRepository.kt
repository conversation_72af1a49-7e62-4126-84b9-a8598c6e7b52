package com.buque.wakoo.repository

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableIntState
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.mutableStateSetOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshots.Snapshot
import com.buque.wakoo.app.Const
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.app.createSafeCoroutineScope
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.bean.LiveRoomExtraInfo
import com.buque.wakoo.bean.PrivateRoomBasicInfo
import com.buque.wakoo.bean.PrivateRoomInteractStatus
import com.buque.wakoo.bean.RoomOnlineInfo
import com.buque.wakoo.bean.RoomRtcToken
import com.buque.wakoo.bean.VoiceCallNotice
import com.buque.wakoo.bean.message.BBoxInfo
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.ext.getStringOrNull
import com.buque.wakoo.ext.isNetworkException
import com.buque.wakoo.ext.keepLastNonNullState
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.inter.MsgFilter
import com.buque.wakoo.im.utils.takeIsNotBlank
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.manager.UserManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.network.api.bean.InteractStatus
import com.buque.wakoo.network.api.bean.PKEvent
import com.buque.wakoo.network.api.bean.PkInfo
import com.buque.wakoo.network.api.bean.PrivateRoomInfoResponse
import com.buque.wakoo.network.api.bean.RoomInfoResponse
import com.buque.wakoo.network.api.bean.Seat
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.network.api.bean.VoiceTag
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.network.api.service.VoiceRoomApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.network.onBusinessFailure
import com.buque.wakoo.rtc.AppRtcHelper
import com.buque.wakoo.rtc.MutableMicState
import com.buque.wakoo.rtc.TencentRtcFactory
import com.buque.wakoo.ui.dialog.AnyPopDialogProperties
import com.buque.wakoo.ui.dialog.DirectionState
import com.buque.wakoo.ui.dialog.PKResultDialog
import com.buque.wakoo.ui.dialog.PrivateRoomJoinTip
import com.buque.wakoo.ui.screens.liveroom.LiveMicMode
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import com.buque.wakoo.ui.screens.liveroom.MicSeatsInfo
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.RoomMember
import com.buque.wakoo.ui.screens.liveroom.RoomUser
import com.buque.wakoo.ui.screens.liveroom.toRoomUser
import com.buque.wakoo.utils.DateTimeUtils
import com.buque.wakoo.utils.eventBus.EventBus
import com.buque.wakoo.utils.eventBus.tryEasyPostDialog
import com.tencent.imsdk.v2.V2TIMGroupMemberInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.json.jsonPrimitive

private data class StateScope(
    val basicInfoState: MutableState<BasicRoomInfo>,
    val onlineCountState: MutableIntState,
    val onlinePreviewState: MutableList<User>,
    val micListState: MutableList<MicSeatsInfo>,
    val adminIdsState: MutableSet<String>,
    val blackIdsState: MutableSet<String>,
    val extraInfoState: MutableState<LiveRoomExtraInfo>,
    val allUserMapState: MutableMap<String, RoomMember>,
) {
    val basicInfo: BasicRoomInfo
        get() = basicInfoState.value

    val onlineCount: Int
        get() = onlineCountState.intValue

    val onlinePreviewList: List<User>
        get() = onlinePreviewState

    // 麦位列表
    val micList: List<MicSeatsInfo>
        get() = micListState

    val adminIds: Set<String>
        get() = adminIdsState

    val blackIds: Set<String>
        get() = blackIdsState

    val extraInfo: LiveRoomExtraInfo
        get() = extraInfoState.value

    val allUserMap: Map<String, RoomMember>
        get() = allUserMapState
}

private class RoomMutableState(
    basicInfo: BasicRoomInfo,
    repository: LiveRoomRepository,
) {
    // 基本信息
    val basicInfoState: MutableState<BasicRoomInfo> = mutableStateOf(basicInfo)

    @Volatile
    var onlineHappenTimestamp = 0L

    // 在线房间人数(包括房主)
    val onlineCountState = mutableIntStateOf(if (SelfUser != null) 1 else 0)

    // 目前显示最多3个人
    val onlinePreviewState =
        mutableStateListOf<User>().apply {
            SelfUser?.also {
                add(it.user)
            }
        }

    // 麦位列表
    val micListState: MutableList<MicSeatsInfo> =
        mutableStateListOf<MicSeatsInfo>().apply {
            addAll(List(basicInfo.micSeatCount) { MicSeatsInfo.Empty(if (basicInfo.roomMode == LiveRoomMode.PKMode) it else it + 1) })
        }

    // 房间管理员列表Id
    val adminIdsState: MutableSet<String> = mutableStateSetOf("")

    // 黑名单列表Id
    val blackIdsState: MutableSet<String> = mutableStateSetOf("")

    // 额外信息
    val extraInfoState: MutableState<LiveRoomExtraInfo> = mutableStateOf(LiveRoomExtraInfo())

    // 计划作为房间所有需要感知用户信息变化ui组件的唯一数据源，包括（麦位，部分观众，房主），以及一些可能离开房间的引用计数不为0的用户
    // 要时刻保证与micListState，owner数据同步
    // 理想情况下一个用户的数据源只有一个
    val allUserMapState: MutableMap<String, RoomMember> = mutableStateMapOf<String, RoomMember>()

    val loadingState: MutableState<Boolean> = mutableStateOf(true)

    val basicInfo: BasicRoomInfo
        get() = basicInfoState.value

    val onlineCount: Int
        get() = onlineCountState.intValue

    val onlinePreviewList: List<User>
        get() = onlinePreviewState

    @Volatile
    var micListHappenTimestamp = 0L

    // 麦位列表
    val micList: List<MicSeatsInfo>
        get() = micListState

    val adminIds: Set<String>
        get() = adminIdsState

    val blackIds: Set<String>
        get() = blackIdsState

    val extraInfo: LiveRoomExtraInfo
        get() = extraInfoState.value

    val loading: Boolean
        get() = loadingState.value

    val allUserMap: Map<String, RoomMember>
        get() = allUserMapState

    val roomInfoState =
        LiveRoomInfoState(
            basicInfoState = basicInfoState,
            onlineInfo = RoomOnlineInfo(onlineCountState, onlinePreviewList),
            allUserMapState = allUserMapState,
            micListState = micListState,
            adminIdsState = adminIdsState,
            blackIdsState = blackIdsState,
            extraInfoState = extraInfoState,
            loadingState = loadingState,
            repository = repository,
        )

    inline fun <R> withMutableSnapshot(block: StateScope.() -> R): R =
        Snapshot.withMutableSnapshot {
            StateScope(
                basicInfoState = basicInfoState,
                onlineCountState = onlineCountState,
                onlinePreviewState = onlinePreviewState,
                allUserMapState = allUserMapState,
                micListState = micListState,
                adminIdsState = adminIdsState,
                blackIdsState = blackIdsState,
                extraInfoState = extraInfoState,
            ).run {
                block()
            }
        }
}

/**
 * 共享于[LiveRoomManager]和[com.buque.wakoo.viewmodel.liveroom.LiveRoomViewModel]
 * 直播房逻辑，收起后还需要运行
 */
class LiveRoomRepository(
    basicInfo: BasicRoomInfo,
    onExitRoom: (String, String?) -> Unit,
) : IMCompatListener {
    val roomId = basicInfo.id

    val autoUpMic = basicInfo.autoUpMic

    private val isPrivateRoom = basicInfo.roomMode.isPrivateRoom

    private val onExitRoom: (String?) -> Unit = {
        // toast msg
        onExitRoom(roomId, it)
    }

    private val limitedDispatcher = Dispatchers.Default.limitedParallelism(1)

    private val coroutineScope = createSafeCoroutineScope(limitedDispatcher + SupervisorJob())

    private val mutableState = RoomMutableState(basicInfo, this)

    val roomInfoState = mutableState.roomInfoState

    @Volatile
    private var released = false

    private val createTimestamp = DateTimeUtils.elapsedRealtime()

    private val eventChannel =
        MutableSharedFlow<RoomEvent>(
            extraBufferCapacity = 100,
            onBufferOverflow = BufferOverflow.DROP_OLDEST,
        )

    private var unlockTimestamp = -1L

    val events: Flow<RoomEvent> = eventChannel.asSharedFlow()

    private var rtcHelper: AppRtcHelper? = null

    private var blindBoxLink = ""
    private val _bBoxInfoState = mutableStateOf<BBoxInfo?>(null)
    val blindBoxInfoState: State<BBoxInfo?> = _bBoxInfoState

    init {
        // 默认最多两人，自己和房主
        mutableState.withMutableSnapshot {
            val selfId =
                SelfUser
                    ?.also {
                        allUserMapState.addMemberRoomUser(
                            it.id,
                            RoomMember(
                                roomUser = it.toRoomUser(),
                                inRoom = false,
                            ),
                        )
                    }?.id

            val owner = basicInfo.owner
            if (owner != null && selfId != owner.id) {
                allUserMapState.addMemberRoomUser(
                    owner.id,
                    RoomMember(
                        roomUser = owner,
                        inRoom = false,
                    ),
                )
            }
        }
        // 加入语音房，连接rtc，连接im
        joinRoom()

        events
            .onEach {
                handleEvent(it)
            }.launchIn(coroutineScope)

        IMCompatCore.addIMListener(this)
    }

    override val filter: MsgFilter = MsgFilter(basicInfo.imId)

    fun restoreBlindBoxInfo(pair: Pair<Long, BBoxInfo>) {
        _bBoxInfoState.value =
            pair.second.copy(remainSeconds = pair.second.remainSeconds - (System.currentTimeMillis() - pair.first) / 1000)
    }

    /**
     * @param onlyExit 是否是单纯的退出，还有的情况是加入一个新房间，退出上一个房间
     */
    fun release(
        reason: String?,
        destroyRtc: Boolean,
    ) {
        if (released) {
            return
        }
        released = true
        showToast(reason)
        IMCompatCore.removeIMListener(this)
        coroutineScope.launch(NonCancellable) {
            rtcHelper?.levelChannel(destroyRtc)
            IMCompatCore.quitConversation(mutableState.basicInfo.imId, ConversationType.CHATROOM, true)
        }
        coroutineScope.cancel()
    }

    fun setAudioCaptureVolume(volume: Int) {
        if (released) {
            return
        }
        coroutineScope.launch {
            rtcHelper?.setAudioCaptureVolume(volume)
        }
    }

    fun muteAllRemoteAudio(muted: Boolean) {
        if (released) {
            return
        }
        coroutineScope.launch {
            rtcHelper?.muteAllRemoteAudio(muted)
        }
    }

    fun restoreRtcSettings() {
        if (released) {
            return
        }
        coroutineScope.launch {
            setAudioCaptureVolume(120)
            muteAllRemoteAudio(false)
        }
    }

    fun sendEvent(event: RoomEvent) {
        coroutineScope.launch {
            eventChannel.emit(event)
        }
    }

    fun getMicStateById(userId: String): MutableMicState? = rtcHelper?.getMicStateById(userId)

    //
    fun requireRoomUser(user: User): RoomUser = mutableState.allUserMap[user.id]?.roomUser ?: user.toRoomUser()

    @Composable
    fun trackRoomMember(
        user: User,
        needFetchLatest: Boolean = true,
    ) = (
        keepLastNonNullState(mutableState.allUserMap[user.id]) ?: remember(user.id) {
            RoomMember(
                roomUser = user.toRoomUser(),
                inRoom = false,
            )
        }
    ).also {
        if (needFetchLatest) {
            LaunchedEffect(user.id) {
                refreshRoomUserInfo(user.id)
            }
        }
    }

    fun getBackToLiveRoomId(): String? {
        if (!isPrivateRoom) {
            return null
        }
        return roomInfoState.extraInfo.privateRoomInfo
            ?.takeIf {
                it.fromSceneType == 2 && it.fromSceneId != 0
            }?.fromSceneId
            .toString()
    }

    private fun handleMemberChangeEvent(
        isAdd: Boolean,
        roomUser: RoomUser,
        audienceCount: Int,
        previewList: List<BasicUser>,
    ) {
        if (roomUser.sIsSelf) {
            return
        }
        if (isPrivateRoom) {
            // 只关心对方是否离开
            mutableState.extraInfo.privateRoomInfo
                ?.takeIf {
                    it.targetUser.id == roomUser.id
                }?.apply {
                    mutableState.extraInfoState.value =
                        mutableState.extraInfo.copy(
                            privateRoomInfo = copy(targetInRoom = isAdd),
                        )
                }
            return
        }

        mutableState.withMutableSnapshot {
            onlineCountState.intValue = audienceCount
            onlinePreviewState.clear()
            onlinePreviewState.addAll(previewList)
            val id = roomUser.id
            if (isAdd) {
                allUserMapState.addMemberRoomUser(
                    id,
                    RoomMember(
                        roomUser = roomUser,
                        inRoom = true,
                    ),
                )
            } else {
                if (id != basicInfo.ownerId) {
                    allUserMapState.remove(id)
                }
            }
        }
    }

    private fun StateScope.handleMicSeatsChange(
        seats: List<Seat>,
        updateAllUserMap: Boolean = true,
    ) {
        val micSeatCount = basicInfo.micSeatCount

        var inMicList = false

        val offset = if (basicInfo.roomMode == LiveRoomMode.PKMode) 0 else 1

        val tempList =
            buildList {
                seats.forEachIndexed { index, it ->
                    add(
                        if (it.hasUser && it.user != null) {
                            val roomUser = RoomUser.fromResponse(it.user)
                            if (updateAllUserMap) {
                                if (!allUserMapState.contains(roomUser.id)) { // 没有这个用户需要补充, 要保证allUserMapState一定有麦上用户
                                    allUserMapState.addMemberRoomUser(
                                        roomUser.id,
                                        RoomMember(
                                            roomUser = roomUser,
                                            inRoom = true,
                                        ),
                                    )
                                }
                            }
                            if (!inMicList && roomUser.id == SelfUser?.id) {
                                inMicList = true
                            }
                            MicSeatsInfo.User(index + offset, roomUser, it.heartValue ?: 0)
                        } else {
                            MicSeatsInfo.Empty(index + offset)
                        },
                    )
                    if (size >= micSeatCount) {
                        return@buildList
                    }
                }

                val count = size
                repeat(micSeatCount - count) {
                    add(MicSeatsInfo.Empty(count + it + offset))
                }
            }

        micListState.clear()
        micListState.addAll(tempList)

        if (inMicList) {
            rtcHelper?.upMic(null)
        } else {
            rtcHelper?.downMic(null)
        }
    }

    private fun updateRoomInfo(infoResp: RoomInfoResponse) {
        mutableState.withMutableSnapshot {
            basicInfoState.value =
                basicInfo.run {
                    copy(
                        publicId = infoResp.publicId,
                        title = infoResp.title,
                        owner = RoomUser.fromResponse(infoResp.owner),
                        roomMode = LiveRoomMode.valueOf(infoResp.roomMode),
                        micMode = LiveMicMode.valueOf(infoResp.occupyMicMode),
                        desc = infoResp.desc?.takeIf { it.isNotBlank() },
                        notice = infoResp.notice?.takeIf { it.isNotBlank() },
                        background = infoResp.roomBackground.takeIf { it.isNotBlank() },
                        tagIds =
                            infoResp.tags?.map {
                                it.id
                            },
                    )
                }

            if (basicInfo.roomMode.isUnKnown) {
                onExitRoom("当前版本不支持此房间模式，已退出".localized)
                return
            }

            if (infoResp.happenTimestamp > mutableState.micListHappenTimestamp) {
                mutableState.micListHappenTimestamp = infoResp.happenTimestamp
                handleMicSeatsChange(infoResp.seats, false)
            }

            val tempMap =
                buildMap {
                    infoResp.audiences.forEach {
                        val uid = it.id
                        val exist = allUserMapState[uid]
                        val member =
                            RoomMember(
                                roomUser = exist?.roomUser?.copy(user = it) ?: it.toRoomUser(),
                                inRoom = true,
                            )
                        put(uid, member)
                    }

                    val owner = basicInfo.owner
                    if (owner != null) {
                        val uid = owner.id
                        val member =
                            RoomMember(
                                roomUser = owner,
                                inRoom = true, // 房主好像无法判断在没在线
                            )
                        put(uid, member)
                    }

                    micListState.forEach {
                        if (it is MicSeatsInfo.User) {
                            val uid = it.user.id
                            val exist = allUserMap[uid]

                            // 因为麦位信息不包含勋章
                            val newUser =
                                it.user.copy(
                                    decorations = it.user.decorations.copy(medalList = exist?.roomUser?.medalList),
                                    cpRelationInfo =
                                        it.user.cpRelationInfo?.copy(
                                            cpCardBackground =
                                                exist
                                                    ?.roomUser
                                                    ?.cpRelationInfo
                                                    ?.cpCardBackground
                                                    .orEmpty(),
                                        ),
                                )
                            val member =
                                RoomMember(
                                    roomUser = newUser,
                                    inRoom = true,
                                )
                            put(uid, member)
                        }
                    }

                    val self = SelfUser
                    if (self != null) { // 补全自己的信息
                        val uid = self.id
                        val member =
                            RoomMember(
                                roomUser = self.toRoomUser(),
                                inRoom = true, // 自己就设定为一定在房间
                            )
                        put(uid, member)
                    }
                }

            // 把那些还在追踪的value添加进去回去继续追踪
            allUserMapState.clear()
            allUserMapState.addMemberRoomUser(tempMap)

            if (infoResp.happenTimestamp > mutableState.onlineHappenTimestamp) {
                mutableState.onlineHappenTimestamp = infoResp.happenTimestamp
                // 在线人数
                onlineCountState.intValue = infoResp.audienceCnt
                onlinePreviewState.clear()
                onlinePreviewState.addAll(infoResp.latestEnterSampleUsers)
            }

            adminIdsState.clear()
            adminIdsState.addAll(infoResp.admins.map { it.id })

            extraInfoState.value =
                extraInfo
                    .run {
                        copy(
                            reqUpMicCount = if (basicInfo.micMode != LiveMicMode.Request) 0 else infoResp.requestMicCount,
                            reqUpMicIng = if (basicInfo.micMode != LiveMicMode.Request) false else reqUpMicIng,
                            pkRoomInfo = if (basicInfo.roomMode == LiveRoomMode.PKMode) infoResp.pkInfo else null,
                            showAudienceTaskPopup = infoResp.showAudienceTaskPopup,
                            audioRoomAudienceBean = infoResp.audienceData,
                        )
                    }
        }
    }

    private fun updatePrivateRoomInfo(infoResp: PrivateRoomInfoResponse) {
        mutableState.withMutableSnapshot {
            basicInfoState.value =
                basicInfo.run {
                    copy(
                        publicId = "",
                        title = infoResp.interactStatus.title.ifEmpty { if (infoResp.isPartner) "CP小屋".localized else "私密小屋".localized },
                        owner = null,
                        roomMode = LiveRoomMode.Private,
                        micMode = LiveMicMode.Free,
                        desc = null,
                        notice = null,
                        background = infoResp.roomBackground.takeIf { it.isNotBlank() },
                        tagIds = null,
                    )
                }

            if (infoResp.happenTimestamp > mutableState.micListHappenTimestamp) {
                mutableState.micListHappenTimestamp = infoResp.happenTimestamp
                handleMicSeatsChange(infoResp.positions, false)
            }

            val self = SelfUser

            val selfUser =
                if (self?.id == infoResp.femaleUser.id) {
                    RoomUser.fromResponse(infoResp.femaleUser)
                } else {
                    RoomUser.fromResponse(infoResp.maleUser)
                }
            val targetUser =
                if (self?.id == infoResp.femaleUser.id) {
                    RoomUser.fromResponse(infoResp.maleUser)
                } else {
                    RoomUser.fromResponse(infoResp.femaleUser)
                }

            val interactStatus =
                PrivateRoomInteractStatus(
                    isCp = infoResp.interactStatus.isCp,
                    isFriend = infoResp.interactStatus.isFriend,
                    starHint = infoResp.interactStatus.starHint,
                    fanHint = infoResp.interactStatus.fanHint,
                    status = infoResp.interactStatus.status,
                    endTimestamp = infoResp.interactStatus.expireTimestamp,
                    remindSeconds = infoResp.interactStatus.remainSeconds,
                    isNewbieTryTime = infoResp.interactStatus.isNewbieTryTime,
                )

            val privateRoomInfo =
                PrivateRoomBasicInfo(
                    mode = infoResp.mode,
                    selfUser = selfUser,
                    targetUser = targetUser,
                    selfIsStar = selfUser.id == infoResp.serviceUserid,
                    targetIsStar = targetUser.id == infoResp.serviceUserid,
                    fromSceneType = infoResp.fromSceneType,
                    fromSceneId = infoResp.fromSceneId,
                    inviteCode = infoResp.inviteCode,
                    newbieLockSeconds = infoResp.newbieLockSeconds,
                    targetInRoom = if (selfUser.id == infoResp.serviceUserid) infoResp.fanInRoom else infoResp.starInRoom,
                    isNewbieTryService = infoResp.isNewbieTryService,
                    interact = interactStatus,
                )

            if (unlockTimestamp == -1L) {
                unlockTimestamp = DateTimeUtils.elapsedRealtime().plus(infoResp.newbieLockSeconds.times(1000L))
            }

            adminIdsState.clear()
            blackIdsState.clear()
            allUserMapState.clear()

            allUserMapState.addMemberRoomUser(
                selfUser.id,
                RoomMember(
                    roomUser = selfUser,
                    inRoom = true,
                ),
            )
            allUserMapState.addMemberRoomUser(
                targetUser.id,
                RoomMember(
                    roomUser = targetUser,
                    inRoom = true,
                ),
            )

            extraInfoState.value = extraInfo.copy(privateRoomInfo = privateRoomInfo)
        }
    }

    private fun MutableMap<String, RoomMember>.addMemberRoomUser(
        id: String,
        member: RoomMember,
    ) {
        put(id, member)
    }

    private fun MutableMap<String, RoomMember>.addMemberRoomUser(members: Map<String, RoomMember>) {
        if (members.isEmpty()) {
            return
        }
        putAll(members)
    }

    // /////////////////////////////////////////////////////////////////////////
    // API接口区域
    // /////////////////////////////////////////////////////////////////////////

    fun refreshRoomInfo() {
        if (DateTimeUtils.elapsedRealtime() - createTimestamp < 2000) {
            return
        }
        coroutineScope.launch {
            getAndUpdateRoomInfo()
        }
    }

    fun refreshRoomUserInfo(userId: String) {
        coroutineScope.launch {
            if (isPrivateRoom) {
                UserManager.getRemoteUserInfo(userId)
            } else {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.getVoiceRoomMemberDetail(roomId, userId)
                }
            }.onSuccess { userResp ->
                mutableState.withMutableSnapshot {
                    allUserMapState[userId]?.also {
                        allUserMapState.addMemberRoomUser(userId, it.copy(roomUser = RoomUser.fromResponse(userResp)))
                    } ?: run {
                        allUserMapState.addMemberRoomUser(userId, RoomMember(RoomUser.fromResponse(userResp), false))
                    }
                    if (userResp.isRoomAdmin == true) {
                        adminIdsState.add(userId)
                    } else {
                        adminIdsState.remove(userId)
                    }
                    if (userResp.inRoomBlackList == true) {
                        blackIdsState.add(userId)
                    } else {
                        blackIdsState.remove(userId)
                    }
                }
            }
        }
    }

    private fun joinRoom() {
        fun connectIM(
            channelId: String,
            retry: Boolean = false,
        ) {
            coroutineScope.launch {
                val ret = IMCompatCore.joinConversation(channelId, ConversationType.CHATROOM, true)
                if (!ret) {
                    if (retry) {
                        onExitRoom("im连接失败，已退出".localized)
                    } else {
                        delay(2000)
                        connectIM(channelId, true)
                    }
                }
            }
        }

        fun connectRtc(
            token: RoomRtcToken,
            retry: Boolean = false,
        ) {
            coroutineScope.launch {
                if (retry) {
                    delay(2000)
                }
                rtcHelper = AppRtcHelper(TencentRtcFactory(), false)
                rtcHelper?.joinChannel(token.channelName, token.token) {
                    if (!retry) {
                        connectRtc(token, true)
                    } else {
                        onExitRoom("rtc连接失败，已退出".localized)
                    }
                }
            }
        }

        fun fetchRoomInfo(retry: Boolean = false) {
            coroutineScope.launch {
                getAndUpdateRoomInfo()
                    .onSuccess {
                        if (autoUpMic && !roomInfoState.isInMic(SelfUser?.id.orEmpty())) {
                            delay(200)
                            sendEvent(RoomEvent.UpMic(-1))
                        }
                    }.onFailure {
                        if (!retry) {
                            delay(2000)
                            fetchRoomInfo(true)
                        } else {
                            onExitRoom("房间信息获取失败，已退出".localized)
                        }
                    }
            }
        }

        suspend fun fetchToken(retry: Boolean = false) {
            executeApiCallExpectingData {
                if (isPrivateRoom) {
                    VoiceRoomApiService.instance.getPrivateRoomToken(mapOf("space_id" to roomId))
                } else {
                    VoiceRoomApiService.instance.getVoiceRoomToken(mapOf("live_house_id" to roomId))
                }
            }.onSuccess { tokenResp ->
                connectIM(tokenResp.imId)
                connectRtc(
                    RoomRtcToken(
                        channelName = tokenResp.rtcChannelName,
                        channelType = tokenResp.rtcChannelType,
                        config = tokenResp.rtcConfig,
                        token = tokenResp.rtcToken,
                    ),
                )
                fetchRoomInfo()
            }.onBusinessFailure {
                if (it.businessCode == -13) {
                    it.extra
                        ?.takeIf { json ->
                            json.containsKey("action") && json["action"]?.jsonPrimitive?.content == "add_friends_popup"
                        }?.getStringOrNull("charge_hint")
                        ?.takeIf { msg -> msg.isNotBlank() }
                        ?.also { msg ->
                            EventBus.tryEasyPostDialog {
                                PrivateRoomJoinTip(msg, "加好友".localized)
                            }
                        }
                }
            }.onFailure {
                if (!retry && it.isNetworkException()) {
                    delay(2000)
                    fetchToken(true)
                } else {
                    onExitRoom("进入房间失败，已退出".localized)
                }
            }
        }

        coroutineScope.launch {
            fetchToken(false)
        }
    }

    private suspend fun getAndUpdateRoomInfo(): Result<*> =
        if (isPrivateRoom) {
            executeApiCallExpectingData {
                VoiceRoomApiService.instance.getPrivateRoomInfo(roomId)
            }.onSuccess { infoResp ->
                updatePrivateRoomInfo(infoResp)
            }
        } else {
            executeApiCallExpectingData {
                VoiceRoomApiService.instance.getVoiceRoomInfo(roomId)
            }.onSuccess { infoResp ->
                updateRoomInfo(infoResp)
                BBoxInfo(
                    roomId = infoResp.roomId.toIntOrNull() ?: 0,
                    status = if (infoResp.blindBoxFilled) 3 else 2,
                    link = infoResp.descLink,
                    remainSeconds = infoResp.blindBoxTaskDuration.toLong(),
                    filled = infoResp.blindBoxFilled,
                    showBlindboxTask = infoResp.showBlindBoxTask,
                    completeDesc = infoResp.blindBoxFilledNote,
                ).also {
                    blindBoxLink = it.link
                    if (_bBoxInfoState.value == null) {
                        _bBoxInfoState.value = it
                    }
                }
            }
        }

    // /////////////////////////////////////////////////////////////////////////
    // API接口区域
    // /////////////////////////////////////////////////////////////////////////

    // /////////////////////////////////////////////////////////////////////////
    // 消息处理
    // /////////////////////////////////////////////////////////////////////////

    override fun onGroupMemberKicked(
        groupID: String,
        opUser: V2TIMGroupMemberInfo,
        memberList: List<V2TIMGroupMemberInfo>,
    ) {
        if (groupID == roomInfoState.imId && memberList.any { it.userID == SelfUser?.id }) {
            coroutineScope.launch {
                IMCompatCore.joinConversation(roomInfoState.imId, ConversationType.CHATROOM, false)
            }
        }
    }

    override fun onRecvNewCustomMessage(
        message: UCCustomMessage,
        offline: Boolean,
    ) {
        coroutineScope.launch {
            when (message.cmd) {
                IMEvent.BLACK_USER -> { // 用户被拉黑
                    message
                        .getJsonValue<BasicUser>("user")
                        ?.takeIf {
                            it.sIsSelf
                        }?.also {
                            onExitRoom("你已被房间拉黑".localized)
                        }
                }

                IMEvent.TAKEAWAY_MIC -> { // 用户被踢下麦
                    message
                        .getJsonValue<BasicUser>("target_user")
                        ?.takeIf {
                            it.sIsSelf
                        }?.also {
                            sendEvent(RoomEvent.DownMic)
                        }
                }

                IMEvent.VOICE_MIC_ABANDONED -> { // 用户被禁麦
                    if (message.getJsonString("userid") == SelfUser?.id) {
                        sendEvent(RoomEvent.DownMic)
                    }
                }

                IMEvent.USER_EXIT -> { // 用户退出
                    val user = message.getJsonValue<BasicUser>("user") ?: return@launch
                    if (user.sIsSelf && message.getJsonBoolean("is_forced", false)) {
                        onExitRoom("你已被踢出房间".localized)
                        return@launch
                    }
                    val happenTimestamp = message.getJsonLong("happen_timestamp", 0)
                    if (happenTimestamp > mutableState.onlineHappenTimestamp) {
                        mutableState.onlineHappenTimestamp = happenTimestamp
                        handleMemberChangeEvent(
                            isAdd = false,
                            roomUser = user.toRoomUser(),
                            audienceCount = message.getJsonInt("audience_cnt", 0),
                            previewList = message.getJsonValue<List<BasicUser>>("latest_enter_sample_users", emptyList()),
                        )
                    }
                }

                IMEvent.USER_ENTRANCE -> { // 用户进入
                    if (!message.getJsonBoolean("is_hidden", false)) {
                        val happenTimestamp = message.getJsonLong("happen_timestamp", 0)
                        if (happenTimestamp > mutableState.onlineHappenTimestamp) {
                            mutableState.onlineHappenTimestamp = happenTimestamp
                            val user = message.getJsonValue<UserResponse>("user") ?: return@launch
                            handleMemberChangeEvent(
                                isAdd = true,
                                roomUser = RoomUser.fromResponse(user),
                                audienceCount = message.getJsonInt("audience_cnt", 0),
                                previewList = message.getJsonValue<List<BasicUser>>("latest_enter_sample_users", emptyList()),
                            )
                        }
                    }
                }

                IMEvent.ROOM_SETTINGS -> { // 房间设置变更
                    when (message.getJsonString("settings_name")) {
                        Const.RoomInfoChangeKey.ROOM_MODE -> {
                            mutableState.withMutableSnapshot {
                                basicInfoState.value =
                                    basicInfo.run {
                                        copy(
                                            roomMode = LiveRoomMode.valueOf(message.getJsonInt("value", -1)),
                                            background =
                                                message.getJsonString("wakoo_room_background").takeIsNotBlank()
                                                    ?: background,
                                        )
                                    }
                                if (basicInfo.roomMode.isUnKnown) {
                                    onExitRoom("不支持此房间模式，已退出".localized)
                                } else {
                                    handleMicSeatsChange(emptyList())
                                }
                            }
                        }

                        Const.RoomInfoChangeKey.MIC_MODE -> {
                            mutableState.withMutableSnapshot {
                                basicInfoState.value =
                                    basicInfo.copy(
                                        micMode = LiveMicMode.valueOf(message.getJsonInt("value", -1)),
                                    )
                                extraInfoState.value =
                                    extraInfo.run {
                                        copy(
                                            reqUpMicCount = if (basicInfo.micMode != LiveMicMode.Request) 0 else reqUpMicCount,
                                            reqUpMicIng = if (basicInfo.micMode != LiveMicMode.Request) false else reqUpMicIng,
                                        )
                                    }
                            }
                        }

                        Const.RoomInfoChangeKey.TITLE -> {
                            mutableState.basicInfoState.value =
                                mutableState.basicInfo.copy(
                                    title = message.getJsonString("value", ""),
                                )
                        }

                        Const.RoomInfoChangeKey.DESC -> {
                            mutableState.basicInfoState.value =
                                mutableState.basicInfo.copy(
                                    desc = message.getJsonString("value", ""),
                                )
                        }

                        Const.RoomInfoChangeKey.TAGS -> {
                            mutableState.basicInfoState.value =
                                mutableState.basicInfo.copy(
                                    tagIds = message.getJsonValue<List<VoiceTag>>("value")?.map { it.id },
                                )
                        }

                        Const.RoomInfoChangeKey.ROOM_BACKGROUND -> {
                            val value = message.getJsonString("value", "")
                            val background = message.getJsonString("wakoo_room_background", "")
                            mutableState.basicInfoState.value =
                                mutableState.basicInfo.copy(
                                    background = value.ifBlank { background },
                                )
                        }
                    }
                }

                IMEvent.AGREE_MIC -> { // 同意上麦
                    message
                        .getJsonValue<BasicUser>("apply_user")
                        ?.takeIf {
                            it.sIsSelf
                        }?.also {
                            mutableState.extraInfoState.value = mutableState.extraInfo.copy(reqUpMicIng = false)
                        }
                }

                IMEvent.REFUSE_MIC -> { // 拒绝上麦
                    message
                        .getJsonValue<BasicUser>("apply_user")
                        ?.takeIf {
                            it.sIsSelf
                        }?.also {
                            mutableState.extraInfoState.value = mutableState.extraInfo.copy(reqUpMicIng = false)
                        }
                }

                IMEvent.MIC_APPLY_COUNT -> { // 申请上麦人数变
                    val requestCount = message.getJsonInt("mic_apply_cnt", 0)
                    mutableState.extraInfoState.value = mutableState.extraInfo.copy(reqUpMicCount = requestCount)
                }

                IMEvent.GRANT_ADMIN -> { // 设置管理员
                    message.getJsonValue<BasicUser>("user")?.id?.also {
                        mutableState.adminIdsState.add(it)
                    }
                }

                IMEvent.REVOKE_ADMIN -> { // 取消管理员
                    message.getJsonValue<BasicUser>("user")?.id?.also {
                        mutableState.adminIdsState.remove(it)
                    }
                }

                IMEvent.SEATS_CHANGE -> { // 麦位发生变化
                    val happenTimestamp = message.getJsonLong("happen_timestamp", 0)
                    if (happenTimestamp > mutableState.micListHappenTimestamp) {
                        mutableState.micListHappenTimestamp = happenTimestamp
                        message.getJsonValue<List<Seat>>("seats")?.also {
                            mutableState.withMutableSnapshot {
                                handleMicSeatsChange(it, true)
                            }
                        }
                    }
                    message.getJsonValue<PkInfo>("pk_info")?.also {
                        mutableState.extraInfoState.value =
                            mutableState.extraInfo.copy(
                                pkRoomInfo = it,
                            )
                    }
                }

                IMEvent.PRIVATE_ROOM_INTERACT -> {
                    if (isPrivateRoom) {
                        val privateRoomStatus = message.parseDataJson<InteractStatus>()
                        if (privateRoomStatus != null && roomId == privateRoomStatus.roomId) {
                            if (privateRoomStatus.status == -1) {
                                onExitRoom("你们的互动时长不足啦, 已退出".localized)
                                return@launch
                            }
                            val interactStatus =
                                PrivateRoomInteractStatus(
                                    isCp = privateRoomStatus.isCp,
                                    isFriend = privateRoomStatus.isFriend,
                                    starHint = privateRoomStatus.starHint,
                                    fanHint = privateRoomStatus.fanHint,
                                    status = privateRoomStatus.status,
                                    endTimestamp = privateRoomStatus.expireTimestamp,
                                    remindSeconds = privateRoomStatus.remainSeconds,
                                    isNewbieTryTime = privateRoomStatus.isNewbieTryTime,
                                )

                            mutableState.extraInfoState.value =
                                mutableState.extraInfo.copy(
                                    privateRoomInfo = mutableState.extraInfo.privateRoomInfo?.copy(interact = interactStatus),
                                )
                        }
                    }
                }

                IMEvent.VOICE_CALL_FINISH -> {
                    if (isPrivateRoom) {
                        val voiceCallNotice = message.parseDataJson<VoiceCallNotice>() ?: return@launch
                        val privateRoomInfo = mutableState.extraInfo.privateRoomInfo ?: return@launch
                        if (privateRoomInfo.mode == 2 && privateRoomInfo.inviteCode == voiceCallNotice.callId) {
                            onExitRoom("本次互动已结束".localized)
                        }
                    }
                }

                IMEvent.TEAM_PK_EVENT -> {
                    message.getJsonValue<PkInfo>("pk_info")?.also {
                        mutableState.extraInfoState.value =
                            mutableState.extraInfo.copy(
                                pkRoomInfo = it,
                            )
                    }
                    val pkEvent = message.parseDataJson<PKEvent>()
                    if (roomInfoState.isPkRoom && pkEvent?.action == "end" && pkEvent.winnerInfo != null &&
                        (pkEvent.winnerSide == 1 || pkEvent.winnerSide == 2)
                    ) {
                        sendEvent(
                            RoomEvent.PanelDialog(
                                dialogProperties = AnyPopDialogProperties(direction = DirectionState.CENTER),
                            ) {
                                PKResultDialog(pkEvent)
                            },
                        )
                    }
                }

                IMEvent.CP_BLIND_BOX_DURATION_SYNC -> {
                    message.parseDataJson<BBoxInfo>()?.also {
                        _bBoxInfoState.value =
                            it.copy(link = blindBoxLink).also { info ->
                                LiveRoomManager.receiveBlindBoxInfo(info)
                            }
                    }
                }
            }
        }
    }

    // /////////////////////////////////////////////////////////////////////////
    // 消息处理
    // /////////////////////////////////////////////////////////////////////////

    // /////////////////////////////////////////////////////////////////////////
    // 事件处理
    // /////////////////////////////////////////////////////////////////////////
    private suspend fun handleEvent(event: RoomEvent) {
        when (event) {
            is RoomEvent.CollapseRoom -> {
                if (checkPrivateRoomByExit()) {
                    LiveRoomManager.collapseRoom(roomId)
                }
            }

            is RoomEvent.ExitRoom -> {
                if (checkPrivateRoomByExit()) {
                    onExitRoom(null)
                }
            }

            is RoomEvent.FollowUser -> {
                executeApiCallExpectingData {
                    UserApiService.instance.followUser(
                        mapOf("userid" to event.userId, "follow" to true.toString()),
                    )
                }.onSuccess {
                    mutableState.allUserMap[event.userId]?.also {
                        mutableState.allUserMapState.addMemberRoomUser(
                            event.userId,
                            it.copy(
                                roomUser =
                                    it.roomUser.copy(
                                        withRelationInfo = it.roomUser.withRelationInfo.copy(isFollowed = true),
                                    ),
                            ),
                        )
                    }
                }
            }

            is RoomEvent.AddFriend -> {
                executeApiCallExpectingData {
                    UserApiService.instance.addFriend(
                        buildMap {
                            put("target_user_id", event.userId)
                            if (event.sceneType != -1) {
                                put("scene_type", event.sceneType.toString())
                            }
                            if (event.sceneId != -1) {
                                put("scene_id", event.sceneId.toString())
                            }
                        },
                    )
                }
            }

            is RoomEvent.EditRoomInfo -> {
                val edit = event.edit
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.updateVoiceRoom(
                        buildMap {
                            put("live_house_id", roomId)
                            put("title", edit.title)
                            put("desc", edit.desc)
                            put("tag_ids", edit.tagIds.joinToString(","))
                        },
                    )
                }.onSuccess {
                    mutableState.basicInfoState.value =
                        mutableState.basicInfo.run {
                            copy(
                                title = edit.title,
                                desc = edit.desc,
                                tagIds = edit.tagIds,
                            )
                        }
                }
            }

            is RoomEvent.UpdateMicMode -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.updateVoiceRoom(
                        buildMap {
                            put("live_house_id", roomId)
                            put(Const.RoomInfoChangeKey.MIC_MODE, event.value)
                        },
                    )
                }
            }

            is RoomEvent.UpdateRoomMode -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.updateVoiceRoom(
                        buildMap {
                            put("live_house_id", roomId)
                            put(Const.RoomInfoChangeKey.ROOM_MODE, event.value)
                        },
                    )
                }
            }

            is RoomEvent.UpMic -> {
                executeApiCallExpectingData {
                    if (isPrivateRoom) {
                        VoiceRoomApiService.instance.joinPrivateRoomMic(
                            buildMap {
                                put("space_id", roomId)
                                if (event.index > -1) {
                                    put("mic_order", event.index.toString())
                                }
                            },
                        )
                    } else {
                        VoiceRoomApiService.instance.joinVoiceRoomMic(
                            buildMap {
                                put("live_house_id", roomId)
                                if (event.index > -1) {
                                    put("mic_order", event.index.toString())
                                }
                            },
                        )
                    }
                }.onSuccess {
                    if (it.isEmpty()) { // 说明不是自由上麦模式
                        mutableState.extraInfoState.value = mutableState.extraInfo.copy(reqUpMicIng = true)
                    }
                }
            }

            is RoomEvent.DownMic -> {
                rtcHelper?.downMic(null)
                executeApiCallExpectingData {
                    if (isPrivateRoom) {
                        VoiceRoomApiService.instance.leavePrivateRoomMic(
                            mapOf("space_id" to roomId),
                        )
                    } else {
                        VoiceRoomApiService.instance.leaveVoiceRoomMic(
                            mapOf("live_house_id" to roomId),
                        )
                    }
                }
            }

            is RoomEvent.ToggleMic -> {
                val muted = rtcHelper?.toggleMuteAudio()
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.muteVoiceRoomMic(
                        buildMap {
                            put("live_house_id", roomId)
                            put("muted", muted.toString())
                        },
                    )
                }
            }

            is RoomEvent.KickMic -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.kickVoiceRoomMic(
                        buildMap {
                            put("live_house_id", roomId)
                            put("target_user_id", event.userId)
                        },
                    )
                }
            }

            is RoomEvent.InviteUpMic -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.sendInviteVoiceRoomMic(
                        buildMap {
                            put("live_house_id", roomId)
                            put("invited_user_id", event.userId)
                        },
                    )
                }
            }

            is RoomEvent.HandleMicReq -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.allowVoiceRoomMicRequest(
                        buildMap {
                            put("live_house_id", roomId)
                            put("apply_user_id", event.userId)
                            put("agree", event.agree.toString())
                        },
                    )
                }
            }

            is RoomEvent.CancelMicReq -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.cancelVoiceRoomMicRequest(
                        buildMap {
                            put("live_house_id", roomId)
                        },
                    )
                }.onFailure {
                    mutableState.extraInfoState.value = mutableState.extraInfo.copy(reqUpMicIng = false)
                }
            }

            is RoomEvent.ClearLoveValue -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.resetLoveValue(
                        buildMap {
                            put("live_house_id", roomId)
                        },
                    )
                }
            }

            is RoomEvent.AgreeUpMic -> {
                executeApiCallExpectingData {
                    if (event.from == 1) {
                        VoiceRoomApiService.instance.joinInvitedVoiceRoomMic(
                            mapOf("live_house_id" to roomId),
                        )
                    } else {
                        VoiceRoomApiService.instance.takeSeatVoiceRoomMic(
                            mapOf("live_house_id" to roomId),
                        )
                    }
                }
            }

            is RoomEvent.SetBlackEvent -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.blockVoiceRoomUser(
                        mapOf(
                            "live_house_id" to roomId,
                            "userid" to event.userId,
                            "black" to event.black.toString(),
                        ),
                    )
                }.onSuccess { infoResp ->
                    if (event.black) {
                        mutableState.blackIdsState.add(event.userId)
                    } else {
                        mutableState.blackIdsState.remove(event.userId)
                    }
                }
            }

            is RoomEvent.SetAdminEvent -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.setVoiceRoomAdmin(
                        mapOf(
                            "live_house_id" to roomId,
                            "userid" to event.userId,
                            "is_admin" to event.admin.toString(),
                        ),
                    )
                }.onSuccess { infoResp ->
                    if (event.admin) {
                        mutableState.adminIdsState.add(event.userId)
                    } else {
                        mutableState.adminIdsState.remove(event.userId)
                    }
                }
            }

            is RoomEvent.OnPkSettingsEvent -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.pkSettings(
                        buildMap {
                            put("live_house_id", roomId)
                            put("setting_type", event.type)
                            if (event.title != null) {
                                put("punish", event.title)
                            }
                            if (event.duration != null) {
                                put("duration", event.duration)
                            }
                        },
                    )
                }
            }

            else -> Unit
        }
    }

    /**
     * @return 是否可以继续退出，true表示可以，否则不行
     */
    private fun checkPrivateRoomByExit(): Boolean {
        if (isPrivateRoom) {
            mutableState.extraInfo.privateRoomInfo
                ?.takeIf {
                    it.isNewbieTryService && (it.interact.isNewbieTryTime || it.mode == 2)
                }?.apply {
                    if (selfIsStar) {
                        if (targetInRoom) {
                            showToast("对方还在房间内，不能退出哦".localized)
                            return false
                        }
                    } else {
                        if (DateTimeUtils.elapsedRealtime() < unlockTimestamp) {
                            showToast("互动至少%ss后才能退出哦".localizedFormat(newbieLockSeconds))
                            return false
                        }
                    }
                }
        }
        return true
    }
    // /////////////////////////////////////////////////////////////////////////
    // 事件处理
    // /////////////////////////////////////////////////////////////////////////
}
