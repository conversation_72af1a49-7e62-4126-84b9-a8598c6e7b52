package com.buque.wakoo.repository

import com.buque.wakoo.ui.screens.crony.CronyRepo

object GlobalRepository {
    val chatGroupRepo by lazy {
        ChatGroupRepo()
    }

    val walletRepo: WalletRepository by lazy {
        WalletRepository()
    }

    val relationRepo by lazy { RelationRepo() }

    val taskRepository by lazy { TaskRepository() }

    val hongBaoRepo by lazy { HongBaoRepo() }

    val cronyRepo by lazy { CronyRepo() }
}
