package com.buque.wakoo.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.TargetUserClassBean
import com.buque.wakoo.bean.TopClassBean
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class FirstClassViewModel(
    val userId: String,
) : ViewModel() {
    private val _firstClassBean = MutableStateFlow(TopClassBean(showFirstClassInfo = false))
    val firstClassBean = _firstClassBean.asStateFlow()

    private val _targetUserClassBean = MutableStateFlow<TargetUserClassBean?>(null)
    val targetUserClassBean = _targetUserClassBean.asStateFlow()

    fun refresh() {
        viewModelScope.launch {
            if (userId == (SelfUser?.id ?: "")) {
                executeApiCallExpectingData {
                    UserApiService.instance.getTopClassInfo()
                }.onSuccess {
                    _firstClassBean.emit(it)
                }
            } else {
                executeApiCallExpectingData {
                    UserApiService.instance.getUserTopClassInfo(userId)
                }.onSuccess {
                    _targetUserClassBean.emit(it)
                }
            }
        }
    }
}
