package com.buque.wakoo.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.bean.AudioRoomAudienceBean
import com.buque.wakoo.ext.getOrNull
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.network.api.service.VoiceRoomApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement

class AudioRoomDailyViewModel(
    initialData: JsonObject? = null,
) : ViewModel(),
    IMCompatListener {
    private val _dailyTask = MutableStateFlow<AudioRoomAudienceBean?>(null)
    val dailyTask = _dailyTask.asStateFlow()

    init {
        IMCompatCore.addIMListener(this)
        if (initialData != null) {
            viewModelScope.launch(Dispatchers.Default) {
                parseTask(initialData)
            }
        } else {
            refreshAudienceInfo()
        }
    }

    override fun onCleared() {
        super.onCleared()
        IMCompatCore.removeIMListener(this)
    }

    @Volatile
    private var isRequesting = false

    @Synchronized
    fun refreshAudienceInfo() {
        if (isRequesting) {
            return
        }
        isRequesting = true
        viewModelScope.launch {
            executeApiCallExpectingData {
                VoiceRoomApiService.instance.getAudioRoomAudienceInfo()
            }.onSuccess {
                parseTask(it)
                isRequesting = false
            }.onFailure {
                isRequesting = false
            }
        }
    }

    fun parseTask(it: JsonObject) {
        val cpTask =
            it.getOrNull("cp_task")?.let {
                runCatching {
                    AppJson.decodeFromJsonElement<AudioRoomAudienceBean>(it)
                }.getOrNull()
            }
        val relativeTask =
            it.getOrNull("not_cp_user_task")?.let {
                runCatching {
                    AppJson.decodeFromJsonElement<AudioRoomAudienceBean>(it)
                }.getOrNull()
            }

        if (cpTask != null && !cpTask.isFinished) {
            _dailyTask.value = cpTask
        } else if (relativeTask != null && !relativeTask.isFinished) {
            _dailyTask.value = relativeTask
        } else {
            _dailyTask.value = relativeTask ?: cpTask
        }
    }

    override fun onRecvNewCustomMessage(
        message: UCCustomMessage,
        offline: Boolean,
    ) {
        if (message.cmd == IMEvent.AUDIOROOM_AUDIENCE_TASK_STATUS_SYNC) {
            parseTask(message.customJson)
        }
    }
}
