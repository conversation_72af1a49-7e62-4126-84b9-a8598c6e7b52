package com.buque.wakoo.viewmodel

import androidx.compose.runtime.mutableStateOf
import androidx.core.net.toUri
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.Const
import com.buque.wakoo.app.DevicesKV
import com.buque.wakoo.bean.AccountInfo
import com.buque.wakoo.bean.TokenInfo
import com.buque.wakoo.bean.user.SelfUserInfo
import com.buque.wakoo.ext.parseValue
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.DeviceInfoManager
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.manager.NetworkManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.track
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.network.api.bean.LoginRequest
import com.buque.wakoo.network.api.bean.LoginResponse
import com.buque.wakoo.network.api.bean.RegisterRequest
import com.buque.wakoo.network.api.service.LoginApiService
import com.buque.wakoo.network.executeApiCall
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.network.onBusinessFailure
import com.buque.wakoo.ui.screens.liveroom.screen.GuideInfo
import com.buque.wakoo.utils.upload.CosTransferHelper
import com.buque.wakoo.utils.upload.CosTransferProvider
import com.buque.wakoo.utils.upload.TransferResult
import com.buque.wakoo.utils.upload.UploadUtils
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import kotlin.coroutines.cancellation.CancellationException

class LoginViewModel : BaseViewModel() {
    private val userApiService
        get() = LoginApiService.instance

    private var registerInfo: LoginResponse? = null

    val recoverState = mutableStateOf<AccountNeedRecoverException?>(null)

    private val cosTransferHelper = CosTransferHelper()

    suspend fun register(request: RegisterRequest?) {
        registerInfo?.also { info ->
            val uriString = request?.avatar_url
            if (uriString?.isNotBlank() == true) {
                val uri = uriString.toUri()
                val path = UploadUtils.generateOSSPath(WakooApplication.instance, uri, UploadUtils.DEFAULT_AVATAR_PATH)
                CosTransferProvider.tempAccessToken = info.accessToken
                val currentTaskWrapper =
                    cosTransferHelper.upload(
                        cosPath = path,
                        uri = uri,
                    )

                // 挂起等待上传结果
                when (val result = currentTaskWrapper.await()) {
                    is TransferResult.Success -> {
                        CosTransferProvider.tempAccessToken = null
                        callRegister(
                            response = info,
                            request =
                                request.copy(
                                    avatar_url = result.url,
                                    ism_device_id = DeviceInfoManager.smBoxId,
                                    is_using_vpn = NetworkManager.isUsingVPN(),
                                ),
                        )
                    }

                    is TransferResult.Failure -> {
                        showToast("头像上传失败".localized)
                    }
                }
            } else {
                callRegister(
                    response = info,
                    request =
                        request?.copy(
                            avatar_url = null,
                            ism_device_id = DeviceInfoManager.smBoxId,
                            is_using_vpn = NetworkManager.isUsingVPN(),
                        ) ?: RegisterRequest(
                            ism_device_id = DeviceInfoManager.smBoxId,
                            is_using_vpn = NetworkManager.isUsingVPN(),
                        ),
                )
            }
        }
    }

    fun sendVerifyCode(number: String) {
        viewModelScope.launch {
            executeApiCall {
                userApiService.sendVerifyCode(
                    mapOf(
                        "code" to "86",
                        "number" to number,
                    ),
                )
            }
        }
    }

    /**
     * 手机号登录
     */
    suspend fun phoneLogin(
        phone: String,
        code: String,
        onlyRegister: Boolean = false,
    ): LoginResponse? {
        val request =
            LoginRequest(
                account_type = 301,
                account_id = phone,
                ism_device_id = DeviceInfoManager.smBoxId,
                is_using_vpn = NetworkManager.isUsingVPN(),
                is_recover = false,
                phone_area_code = "86",
                verify_code = code,
            )
        return executeApiCallExpectingData(
            toastWhiteList = intArrayOf(-21),
        ) {
            userApiService.login(request)
        }.handleLoginResult(request, onlyRegister)
    }

    /**
     * 快速登录
     *
     * @return 是否需要注册
     */
    suspend fun quickLogin(): LoginResponse? {
        val request =
            LoginRequest(
                account_type = 305,
                account_id = DeviceInfoManager.deviceId,
                ism_device_id = DeviceInfoManager.smBoxId,
                is_using_vpn = NetworkManager.isUsingVPN(),
                is_recover = false,
            )
        return executeApiCallExpectingData(
            toastWhiteList = intArrayOf(-21),
        ) {
            userApiService.login(request)
        }.handleLoginResult(request)
    }

    /**
     * google登录
     *
     * @return 是否需要注册
     */
    suspend fun googleLogin(token: String): LoginResponse? {
        val request =
            LoginRequest(
                account_type = 302,
                account_id = token,
                ism_device_id = DeviceInfoManager.smBoxId,
                is_using_vpn = NetworkManager.isUsingVPN(),
                is_recover = false,
            )
        return executeApiCallExpectingData(toastWhiteList = intArrayOf(-21)) {
            userApiService.login(request)
        }.handleLoginResult(request)
    }

    /**
     * 恢复账号
     */
    suspend fun recoverAccount(value: AccountNeedRecoverException?): LoginResponse? {
        val request = (value ?: recoverState.value)?.request?.copy(is_recover = true)
        if (request == null) {
            throw CancellationException("数据异常，请重试".localized)
        }
        return executeApiCallExpectingData {
            userApiService.login(request)
        }.handleLoginResult(request)
    }

    private suspend fun callRegister(
        response: LoginResponse,
        request: RegisterRequest,
    ) {
        executeApiCall {
            userApiService.register(
                token = response.accessToken,
                registerRequest = request,
            )
        }.onSuccess { resp ->
            Const.AdjustEvent.注册成功.track()
            saveAccountInfo(response, resp)
        }
    }

    private suspend fun Result<LoginResponse>.handleLoginResult(
        request: LoginRequest,
        onlyRegister: Boolean = false,
    ): LoginResponse? =
        onSuccess {
            if (!it.forcePhoneLogin) {
                if (!EnvironmentManager.isProdRelease) {
                    if (request.account_type == 301) {
                        DevicesKV.putString("_debug_current_phone", request.account_id)
                    } else {
                        DevicesKV.putString("_debug_current_phone", "")
                    }
                }
                if (it.needRegister) {
                    registerInfo = it
                } else if (!onlyRegister) {
                    saveAccountInfo(it)
                }
            }
        }.onBusinessFailure {
            if (it.businessCode == -21) {
                recoverState.value =
                    AccountNeedRecoverException(
                        it.businessMessage
                            ?: "目前该账号处于申请注销状态，此时登录将取消注销并回复您账号相关数据。如果您已决定需要注销此账号，请在发起注销后的30天内不要登录此账号，30天后此账号将自动完成注销。".localized,
                        request,
                    )
            }
        }.getOrNull()

    private suspend fun saveAccountInfo(
        response: LoginResponse,
        registerJson: JsonObject? = null,
    ): Result<*> =
        executeApiCallExpectingData {
            userApiService.getUserInfo(response.accessToken, response.userid)
        }.onSuccess {
            registerJson?.parseValue<GuideInfo>("guide_info")?.also { info ->
                LocalAppNavController.useGlobalRoot?.push(Route.InviteToPrivateRoom(info, true))
            }
            AccountManager.login(
                AccountInfo(
                    tokenInfo = TokenInfo(response.accessToken, response.refreshToken, response.imToken),
                    userInfo = SelfUserInfo.fromResponse(it),
                ),
            )
        }
}

class AccountNeedRecoverException(
    val title: String,
    val request: LoginRequest,
) : Exception()
