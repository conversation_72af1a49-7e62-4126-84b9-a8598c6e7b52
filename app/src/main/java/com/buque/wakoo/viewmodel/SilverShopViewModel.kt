package com.buque.wakoo.viewmodel

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.bean.ExGift
import com.buque.wakoo.bean.ExProps
import com.buque.wakoo.bean.ExchangeItem
import com.buque.wakoo.bean.SilverMallResult
import com.buque.wakoo.ext.getIntOrNull
import com.buque.wakoo.ext.getLongOrNull
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.api.service.DressupApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.cUpdate
import com.buque.wakoo.ui.widget.state.executeStatefulApiCall
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

class SilverShopViewModel : BaseViewModel() {
    private val mutableCState: MutableState<CState<SilverMallResult>> = mutableStateOf(CState.Idle)

    val cState get() = mutableCState.value

    init {
        requestData(false)
    }

    fun requestData(isRefresh: Boolean) {
        viewModelScope.launch {
            executeStatefulApiCall(mutableCState, isRefresh, {
                DressupApiService.instance.getSilverMallInfo()
            }) { it }
        }
    }

    fun exchangeItem(
        item: ExchangeItem,
        num: Int,
    ): Deferred<Boolean> {
        val map = item.buildRequestMap().also { it["exchange_cnt"] = num.toString() }
        return viewModelScope.async {
            executeApiCallExpectingData {
                DressupApiService.instance.exchange(map)
            }.onSuccess { ret ->
                ret.getLongOrNull("silver_balance")?.also { balance ->
                    AccountManager.updateSelfCurrencyInfo {
                        it.copy(silverBalance = balance)
                    }
                    mutableCState.cUpdate { it.copy(silverBalance = balance) }
                }
                when (item) {
                    is ExGift -> showToast("礼物兑换成功，请前往背包查看".localized)
                    is ExProps -> showToast("装扮兑换成功，请前往我的装扮页面进行佩戴".localized)
                }
                // 刷新礼物面板
            }.isSuccess
        }
    }
}
