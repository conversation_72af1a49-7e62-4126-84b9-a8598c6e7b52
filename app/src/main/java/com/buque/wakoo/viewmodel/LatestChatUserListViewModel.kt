package com.buque.wakoo.viewmodel

import com.buque.wakoo.app.AppJson
import com.buque.wakoo.ext.getOrNull
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.widget.state.CState
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonPrimitive

class LatestChatUserListViewModel : BasicListPaginateViewModel<Int, UserResponse>() {
    private val api = UserApiService.instance
    private var lastId = 0
    override suspend fun loadData(
        pageKey: Int,
        pageSize: Int
    ): Result<List<UserResponse>> = executeApiCallExpectingData {
        api.getLatestChatUserList(pageKey)
    }.map { obj ->
        lastId = obj.getOrNull("last_chat_id")?.jsonPrimitive?.intOrNull ?: -1
        obj.getOrNull("users")?.let { el ->
            AppJson.decodeFromJsonElement<List<UserResponse>>(el)
        }.orEmpty()
    }

    override fun getFirstPageKey(dataKey: Any): Int {
        return 0
    }

    override fun getNextPageKey(
        cState: CState<List<UserResponse>>,
        dataKey: Any,
        pageKey: Int?
    ): Int {
        return lastId
    }

    override fun getDistinctSelector(): (UserResponse) -> String {
        return {
            it.id
        }
    }
}