package com.buque.wakoo.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.bean.GiftWallItemDetail
import com.buque.wakoo.ext.getOrNull
import com.buque.wakoo.ext.getStringOrNull
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.NetworkManager
import com.buque.wakoo.network.api.service.GiftApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.datapoints.Analytics
import com.buque.wakoo.utils.eventBus.EventBus
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlin.getValue

class GiftWallDetailViewModel : ViewModel() {
    private val _isLoadingState = MutableStateFlow(false)
    val isLoadingState = _isLoadingState.asStateFlow()

    private val _events = MutableSharedFlow<Event>()
    val events = _events.asSharedFlow()

    @Synchronized
    fun getGiftDetail(
        userId: Int,
        giftId: Int,
        purpose: Int,
        sceneType: Int,
        sceneId: Int,
        begId: Int? = null,
        callback: (GiftWallItemDetail?) -> Unit,
    ) {
        if (_isLoadingState.value) {
            return
        }
        _isLoadingState.value = true
        viewModelScope.launch {
            executeApiCallExpectingData {
                GiftApiService.instance
                    .getWallGiftDetail(userId, giftId, purpose, sceneType, sceneId.toString(), begId)
            }.onSuccess {
                callback(it)
//                    _giftDetailState.value = it
                _isLoadingState.value = false
            }.onFailure {
                it.printStackTrace()
                callback(null)
//                    _giftDetailState.value = it
                _isLoadingState.value = false
            }
        }
    }

    /**
     * 1. 赠送礼物墙礼物
     */
    suspend fun sendWallGift(
        userId: Int,
        giftDetail: GiftWallItemDetail,
        count: Int,
        sence_type: Int,
        scene_id: Int,
        greetings: String = "",
    ): Result<JsonObject> =
        executeApiCallExpectingData {
            GiftApiService.instance.sendWallGift(
                mapOf(
                    "target_user_id" to userId,
                    "gift_id" to giftDetail.id,
                    "count" to count,
                    "from_packet" to (giftDetail.packetCnt > 0).toString(),
                    "greetings" to greetings,
                    "scene_type" to sence_type,
                    "scene_id" to scene_id,
                ),
            )
        }.onSuccess {
//                delay(500)
            showToast(it.getStringOrNull("hint_msg"))
            it.getOrNull("record_ids")?.let {
                val sentGiftIds = AppJson.decodeFromJsonElement<List<Int>>(it)
                EventBus.trySend(SendGiftFromWallEvent(userId, sentGiftIds))
            }
        }

    /**
     * 2. 创建一个礼物求打赏信息
     *
     * @param giftId 打赏的礼物id
     * @param count 打赏的礼物数量
     */
    suspend fun makeBegGiftRequest(
        userId: Int,
        giftDetail: GiftWallItemDetail,
        count: Int,
    ): Result<JsonObject> =
        executeApiCallExpectingData {
            GiftApiService.instance.requestRewardGift(
                mapOf(
                    "user_id" to userId.toString(),
                    "gift_id" to giftDetail.id,
                    "gift_count" to count,
                ),
            )
        }.onSuccess {
//            showToast(it.getStringOrNull("toast"))
            _events.emit(Event.BegGiftEvent(giftDetail.id, count))
        }

    /**
     * 3 施舍
     *
     * @param begId 乞讨id
     */
    suspend fun giveBegGiftRequest(
        begId: Int?,
        greetings: String? = null,
    ): Result<JsonObject> {
        if (begId == null) {
            return Result.failure(Exception("无法施舍礼物,因为begId = null"))
        }
        return executeApiCallExpectingData {
            GiftApiService.instance.requestCharityGift(
                buildMap {
                    put("beg_id", begId)
                    if (!greetings.isNullOrBlank()) {
                        put("greetings", greetings)
                    }
                    put("is_using_vpn", (NetworkManager.isUsingVpnOrProxy()).toString())
                },
            )
        }.onSuccess {
//            showToast(it.getStringOrNull("toast_msg"))
            _events.emit(Event.CharitGiftEvent(begId))
        }
    }

    sealed interface Event {
        data class BegGiftEvent(
            val giftId: Int,
            val count: Int,
            val timestamp: Long = System.currentTimeMillis(),
        ) : Event

        data class CharitGiftEvent(
            val begId: Int,
            val timestamp: Long = System.currentTimeMillis(),
        ) : Event
    }
}

@Serializable
data class SendGiftFromWallEvent(
    val userId: Int,
    val giftIds: List<Int>,
)
