package com.buque.wakoo.viewmodel

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.isRefreshing
import kotlinx.coroutines.launch

abstract class CSViewModel<ST> : BaseViewModel() {
    protected val rawState: MutableState<CState<ST>> = mutableStateOf(CState.Idle)

    val state: State<CState<ST>> = rawState

    fun refreshState() {
        if (rawState.value.isRefreshing) return
        if (rawState.value is CState.Idle) {
            rawState.value = CState.Loading()
        } else if (rawState.value is CState.Success) {
            rawState.value = (rawState.value as CState.Success<ST>).copy(isRefreshing = true)
        }
        loadData()
    }

    private fun loadData() {
        viewModelScope.launch {
            rawState.value = loadState()
            afterLoadState()
        }
    }

    protected open suspend fun afterLoadState() {
    }

    abstract suspend fun loadState(): CState<ST>
}
