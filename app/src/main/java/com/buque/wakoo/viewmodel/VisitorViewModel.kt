package com.buque.wakoo.viewmodel

import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.VisitorRecordResponse
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.ui.widget.state.CState

class VisitorViewModel : ListPaginateViewModel<Any, Int, UserResponse, VisitorRecordResponse>() {
    var isVip: Boolean = false
        private set

    override fun getFirstPageKey(dataKey: Any): Int = 1

    override fun getNextPageKey(
        cState: CState<List<UserResponse>>,
        dataKey: Any,
        pageKey: Int?,
    ): Int = (pageKey ?: 1) + 1

    override suspend fun getData(
        reqKey: Any,
        dataKey: Any,
        pageKey: Int,
        pageSize: Int,
    ): ApiResponse<VisitorRecordResponse> = UserApiService.instance.getVisitors(if (SelfUser?.isVip == true) 1 else 0, pageKey, pageSize)

    override fun getDataListFromResponse(
        dataKey: Any,
        response: VisitorRecordResponse,
    ): List<UserResponse> {
        this.isVip = response.isVip
        return response.list
    }

    override fun getHasNextPageKeyFromResponse(
        dataKey: Any,
        pageKey: Int,
        response: VisitorRecordResponse,
        distinctAddSize: Int,
    ): Int? =
        if (response.list.size >= defaultPageSize) {
            pageKey + 1
        } else {
            null
        }

    override fun getDistinctSelector(): (UserResponse) -> String =
        {
            it.id
        }
}
