package com.buque.wakoo.viewmodel.chatgroup

import androidx.compose.ui.unit.Density
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.bean.SendParams
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.UIMessageEntry
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.message.types.UCGiftMessage
import com.buque.wakoo.im_business.message.types.UCImageMessage
import com.buque.wakoo.im_business.message.types.UCTextMessage
import com.buque.wakoo.im_business.message.types.UCVoiceMessage
import com.buque.wakoo.im_business.message.ui.entry.ChatGroupMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.MsgUIEntry
import com.buque.wakoo.im_business.viewmodel.IMMessageConfig
import com.buque.wakoo.im_business.viewmodel.ListStateMessageViewModel
import com.buque.wakoo.manager.EnvironmentManager

class ChatGroupViewModel(
    imId: String,
    private val density: Density,
    hasAddNewMsgTag: Boolean = false,
    fissionGiftMessageEnable: Boolean = false,
) : ListStateMessageViewModel(
    sendParams = SendParams(receiver = imId, type = ConversationType.GROUP),
    config =
        IMMessageConfig(
            hasLoadUnreadMsgInfo = true,
            hasAddNewMsgTag = hasAddNewMsgTag,
            fissionGiftMessageEnable = fissionGiftMessageEnable,
        ),
) {
    override fun filterShownMessage(message: UCInstanceMessage): Boolean =
        when (message) {
            is UCTextMessage, is UCImageMessage, is UCVoiceMessage, is UCGiftMessage -> true
            is UCCustomMessage -> {
                when (message.cmd) {
                    IMEvent.GROUP_ACTION_HINT, IMEvent.GROUP_NOTICE_ACTION_CARD -> true
                    IMEvent.TRIBE_COMMON_RICH_TEXT -> false
                    IMEvent.GROUP_COMMON_RICH_TEXT -> true
                    else -> false
                }
            }

            else -> false
        }

    override fun sceneConvertToUIMessageEntry(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
        oldEntry: UIMessageEntry?,
    ): MsgUIEntry? = ChatGroupMsgEntry.from(message, density)
}
