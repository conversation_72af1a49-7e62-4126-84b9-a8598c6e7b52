package com.buque.wakoo.viewmodel.liveroom

import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.app.Const
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.bean.GiftBean
import com.buque.wakoo.bean.UserRichList
import com.buque.wakoo.bean.message.BBoxInfo
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.ext.getIntOrNull
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.bean.IMUser
import com.buque.wakoo.im.bean.SendParams
import com.buque.wakoo.im.isSent
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.UIMessageEntry
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.message.types.UCGiftMessage
import com.buque.wakoo.im_business.message.types.UCTextMessage
import com.buque.wakoo.im_business.message.types.UCUnknownMessage
import com.buque.wakoo.im_business.message.ui.entry.HongBaoMessageEntry
import com.buque.wakoo.im_business.message.ui.entry.LiveRoomSystemMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.LiveRoomUserSystemMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.MsgUIEntry
import com.buque.wakoo.im_business.message.ui.entry.NoProviderEntry
import com.buque.wakoo.im_business.message.ui.entry.RecallMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.TextMsgEntry
import com.buque.wakoo.im_business.viewmodel.IMMessageConfig
import com.buque.wakoo.im_business.viewmodel.ListStateMessageViewModel
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.network.api.bean.PKEvent
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.ui.screens.liveroom.InputTextState
import com.buque.wakoo.ui.screens.liveroom.LiveMicMode
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.panel.LiveInviteUpMicPanel
import com.buque.wakoo.ui.screens.messages.chat.BBoxDialogContent
import com.buque.wakoo.ui.widget.gift.GiftSendParams
import com.buque.wakoo.utils.LogUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject

class LiveRoomViewModel(
    basicInfo: BasicRoomInfo,
) : ListStateMessageViewModel(
    sendParams = SendParams(basicInfo.imId, ConversationType.CHATROOM),
    config =
        IMMessageConfig(
            loadHistoryEnable = LiveRoomManager.roomHasCollapseHistory(basicInfo.id),
            insetTimeLine = false,
            autoCleanUnreadCount = false,
            autoScrollLatestAndFirstVisibleCount = 4,
            smoothScroll = false,
        ),
) {
    class Factory(
        private val basicInfo: BasicRoomInfo,
    ) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T = LiveRoomViewModel(basicInfo) as T
    }

    private val repository = LiveRoomManager.createLiveRoomRepository(basicInfo)

    val roomId get() = roomInfoState.id

    val roomInfoState get() = repository.roomInfoState

    private val inputTextStateFlow = MutableStateFlow<InputTextState>(InputTextState.Hidden)

    val inputTextState: State<InputTextState>
        @Composable get() = inputTextStateFlow.collectAsStateWithLifecycle()

    fun setInputTextState(state: InputTextState) {
        inputTextStateFlow.value = state
    }

    fun refreshRoomInfo() {
        repository.refreshRoomInfo()
    }

    fun sendEvent(event: RoomEvent) {
        repository.sendEvent(event)
    }

    fun restoreRtcSettings() {
        repository.restoreRtcSettings()
    }

    private val packetEnable: Boolean
        get() = AppConfigManager.uiConfigFlow.value.packetEnabled

    /**
     * 收到的新消息
     */
    override fun onRecvNewCustomMessage(
        message: UCCustomMessage,
        offline: Boolean,
    ) {
        super.onRecvNewCustomMessage(message, offline)
        LogUtils.d("rec room msg [${message.cmd}]:\n${message.getSummaryString()}")
        viewModelScope.launch {
            when (message.cmd) {
                IMEvent.RED_PACKET_START_GRAB -> {
                    if (!packetEnable) return@launch
                    val hbEl = message.getJsonValue<JsonObject>("red_packet") ?: return@launch
                    val id = hbEl.getIntOrNull("id") ?: return@launch
                    sendEvent(RoomEvent.ShowHongBao(id.toString()))
                }

                IMEvent.INVITE_MIC -> { // 邀请上麦
                    if (message.getJsonValue<BasicUser>("invited_user")?.sIsSelf == true) {
                        message.getJsonValue<BasicUser>("admin_user")?.also { user ->
                            sendEvent(
                                RoomEvent.PanelDialog {
                                    LiveInviteUpMicPanel(
                                        user = user,
                                        roomInfoState = roomInfoState,
                                    )
                                },
                            )
                        }
                    }
                }

                IMEvent.AGREE_MIC -> { // 同意上麦
                    showToast("你已被同意上麦".localized)
                    sendEvent(RoomEvent.AgreeUpMic(2))
                }

                IMEvent.REFUSE_MIC -> { // 拒绝上麦
                    showToast("你的上麦请求被拒绝".localized)
                }

                IMEvent.CP_BLIND_BOX_DURATION_SYNC -> {
                    val info = message.parseDataJson<BBoxInfo>()
                    if (info != null) {
                        if (info.isComplete && info.showPopup) {
                            val leftUser = info.userInfo ?: return@launch
                            val rightUser = info.cpUserInfo ?: return@launch
                            val message = "恭喜【%s】和【%s】获得1个CP盲盒！".localizedFormat(leftUser.name, rightUser.name)
                            val recId = if (leftUser.sIsSelf) rightUser.id else leftUser.id
                            sendEvent(
                                RoomEvent.CustomDialog {
                                    BBoxDialogContent(
                                        leftUser,
                                        rightUser,
                                        message,
                                        "开盲盒送给CP".localized,
                                        "%s钻石".localizedFormat(info.price),
                                        onClick = {
                                            sendEvent(
                                                RoomEvent.SendGift(
                                                    listOf(recId),
                                                    GiftBean(id = info.giftId, name = "", icon = "", price = info.price),
                                                    GiftSendParams(),
                                                ),
                                            )
                                            dismiss()
                                        },
                                    )
                                },
                            )
                        }
                    }
                }
            }
        }
    }

    /**
     * 过滤出需要显示的消息
     */
    override fun filterShownMessage(message: UCInstanceMessage): Boolean {
        if (message is UCUnknownMessage) {
            return false
        }

        if (message !is UCCustomMessage) {
            return message is UCTextMessage || message is UCGiftMessage
        }

        var filter = false
        message.apply {
            when (cmd) {
                IMEvent.USER_ENTRANCE -> {
                    message.getJsonValue<UserResponse>("user")?.also {
                        filter = !it.isHidden
                    }
                }

                IMEvent.ROOM_SETTINGS -> {
                    filter =
                        when (message.getJsonString("settings_name")) {
                            Const.RoomInfoChangeKey.ROOM_MODE,
                            Const.RoomInfoChangeKey.MIC_MODE,
                                -> true

                            else -> false
                        }
                }

                IMEvent.AGREE_MIC -> {
                    val sendUser = message.getJsonValue<UserResponse>("admin_user")
                    val targetUser = message.getJsonValue<UserResponse>("apply_user")
                    sendUser?.also {
                        if (targetUser?.id == SelfUser?.id) {
                            filter = true
                        }
                    }
                }

                IMEvent.REFUSE_MIC -> {
                    val sendUser = message.getJsonValue<UserResponse>("admin_user")
                    val targetUser = message.getJsonValue<UserResponse>("apply_user")
                    sendUser?.also {
                        if (targetUser?.id == SelfUser?.id) {
                            filter = true
                        }
                    }
                }

                IMEvent.RED_PACKET_CREATED -> {
                    filter = packetEnable && message.getJsonString("red_packet_id")?.isNotEmpty() == true
                }

                IMEvent.FOLLOW_CHATROOM_PUBLIC_MESSAGES -> {
                    filter = SelfUser?.type != 0
                }

                IMEvent.COMMON_CHATROOM_PUBLIC_MESSAGES -> {
                    filter = message
                        .getJsonValue<List<UserRichList>>("messages")
                        ?.any {
                            if (it.code == Const.BusinessCode.hongbao) {
                                packetEnable
                            } else {
                                true
                            }
                        } == true
                }

                IMEvent.TEAM_PK_EVENT -> {
                    val pkEvent = message.parseDataJson<PKEvent>()
                    if (pkEvent != null && pkEvent.action == "end") {
                        filter = true
                    }
                }
            }
        }

        return filter
    }

    override fun createRecallUIMessageEntry(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
        oldEntry: UIMessageEntry?,
    ): MsgUIEntry {
        val user = message.user
        return RecallMsgEntry(
            buildAnnotatedString {
                if (message.isSent) {
                    append("你撤回了一条消息".localized)
                } else if (user != null) {
                    withStyle(SpanStyle(color = Color(0xFFFFD683))) {
                        append(user.name)
                    }
                    append(" ")
                    append("撤回了一条消息".localized)
                } else {
                    append("系统撤回了一条消息".localized)
                }
            },
        )
    }

    override fun sceneConvertToUIMessageEntry(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
        oldEntry: UIMessageEntry?,
    ): MsgUIEntry? =
        when (message) {
            is UCTextMessage -> {
                TextMsgEntry(message.text, message.requireUser())
            }

            is UCCustomMessage -> {
                when (message.cmd) {
                    IMEvent.USER_ENTRANCE -> {
                        val user = message.getJsonValue<BasicUser>("user")!!
                        LiveRoomUserSystemMsgEntry(
                            content =
                                buildAnnotatedString {
                                    withStyle(SpanStyle(color = Color(0xFFFFD683))) {
                                        append(user.name)
                                    }
                                    append(" ")
                                    append("进入了房间".localized)
                                },
                            user = user,
                        )
                    }

                    IMEvent.ROOM_SETTINGS -> {
                        when (message.getJsonString("settings_name")) {
                            Const.RoomInfoChangeKey.ROOM_MODE -> {
                                LiveRoomSystemMsgEntry(
                                    buildAnnotatedString {
                                        append("房间已切换为".localized)
                                        withStyle(SpanStyle(color = Color(0xFFFFD683))) {
                                            append(LiveRoomMode.valueOf(message.getJsonInt("value", -1)).stringOf())
                                        }
                                    },
                                )
                            }

                            Const.RoomInfoChangeKey.MIC_MODE -> {
                                LiveRoomSystemMsgEntry(
                                    buildAnnotatedString {
                                        append("麦位已切换为".localized)
                                        withStyle(SpanStyle(color = Color(0xFFFFD683))) {
                                            append(LiveMicMode.valueOf(message.getJsonInt("value", -1)).stringOf())
                                        }
                                    },
                                )
                            }

                            else -> NoProviderEntry
                        }
                    }

                    IMEvent.AGREE_MIC -> {
                        val sendUser = message.getJsonValue<BasicUser>("admin_user")
                        LiveRoomSystemMsgEntry(
                            buildAnnotatedString {
                                withStyle(SpanStyle(color = Color(0xFFFFD683))) {
                                    append(sendUser?.name)
                                }
                                append(" ")
                                append("同意了你的上麦申请".localized)
                            },
                        )
                    }

                    IMEvent.REFUSE_MIC -> {
                        val sendUser = message.getJsonValue<BasicUser>("admin_user")
                        LiveRoomSystemMsgEntry(
                            buildAnnotatedString {
                                withStyle(SpanStyle(color = Color(0xFFFFD683))) {
                                    append(sendUser?.name)
                                }
                                append(" ")
                                append("拒绝了你的上麦申请".localized)
                            },
                        )
                    }

                    IMEvent.RED_PACKET_CREATED -> {
                        val id = message.getJsonString("red_packet_id").orEmpty()
                        val title = message.getJsonString("red_packet_greets").orEmpty()
                        val user = message.getJsonValue<UserResponse>("sender") ?: return null
                        HongBaoMessageEntry(IMUser.fromResponse(user), id, title)
                    }

                    IMEvent.TEAM_PK_EVENT -> {
                        val pkEvent = message.parseDataJson<PKEvent>()
                        LiveRoomSystemMsgEntry(
                            buildAnnotatedString {
                                withStyle(SpanStyle(color = Color(0xFFFFA5A5))) {
                                    append(pkEvent?.digest.orEmpty())
                                }
                            },
                        )
                    }

                    else -> {
                        null
                    }
                }
            }

            else -> null
        }
}
