package com.buque.wakoo.viewmodel

import androidx.lifecycle.viewModelScope
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.bean.user.UserProfileInfo
import com.buque.wakoo.consts.SceneType
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.UserManager
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.repository.UserRepository
import com.buque.wakoo.utils.UpdateAction
import com.buque.wakoo.utils.evolveWith
import com.buque.wakoo.utils.handleOptimisticRequest
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject

class UserProfileViewModel constructor(
    initialValue: UserProfileInfo,
) : BaseViewModel() {
    constructor(user: User) : this(UserProfileInfo(user))

    constructor(uid: String) : this(BasicUser.fromUid(uid))

    private val userId = initialValue.id

    private val isSelf = initialValue.sIsSelf

    private val userRepository = UserRepository()

    /**
     * 用于更新
     */
    private val updateValueFlow = MutableSharedFlow<UpdateAction<UserProfileInfo>>()

    val userProfileInfoFlow =
        UserManager
            .getUserInfoFlow(userId, initialValue.isEmpty)
            .map {
                UserProfileInfo.fromResponse(it)
            }.evolveWith(initialValue, updateValueFlow)
            .stateIn(viewModelScope, SharingStarted.Eagerly, initialValue)

    private val info: UserProfileInfo
        get() = userProfileInfoFlow.value

    private var followRequesting = false

    fun refresh() {
        UserManager.refreshUserInfo(userId)
    }

    fun toggleFollowState() {
        viewModelScope.launch {
            if (!info.withRelationInfo.isEmpty) {
                handleOptimisticRequest(
                    getItem = {
                        info.withRelationInfo
                    },
                    setItem = {
                        updateValueFlow.emit { value ->
                            value.copy(withRelationInfo = it)
                        }
                    },
                    getProperty = { it.isFollowed },
                    isRequesting = { followRequesting },
                    copy = { item, isFollowed, isRequesting ->
                        followRequesting = isRequesting
                        item.copy(isFollowed = isFollowed)
                    },
                    apiCall = { isFollow -> userRepository.updateFollowState(userId, isFollow).isSuccess },
                )
            }
        }
    }

    suspend fun updateBlackState(isBlack: Boolean): Result<JsonObject> = userRepository.updateBlackState(userId, isBlack)

    /**
     * 删除
     */
    suspend fun removeVoiceIntro() {
        if (!isSelf) {
            return
        }
        executeApiCallExpectingData {
            UserApiService.instance.deleteUserSoundBrand()
        }.onSuccess {
            updateValueFlow.emit { value ->
                value.copy(
                    socialInfo =
                        value.socialInfo.copy(
                            soundBrand = null,
                        ),
                )
            }
            AccountManager.updateSelfSocialInfo {
                it.copy(soundBrand = null)
            }
        }
    }

    suspend fun addFriendFromPrivateChat() {
        GlobalRepository.relationRepo
            .addFriend(userId, SceneType.WAKOO_PRIVATE_CHAT, userId)
            .onSuccess {
                refresh()
            }
    }
}
