package com.buque.wakoo.viewmodel

import androidx.lifecycle.viewModelScope
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.AreaRecommendUser
import com.buque.wakoo.bean.RecommendUserList
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.repository.UserRepository
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.utils.handleOptimisticRequest
import kotlinx.coroutines.launch

class RecommendUserViewModel : ListPaginateViewModel<Any, Int, AreaRecommendUser, RecommendUserList>() {
    private val repo by lazy {
        UserRepository()
    }

    var onlyShowSameCity = 0
        private set

    suspend fun toggleRegionType(type: Int): RefreshResult<Int> {
        if (SelfUser?.isJP == false || onlyShowSameCity == type) {
            return RefreshResult(false, null)
        }
        val oldType = onlyShowSameCity
        onlyShowSameCity = type
        val ret = refreshList(Any(), true)
        if (!ret.success) {
            onlyShowSameCity = oldType
        }
        return ret
    }

    override fun getFirstPageKey(dataKey: Any): Int = 1

    override fun getNextPageKey(
        cState: CState<List<AreaRecommendUser>>,
        dataKey: Any,
        pageKey: Int?,
    ): Int = (pageKey ?: 0) + 1

    override suspend fun getData(
        reqKey: Any,
        dataKey: Any,
        pageKey: Int,
        pageSize: Int,
    ): ApiResponse<RecommendUserList> =
        UserApiService.instance.getRecommendUserListByRegion(
            page = pageKey,
            region =
                if (SelfUser?.isJP == true) {
                    onlyShowSameCity
                } else {
                    null
                },
        )

    override fun getDataListFromResponse(
        dataKey: Any,
        response: RecommendUserList,
    ): List<AreaRecommendUser> = response.userList

    override fun getHasNextPageKeyFromResponse(
        dataKey: Any,
        pageKey: Int,
        response: RecommendUserList,
        distinctAddSize: Int,
    ): Int? = if (response.hasNext) pageKey + 1 else null

    override fun getDistinctSelector(): (AreaRecommendUser) -> String =
        {
            it.userid.toString()
        }

    fun updateFollowStatus(user: AreaRecommendUser) {
        viewModelScope.launch {
            val state = getCState(Unit)
            if (state is CState.Success) {
                handleOptimisticRequest(
                    itemId = user.userid,
                    items = state.data,
                    findItemIdFromData = { it.userid },
                    getProperty = { it.isFollowed },
                    isRequesting = { it.relationRequesting },
                    copy = { item, isFollow, isRequesting ->
                        item.copy(isFollowed = isFollow, relationRequesting = isRequesting)
                    },
                    apiCall = { isFollow ->
                        repo
                            .updateFollowState(
                                user.userid.toString(),
                                isFollow,
                            ).isSuccess
                    },
                )
            }
        }
    }
}
