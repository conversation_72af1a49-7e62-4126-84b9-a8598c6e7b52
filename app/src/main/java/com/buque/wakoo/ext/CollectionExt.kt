package com.buque.wakoo.ext

inline fun <T> List<T>.forEachReversed(action: (T) -> Unit) {
    for (i in this.indices.reversed()) {
        action(this[i])
    }
}

inline fun <T> List<T>.forEachReversedIndexed(action: (index: Int, T) -> Unit) {
    for (i in this.indices.reversed()) {
        action(i, this[i])
    }
}

val Collection<*>?.sizeOrZero: Int
    get() = this?.size ?: 0

fun <T> List<T>.safeIndexOf(
    element: @UnsafeVariance T,
    defaultIndex: () -> Int,
): Int = indexOf(element).takeIf { it > -1 } ?: defaultIndex()
