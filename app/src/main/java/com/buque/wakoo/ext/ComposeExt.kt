package com.buque.wakoo.ext

import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedDispatcher
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imeAnimationTarget
import androidx.compose.foundation.layout.isImeVisible
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisallowComposableCalls
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.DisposableEffectResult
import androidx.compose.runtime.DisposableEffectScope
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.produceState
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.Dp
import androidx.core.view.drawToBitmap
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.LocalAppNavController
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import kotlin.reflect.KProperty

@Composable
fun Modifier.noEffectClick(
    enabled: Boolean = true,
    onClickLabel: String? = null,
    role: Role? = null,
    onClick: () -> Unit,
) = clickable(
    interactionSource = remember { MutableInteractionSource() },
    indication = null,
    enabled = enabled,
    onClickLabel = onClickLabel,
    role = role,
    onClick = onClick,
)

@Composable
fun Modifier.noRippleCombinedClickable(
    enabled: Boolean = true,
    onClickLabel: String? = null,
    role: Role? = null,
    onLongClickLabel: String? = null,
    onLongClick: (() -> Unit)? = null,
    onDoubleClick: (() -> Unit)? = null,
    hapticFeedbackEnabled: Boolean = true,
    onClick: () -> Unit,
) = combinedClickable(
    interactionSource = remember { MutableInteractionSource() },
    indication = null,
    enabled = enabled,
    onClickLabel = onClickLabel,
    role = role,
    onLongClickLabel = onLongClickLabel,
    onLongClick = onLongClick,
    onDoubleClick = onDoubleClick,
    hapticFeedbackEnabled = hapticFeedbackEnabled,
    onClick = onClick,
)

val onBackPressedDispatcher: OnBackPressedDispatcher?
    @Composable
    get() = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher

val onBackInvoker: () -> Unit
    @Composable
    get() =
        with(onBackPressedDispatcher) {
            if (this == null) {
                {}
            } else {
                remember(this) {
                    {
                        this.onBackPressed()
                    }
                }
            }
        }

val onRootPopInvoker: (AppNavKey?) -> Unit
    @Composable
    get() =
        with(LocalAppNavController.root) {
            remember(this) {
                {
                    if (it != null) {
                        popIf(it)
                    } else {
                        pop()
                    }
                }
            }
        }

val onPopInvoker: (AppNavKey?) -> Unit
    @Composable
    get() =
        with(LocalAppNavController.current) {
            remember(this) {
                {
                    if (it != null) {
                        popIf(it)
                    } else {
                        pop()
                    }
                }
            }
        }

@Composable
inline fun <reified T : AppNavKey> typeRootPopInvoker(): () -> Unit =
    with(LocalAppNavController.root) {
        remember(this) {
            {
                popIs<T>()
            }
        }
    }

@Composable
inline fun <reified T : AppNavKey> typePopInvoker(): () -> Unit =
    with(LocalAppNavController.current) {
        remember(this) {
            {
                popIs<T>()
            }
        }
    }

@Composable
fun <T> keepLastNonNullState(newState: T?): T? {
    val lastState =
        remember {
            mutableStateOf<T?>(null)
        }
    return if (newState == null) {
        lastState.value
    } else {
        SideEffect {
            lastState.value = newState
        }
        newState
    }
}

@Composable
inline fun <T> rememberRefWithPrevious(
    key1: Any?,
    crossinline calculation: @DisallowComposableCalls (T?) -> MutableState<T>,
): MutableState<T> {
    val cacheRef = rememberRef<T>()
    val state =
        remember(key1) {
            calculation(cacheRef.value)
        }
    SideEffect {
        cacheRef.value = state.value
    }
    return state
}

@Composable
inline fun <T> rememberSaveableRefWithPrevious(
    key1: Any?,
    crossinline calculation: @DisallowComposableCalls (T?) -> MutableState<T>,
): MutableState<T> {
    if (LocalInspectionMode.current) {
        return remember(key1) {
            calculation(null)
        }
    }
    val cacheRef = rememberSaveableRef<T>()
    val state =
        rememberSaveable(key1) {
            calculation(cacheRef.value)
        }
    SideEffect {
        cacheRef.value = state.value
    }
    return state
}

@Composable
fun <T> rememberSaveableRef(): MutableState<T?> {
    // for some reason it always recreated the value with vararg keys,
    // leaving out the keys as a parameter for remember for now
    return rememberSaveable {
        mutableStateOf(null)
    }
}

operator fun <T> ConsumableState<T>.getValue(
    thisObj: Any?,
    property: KProperty<*>,
): T = value

/**
 * 一个只能被“消耗”一次的状态。
 * 首次读取 `value` 时，返回 `initialValue` 并将状态标记为已消耗。
 * 后续所有读取都将返回 `consumedValue`。
 *
 * @param T 状态值的类型。
 * @param initialValue 首次读取时返回的值。
 * @param consumedValue 消耗后返回的值。
 */
data class ConsumableState<T>(
    private val initialValue: T,
    private val consumedValue: T,
) {
    private var isConsumed = false

    val value: T
        get() {
            return if (!isConsumed) {
                isConsumed = true
                initialValue
            } else {
                consumedValue
            }
        }
}

/**
 * 创建并记住一个 `ConsumableState`。
 * 主要用于处理那些只需要在首次组合/读取时触发一次的逻辑。
 *
 * 例如，判断是否是首次进入某个 Composable。
 *
 * @param initialValue 首次读取时返回的值。
 * @param consumedValue 消耗后返回的值。
 */
@Composable
fun <T> rememberConsumableState(
    initialValue: T,
    consumedValue: T,
): ConsumableState<T> =
    remember {
        ConsumableState(initialValue, consumedValue)
    }

@Composable
fun rememberIsFirstComposition(): ConsumableState<Boolean> = rememberConsumableState(initialValue = true, consumedValue = false)

@Composable
fun rememberFirstEnter(): State<Boolean> =
    rememberSaveable {
        mutableStateOf(true)
    }.also {
        LaunchedEffect(Unit) {
            it.value = false
        }
    }

@Composable
fun rememberIsRestore(): State<Boolean> {
    val isRestore =
        remember {
            mutableStateOf(true)
        }
    rememberSaveable {
        isRestore.value = false
        mutableStateOf(false)
    }
    return isRestore
}

@Composable
fun <T> rememberRef(): MutableState<T?> {
    // for some reason it always recreated the value with vararg keys,
    // leaving out the keys as a parameter for remember for now
    return remember {
        mutableStateOf(null)
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun imeVisible(minKeyboardHeight: Dp): State<Boolean> {
    val density = LocalDensity.current
    val imeAnimationTarget = WindowInsets.imeAnimationTarget
    return produceState(initialValue = WindowInsets.isImeVisible, imeAnimationTarget, density, minKeyboardHeight) {
        snapshotFlow {
            with(density) {
                imeAnimationTarget.getBottom(this) >= minKeyboardHeight.roundToPx()
            }
        }.collectLatest {
            value = it
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun imeHeight(): State<Int> {
    val density = LocalDensity.current
    val ime = WindowInsets.ime
    return produceState(initialValue = ime.getBottom(density), density) {
        snapshotFlow {
            ime.getBottom(density)
        }.collectLatest {
            value = it
        }
    }
}

/**
 * 一个自定义的 Modifier，当在其应用的组件上发生点击时，
 * 会清除当前焦点，通常用于在点击背景时隐藏键盘。
 */
fun Modifier.hideKeyboardOnClickOutside(): Modifier =
    composed {
        val focusManager = LocalFocusManager.current
        this.clickable(
            interactionSource = remember { MutableInteractionSource() },
            indication = null,
        ) {
            focusManager.clearFocus()
        }
    }

fun Modifier.conditional(
    condition: Boolean,
    modifier: Modifier.() -> Modifier,
): Modifier =
    if (condition) {
        this.modifier()
    } else {
        this
    }

@Composable
fun LaunchOnceEffect(
    key: Any? = null,
    block: suspend CoroutineScope.() -> Unit,
) {
    var launched by rememberSaveable(key) {
        mutableStateOf(false)
    }
    if (!launched) {
        LaunchedEffect(key1 = key) {
            try {
                block()
            } finally {
                launched = true
            }
        }
    }
}

@Composable
fun DisposableOnceEffect(
    key: Any? = null,
    effect: DisposableEffectScope.(Boolean) -> DisposableEffectResult,
) {
    var firstEnter by rememberSaveable {
        mutableStateOf(true)
    }
    DisposableEffect(key) {
        effect(
            firstEnter.also {
                firstEnter = false
            },
        )
    }
}

fun createBitmapFromComposable(
    context: Context,
    width: Int = 0,
    height: Int = 0,
    content: @Composable () -> Unit,
    callback: (Bitmap?) -> Unit,
) {
    appCoroutineScope.launch {
        val composeView =
            ComposeView(context).apply {
                setContent { content() }
            }

        val latch = CountDownLatch(1)

        composeView.addOnAttachStateChangeListener(
            object : View.OnAttachStateChangeListener {
                override fun onViewAttachedToWindow(v: View) {
                    latch.countDown()
                    composeView.removeOnAttachStateChangeListener(this)
                }

                override fun onViewDetachedFromWindow(v: View) {}
            },
        )

        // 临时添加到视图层级
        (context as? Activity)?.window?.decorView?.let {
            (it as? ViewGroup)?.addView(composeView)
        }

        try {
            // 等待视图附加
            latch.await(1, TimeUnit.SECONDS)

            // 测量和生成Bitmap
            val widthSpec =
                if (width > 0) {
                    View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY)
                } else {
                    View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
                }

            val heightSpec =
                if (height > 0) {
                    View.MeasureSpec.makeMeasureSpec(height, View.MeasureSpec.EXACTLY)
                } else {
                    View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
                }

            composeView.measure(widthSpec, heightSpec)
            composeView.layout(0, 0, composeView.measuredWidth, composeView.measuredHeight)

            callback(composeView.drawToBitmap())
        } catch (e: Exception) {
            callback(null)
        } finally {
            // 清理
            (context as? Activity)?.window?.decorView?.let {
                (it as? ViewGroup)?.removeView(composeView)
            }
        }
    }
}

fun <T> MutableState<T>.update(function: (T) -> T) {
    value = function(value)
}
