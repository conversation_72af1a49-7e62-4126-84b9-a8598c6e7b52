package com.buque.wakoo.manager

import android.content.res.Resources
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.core.os.ConfigurationCompat
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.DevicesKV
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.ext.getIntOrNull
import com.buque.wakoo.ext.getStringOrNull
import com.buque.wakoo.network.api.service.TranslateApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.utils.FileUtils
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.RecycleStringBuilder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileNotFoundException
import java.util.Formatter
import java.util.Locale

@OptIn(ExperimentalCoroutinesApi::class)
object L10nManager {
    private const val KEY_PREFIX = "wakoo_languageTag_l10n_"
    private const val SPLIT_S = "_##**##_"

    private val downloadPath: String by lazy {
        FileUtils.createDefaultlBubbleDownloadOutputFile(WakooApplication.instance).absolutePath
    }

    @Suppress("ktlint:standard:backing-property-naming")
    private var _currentLocale by mutableStateOf(getCurrentDeviceLocale())

    val languageTag get() = _currentLocale.toLanguageTag()

    private val l10nMaps = mutableStateOf<Map<String, String>?>(null)

    fun getL10nValue(key: String): String? = l10nMaps.value?.get(key)

    fun onConfigurationChanged() {
        val newLocale = getCurrentDeviceLocale()
        if (newLocale != _currentLocale) {
            _currentLocale = newLocale
        }
    }

    init {
        appCoroutineScope.launch(Dispatchers.IO) {
            snapshotFlow {
                languageTag
            }.collectLatest { tag ->
                val key = "$KEY_PREFIX$tag"
                val cache = DevicesKV.getString(key, "")
                var loaded = false
                val list =
                    cache
                        .takeUnless {
                            it.isNullOrBlank()
                        }?.split(SPLIT_S)
                        ?.takeIf { it.size == 2 }
                if (list != null) {
                    val file = File(FileUtils.createFileNameFromUrl(list[1]))
                    if (file.exists()) {
                        loaded = true
                        loadL10Package(tag, file)
                    }
                }
                executeApiCallExpectingData {
                    TranslateApiService.instance.queryLanguagePackage(code = tag)
                }.getOrNull()?.let { json ->
                    var url = json.getStringOrNull("link")?.takeIf { it.isNotEmpty() } ?: return@collectLatest
                    val version = json.getIntOrNull("version") ?: 1
                    if (list != null && version < list[0].toInt()) {
                        if (!loaded) {
                            var url = list[1]
                            val file =
                                DownloadManager
                                    .download(url, downloadPath, FileUtils.createFileNameFromUrl(url), Priority.HIGHEST)
                                    .getOrNull()
                            if (file != null && file.exists()) {
                                loadL10Package(tag, file)
                            }
                        }
                    } else {
                        DevicesKV.putString("$KEY_PREFIX$tag", "$version$SPLIT_S$url")
                        val file =
                            DownloadManager
                                .download(url, downloadPath, FileUtils.createFileNameFromUrl(url), Priority.HIGHEST)
                                .getOrNull()
                        if (file != null && file.exists()) {
                            loadL10Package(tag, file)
                        }
                    }
                }
            }
        }
    }

    private fun loadL10Package(
        tag: String,
        file: File,
    ) {
        val result: Map<String, String>? =
            try {
                // 1. 检查文件是否存在且不为空
                if (file.length() == 0L) {
                    // 这样做比直接调用 readText() 更安全，后者在文件不存在时会抛出异常
                    return
                }

                // 2. 读取文件内容
                val jsonString = file.readText(Charsets.UTF_8)

                // 3. 使用 kotlinx.serialization 解析JSON字符串
                // decodeFromString<Map<String, String>>() 会自动将JSON对象转换为一个Map
                AppJson.decodeFromString<Map<String, String>>(jsonString)
            } catch (e: FileNotFoundException) {
                // 这个异常理论上已被上面的 file.exists() 检查处理，但作为双重保障保留
                null
            } catch (e: Exception) {
                null
            }
        if (result != null) {
            LogUtils.dTag("L10nManager", "加载国际化包成功: $tag")
            l10nMaps.value = result
        } else {
            LogUtils.eTag("L10nManager", "加载国际化包失败: $tag")
        }
    }

    /**
     * 获取当前设备设置的主要语言环境。
     * 使用 ConfigurationCompat.getLocales 是获取多语言支持下的首选语言的最佳方式。
     */
    private fun getCurrentDeviceLocale(): Locale {
        // Resources.getSystem() 确保我们总是获取系统级别的配置，而不是应用覆盖后的配置
        val locales = ConfigurationCompat.getLocales(Resources.getSystem().configuration)
        // locales[0] 是用户在系统设置中的首选语言
        return locales[0] ?: Locale.getDefault()
    }
}

/**
 * 适合不需要占位符，且Key和中文value一致的情况
 */
val String.localized: String
    get() = L10nManager.getL10nValue(this) ?: this

/**
 * 适合需要占位符，key和默认占位符模板一样的情况
 */
fun String.localizedFormat(vararg formatArgs: Any): String {
    val template = localized
    return if (formatArgs.isNotEmpty()) {
        RecycleStringBuilder.string { sBuilder ->
            try {
                Formatter(sBuilder.sBuilder).format(template, *formatArgs)
            } catch (e: Exception) {
                template
            }
        }
    } else {
        template
    }
}

/**
 * 适合不需要占位符，想要自定义key和默认value
 */
fun String.localizedWithKey(key: String = this): String = L10nManager.getL10nValue(key) ?: this

/**
 * 适合需要占位符，且想要自定义key和默认value
 */
fun String.localizedFormatWithKey(
    key: String = this,
    vararg formatArgs: Any,
): String {
    val template = localizedWithKey(key)
    return if (formatArgs.isNotEmpty()) {
        RecycleStringBuilder.string { sBuilder ->
            try {
                Formatter(sBuilder.sBuilder).format(template, *formatArgs)
            } catch (e: Exception) {
                template
            }
        }
    } else {
        template
    }
}
