package com.buque.wakoo.manager

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.app.currentUserKV
import com.buque.wakoo.bean.PendantConfig
import com.buque.wakoo.bean.UIConfig
import com.buque.wakoo.network.api.service.CommonApiService
import com.buque.wakoo.network.api.service.SettingsApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.ui.widget.state.executeStatefulApiCall
import com.buque.wakoo.ui.widget.state.shouldRequestData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

// @Serializable
// data class UIConfig(
//    val showGenderSetting: Boolean = false, // 是否显示性别切换
//    val showPublishVoice: Boolean = false, // 是否显示发布声音
//    val searchBtnExists: Boolean = false, // 是否显示搜索按钮
//    val disCoveryTabs: List<DiscoveryTab> = listOf(), // 发现页tab
//    val showGiftWall: Boolean = false,
//    val decoStoreHasSweat:Boolean = false
// )

data class FeatureConfig(
    val hasNewVersion: Boolean = false, // 是否有新版本
    val androidNewestVersion: String = "", // android新版本
    val defaultCsId: Int = -1, // 客服id
    val officialUserIds: List<Int> = emptyList(), // 官方id
    val publicServiceUids: List<Int> = emptyList(), // 官方id
)

object AppConfigManager : NetworkManager.Callback {
    private const val KEY_CACHE_USER_CONFIG = "cache_key_user_config"
    val uiConfigMutableFlow: MutableStateFlow<CState<UIConfig>> = MutableStateFlow(CState.Idle)

    val uiConfigFlow: StateFlow<UIConfig>
        get() = uiConfigMutableFlow
            .map {
                it.dataOrNull ?: UIConfig.EMPTY
            }
            .stateIn(appCoroutineScope, SharingStarted.Eagerly, getCachedUserConfig() ?: UIConfig.EMPTY)


    private val featureConfigMutableFlow: MutableStateFlow<FeatureConfig> = MutableStateFlow(FeatureConfig())

    val featureConfigFlow: StateFlow<FeatureConfig> = featureConfigMutableFlow.asStateFlow()

    private val _pendantConfigFlow: MutableStateFlow<PendantConfig> = MutableStateFlow(PendantConfig())
    val flowBannerList =
        _pendantConfigFlow
            .map { it.banners }
            .stateIn(appCoroutineScope, SharingStarted.Eagerly, _pendantConfigFlow.value.banners)

    // 301-全局;302-首页；303-发现页；304-消息页；305-我的页；306-任务页（日区）；307-语音房；308-私聊页;
    val flowPendantList =
        _pendantConfigFlow
            .map { it.pendants }
            .stateIn(appCoroutineScope, SharingStarted.Eagerly, _pendantConfigFlow.value.pendants)

    private val blindBoxVisibleState = mutableStateOf(true)
    val bboxVisibleState: State<Boolean> = blindBoxVisibleState

    private val _alertNtfVisibleState = mutableStateOf(true)
    val alertNtfVisibleState: State<Boolean> = _alertNtfVisibleState

    //标记是否根据配置切换过首页tab
    val landState = mutableStateOf(false)

    fun closeNtfAlert() {
        _alertNtfVisibleState.value = false
    }

    fun hideBlindBox() =
        run {
            blindBoxVisibleState.value = false
        }

    init {
        NetworkManager.register(this)
    }

    override fun onNetworkAvailable() {
        if (uiConfigFlow.value == UIConfig.EMPTY) {
            fetchAppConfig()
        }
    }

     fun getCachedUserConfig(): UIConfig? {
        return currentUserKV.getString(KEY_CACHE_USER_CONFIG, "")?.takeIf { it.isNotEmpty() }?.let {
            try {
                AppJson.decodeFromString<UIConfig>(it)
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }

    fun fetchAppConfig() {

        appCoroutineScope.launch {
            launch {
                getUIConfig(false)
            }

            launch {
                getSystemConfig()
            }

            launch {
                fetchPendantConfig()
            }
        }
    }

    @Composable
    fun rememberUIConfigState(needRefresh: Boolean): State<CState<UIConfig>> {
        LaunchedEffect(Unit) {
            if (uiConfigMutableFlow.value == CState.Idle) {
                getCachedUserConfig()?.also {
                    uiConfigMutableFlow.value = CState.Success(it)
                }
            }
            val shouldRequestData = uiConfigMutableFlow.value.shouldRequestData
            if (needRefresh || shouldRequestData) {
                getUIConfig(!shouldRequestData)
            }
        }
        return uiConfigMutableFlow.collectAsStateWithLifecycle()
    }

    suspend fun getUIConfig(isRefresh: Boolean) =
        executeStatefulApiCall(
            stateFlow = uiConfigMutableFlow,
            isRefresh = isRefresh,
            apiCall = {
                SettingsApiService.instance.getUserConfig()
            },
            transform = {
                val json = AppJson.encodeToString(it.copy(isRemoteData = false))
                currentUserKV.putString(KEY_CACHE_USER_CONFIG, json)
                it
            },
        )

    suspend fun fetchPendantConfig() {
        executeApiCallExpectingData {
            CommonApiService.instance.getNavigationEntries()
        }.onSuccess {
            _pendantConfigFlow.emit(it)
        }
    }

    /**
     * 此接口不需要登录
     */
    suspend fun getSystemConfig() =
        executeApiCallExpectingData {
            SettingsApiService.instance.getSystemConfig()
        }.map {
            withContext(Dispatchers.Default) {
                FeatureConfig(
                    hasNewVersion = it.hasNewVersion,
                    androidNewestVersion = it.androidNewestVersion,
                    defaultCsId = it.defaultCsId,
                    officialUserIds = it.officialUserIds,
                    publicServiceUids = it.publicServiceUids,
                )
            }
        }.onSuccess {
            featureConfigMutableFlow.value = it
        }
}
