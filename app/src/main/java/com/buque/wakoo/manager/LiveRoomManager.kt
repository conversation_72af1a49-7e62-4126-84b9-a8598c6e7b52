package com.buque.wakoo.manager

import android.content.Intent
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.runtime.snapshots.Snapshot
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.app.Const
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.app.currentUserKV
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.bean.message.BBoxInfo
import com.buque.wakoo.ext.keepLastNonNullState
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.navigation.LiveRoomRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.repository.LiveRoomRepository
import com.buque.wakoo.ui.screens.liveroom.LiveMicMode
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.screen.LiveRoomNavCtrlKey
import com.buque.wakoo.viewmodel.liveroom.LiveRoomViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

private class CurrentRoom(
    val id: String,
    val isPrivateRoom: Boolean,
) {
    val isExpandRoom = mutableStateOf(true)

    var hasCollapsed = false

    var blindBoxInfoReceived: Pair<Long, BBoxInfo>? = null
}

@Serializable
data class SaveStateRoom(
    val info: BasicRoomInfo,
    val isExpandRoom: Boolean,
    val hasCollapsed: Boolean,
)

object LiveRoomManager {
    private var currentRoom by mutableStateOf<CurrentRoom?>(null)

    private var currentRepository by mutableStateOf<LiveRoomRepository?>(null)

    private var blockGiftEffect by mutableStateOf(false)

    private val onExitRoom: (String, String?) -> Unit = { roomId, reason ->
        Snapshot.withMutableSnapshot {
            if (currentRoom?.id == roomId) {
                val backRoomId = currentRepository?.getBackToLiveRoomId()
                currentRoom = null
                if (backRoomId.isNullOrBlank()) {
                    currentRepository?.release(reason = reason, destroyRtc = true)
                    currentRepository = null
                    try {
                        AppManager.appActivity?.also {
                            it.stopService(Intent(it, VoiceLiveService::class.java))
                        }
                    } catch (_: Exception) {
                    }
                } else {
                    showToast(reason)
                    joinRoom(roomId = backRoomId, isPrivateRoom = false)
                }
            }
        }
    }

    private val floatBackground by derivedStateOf {
        currentRepository?.roomInfoState?.let {
            if (it.isPrivateRoom) {
                it.extraInfo.privateRoomInfo
                    ?.targetUser
                    ?.avatar
            } else {
                it.basicInfo.owner?.avatar
            }
        }
    }

    val isCollapse: Boolean by derivedStateOf {
        currentRoom?.isExpandRoom?.value == false
    }

    var inFeedPage by mutableStateOf(false)

    val blockGiftEffectEnable
        get() = blockGiftEffect

    init {
        appCoroutineScope.launch {
            snapshotFlow {
                isCollapse && inFeedPage
            }.collectLatest {
                currentRepository?.muteAllRemoteAudio(it)
            }
        }

        appCoroutineScope.launch {
            AccountManager.accountStateFlow.filterNotNull().collectLatest {
                blockGiftEffect = currentUserKV.getBoolean(Const.KVKey.BLOCK_ROOM_GIFT_EFFECT, false)
            }
        }
    }

    fun toggleBlockGiftEffect() {
        currentUserKV.putBoolean(Const.KVKey.BLOCK_ROOM_GIFT_EFFECT, !blockGiftEffect)
        blockGiftEffect = !blockGiftEffect
    }

    fun getCurrentSaveStateRoom(): SaveStateRoom? {
        val currentRoom = currentRoom
        if (currentRoom != null && currentRepository != null) {
            return currentRepository?.roomInfoState?.basicInfo?.let {
                SaveStateRoom(it, currentRoom.isExpandRoom.value, currentRoom.hasCollapsed)
            }
        }
        return null
    }

    fun roomHasCollapseHistory(roomId: String): Boolean =
        if (currentRoom?.id == roomId) {
            currentRoom?.hasCollapsed == true
        } else {
            false
        }

    @Composable
    fun liveRoomBackground() = keepLastNonNullState(floatBackground)

    /**
     * 一般是用于语音房主页之上打开了一个新页面，这个时候也显示悬浮窗
     */
    @Composable
    fun AutoCollapse(roomId: String) {
        if (currentRoom != null && currentRoom?.id == roomId) {
            DisposableEffect(Unit) {
                if (currentRoom != null && currentRoom?.id == roomId) {
                    currentRoom?.isExpandRoom?.value = true
                }
                onDispose {
                    collapseRoom(roomId)
                }
            }
        }
    }

    @Composable
    fun attachLiveRoomViewModel(basicInfo: BasicRoomInfo): LiveRoomViewModel {
        val rootNavController = LocalAppNavController.root

        LaunchedEffect(Unit) {
            snapshotFlow {
                currentRoom == null || currentRoom?.id != basicInfo.id
            }.filter { it }.collectLatest {
                rootNavController.removeIf {
                    it is Route.LiveRoom && it.basicInfo.id == basicInfo.id
                }
            }
        }

        return viewModel<LiveRoomViewModel>(factory = LiveRoomViewModel.Factory(basicInfo)).apply {
            LaunchedEffect(rootNavController, roomInfoState) {
                roomInfoState.events
                    .onEach { event ->
                        if (event is RoomEvent.CollapseRoom) {
                            rootNavController.popIs<Route.LiveRoom>()
                        }
                    }.launchIn(this)
            }
        }
    }

    fun restoreSaveStateRoom(saveStateRoom: SaveStateRoom) {
        if (currentRoom == null && currentRepository == null) {
            currentRepository = LiveRoomRepository(saveStateRoom.info, onExitRoom)
            currentRoom =
                CurrentRoom(saveStateRoom.info.id, saveStateRoom.info.isPrivateRoom).also {
                    it.isExpandRoom.value = saveStateRoom.isExpandRoom
                    it.hasCollapsed = saveStateRoom.hasCollapsed
                }
        }
    }

    /**
     * 加入房间
     * @param isPrivateRoom 是否是私密小屋
     */
    fun joinRoom(
        roomId: String,
        isPrivateRoom: Boolean = false,
        autoUpMic: Boolean = false, // 进入语音房以后是否要自动上麦
    ) {
        if (roomId.isBlank()) {
            return
        }
        joinRoom(
            BasicRoomInfo(
                id = roomId,
                publicId = "",
                title = "",
                owner = null,
                roomMode = if (isPrivateRoom) LiveRoomMode.Private else LiveRoomMode.UnKnown(-1),
                micMode = LiveMicMode.UnKnown(-1),
                desc = null,
                notice = null,
                background = null,
                tagIds = null,
                autoUpMic = autoUpMic,
            ),
        )
    }

    fun joinRoom(basicInfo: BasicRoomInfo) {
        if (basicInfo.id.isBlank()) {
            return
        }
        Snapshot.withMutableSnapshot {
            currentRoom?.takeIf { it.id == basicInfo.id && it.isPrivateRoom == basicInfo.isPrivateRoom }?.also {
                it.isExpandRoom.value = true
            } ?: run {
                currentRepository?.release(reason = null, destroyRtc = false)
                currentRepository =
                    LiveRoomRepository(basicInfo, onExitRoom).also {
                        currentRoom?.blindBoxInfoReceived?.also { pair ->
                            it.restoreBlindBoxInfo(pair)
                        }
                    }
                currentRoom = CurrentRoom(basicInfo.id, basicInfo.isPrivateRoom)
            }

            LocalAppNavController.useRoot?.apply {
                val exists =
                    findNavKeyByPredicate {
                        it is Route.LiveRoom && it.basicInfo.id == basicInfo.id && it.basicInfo.isPrivateRoom == basicInfo.isPrivateRoom
                    }
                if (exists != null && moveToTop(exists)) {
                    LocalAppNavController[LiveRoomNavCtrlKey]?.popUntil {
                        it is LiveRoomRoute.Home
                    }
                    return
                }
                removeAll { it is Route.LiveRoom }
                push(Route.LiveRoom(basicInfo))
            }
        }
    }

    fun expandCurrentRoom() {
        if (currentRoom != null && currentRepository != null) {
            currentRoom?.isExpandRoom?.value = true
            currentRepository?.also { repo ->
                currentRoom?.blindBoxInfoReceived?.also { pair ->
                    repo.restoreBlindBoxInfo(pair)
                }
            }
            LocalAppNavController.useRoot?.apply {
                if (popUntil { it is Route.LiveRoom }) {
                    LocalAppNavController[LiveRoomNavCtrlKey]?.popUntil {
                        it is LiveRoomRoute.Home
                    }
                } else {
                    push(Route.LiveRoom(currentRepository!!.roomInfoState.basicInfo))
                }
            }
        }
    }

    fun exitCurrentRoom() {
        if (currentRoom != null) {
            onExitRoom(currentRoom?.id.orEmpty(), null)
        }
    }

    fun collapseRoom(id: String) {
        if (currentRoom?.id == id) {
            currentRoom?.hasCollapsed = true
            currentRoom?.isExpandRoom?.value = false
        }
    }

    fun createLiveRoomRepository(basicInfo: BasicRoomInfo): LiveRoomRepository =
        currentRepository?.takeIf { it.roomId == basicInfo.id } ?: LiveRoomRepository(basicInfo, onExitRoom)

    fun receiveBlindBoxInfo(info: BBoxInfo) {
        currentRoom?.also {
            it.blindBoxInfoReceived = Pair(System.currentTimeMillis(), info)
        }
    }
}
