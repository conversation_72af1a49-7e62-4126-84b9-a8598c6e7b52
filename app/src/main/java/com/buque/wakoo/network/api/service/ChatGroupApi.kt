package com.buque.wakoo.network.api.service

import com.buque.wakoo.bean.RecommendLiveRoomResponse
import com.buque.wakoo.bean.chatgroup.ChatGroupBean
import com.buque.wakoo.bean.chatgroup.ChatGroupSquareResponse
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

// 定义一个接口ChatGroupApi，用于处理群组相关的API请求
interface ChatGroupApi {
    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<ChatGroupApi>()
        }
    }

    // 获取群组列表
    @GET("api/xya/group/v1/group/list")
    suspend fun getGroupSquareList(
        @Query("last_id") last_id: Int = 0,
    ): ApiResponse<ChatGroupSquareResponse>

    // 创建群组
    @POST("api/xya/group/v1/group/create")
    suspend fun createGroup(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    // 更新群组信息
    @POST("api/xya/group/v1/group/update")
    suspend fun updateGroupInfo(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    // 获取群组详情
    @GET("api/xya/group/v1/group/detail")
    suspend fun getGroupDetail(
        @Query("group_id") groupId: String,
    ): ApiResponse<ChatGroupBean>

    // 申请加入群组
    @POST("api/xya/group/v1/join/apply/create")
    suspend fun applyJoinChatGroup(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    // 获取加入申请列表
    @GET("api/xya/group/v1/join/apply/list")
    suspend fun getJoinApplyList(
        @Query("group_id") groupId: String,
        @Query("last_apply_id") lastId: Int = 0,
    ): ApiResponse<JsonObject>

    @POST("api/xya/group/v1/join/apply/accept")
    suspend fun handleApply(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    @GET("api/xya/group/v1/fellow/list")
    suspend fun getGroupMemberList(
        @Query("group_id") groupId: String,
        @Query("last_fellow_id") lastId: String,
        @Query("last_fellow_is_online") lastMemberIsOnline: Boolean
    ): ApiResponse<JsonObject>

    @POST("api/xya/group/v1/group/exit")
    suspend fun exitGroup(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    @POST("api/xya/group/v1/group/disband")
    suspend fun destroyGroup(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    @POST("api/xya/group/v1/fellow/remove")
    suspend fun kickOutMember(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    @POST("api/xya/group/v1/administrator/add")
    suspend fun setAdmin(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>


    @GET("api/xya/group/v1/fellow/type/list")
    suspend fun getMemberListWithRoles(
        @Query("group_id") groupId: String,
        @Query("roles") roles: String,
        @Query("last_fellow_id") lastFellowId: Int,
    ): ApiResponse<JsonObject>

    @GET("api/xya/group/v1/fellow/search")
    suspend fun searchMember(
        @Query("group_id") groupId: String,
        @Query("keyword") roles: String,
    ): ApiResponse<JsonObject>

    @POST("api/xya/group/v1/group/invite/create")
    suspend fun inviteToChatGroup(@Body body: Map<String, String>): ApiResponse<JsonObject>

    @POST("api/xya/group/v1/group/invite/accept")
    suspend fun acceptInvite(@Body body: Map<String, String>): ApiResponse<JsonObject>


    @GET("api/xya/group/v1/livehouse/list")
    suspend fun getAudioRoomList(
        @Query("group_id") tribeId: String,
        @Query("page") pageNum: Int
    ): ApiResponse<RecommendLiveRoomResponse>
}
