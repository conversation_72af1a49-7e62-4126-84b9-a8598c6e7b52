package com.buque.wakoo.network.api.service

import com.buque.wakoo.bean.ProfileEnums
import com.buque.wakoo.bean.RecommendUserList
import com.buque.wakoo.bean.TargetUserClassBean
import com.buque.wakoo.bean.TopClassBean
import com.buque.wakoo.bean.UserExtendedStatusList
import com.buque.wakoo.bean.VisitorRecordResponse
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.UserListResponse
import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface UserApiService {
    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<UserApiService>()
        }
    }

    /** 更新用户信息 */
    @POST("api/xya/user/v1/info/set")
    suspend fun updateUserInfo(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 获取用户信息 */
    @GET("api/xya/user/v1/user/info")
    suspend fun getUserInfo(
        @Query("userid") userId: String,
        @Query("scene_type") sceneType: Int? = null,
        @Query("scene_id") sceneId: Int? = null,
        @Query("is_using_app") is_using_app: Int? = null,
    ): ApiResponse<UserResponse>

    /** 获取粉丝信息 */
    @GET("api/xya/user/v1/follower/list")
    suspend fun getUserFollowerList(
        @Query("userid") userId: String,
        @Query("last_relation_id") lastId: Int,
    ): ApiResponse<UserListResponse>

    /** 获取关注信息 */
    @GET("api/xya/user/v1/followee/list")
    suspend fun getUserFollowingList(
        @Query("userid") userId: String,
        @Query("last_relation_id") lastId: Int,
    ): ApiResponse<UserListResponse>

    /** 关注用户 */
    @POST("api/xya/user/v1/follow")
    suspend fun followUser(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 拉黑接口 */
    @POST("api/xya/user/v1/user/black")
    suspend fun blackUser(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 拉黑列表 */
    @GET("api/xya/user/v1/black/list")
    suspend fun getBlackList(
        @Query("userid") userId: String,
        @Query("last_relation_id") lastId: Int,
    ): ApiResponse<UserListResponse>

    @GET("api/xya/user/v1/discover/users")
    suspend fun getRecommendUserListByRegion(
        @Query("same_city") region: Int? = null,
        @Query("page_no") page: Int,
    ): ApiResponse<RecommendUserList>

    @GET("api/xya/user/v1/user/short/profile")
    suspend fun getUserBriefProfile(
        @Query("userid") userId: Int,
    ): ApiResponse<UserResponse>

    /**
     * 升级用户资料
     */
    @POST("api/xya/user/v1/profile/update")
    suspend fun updateUserExtraInfo(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /**
     * 升级声音名片
     * voice_intro_url      声音地址
     * voice_intro_duration 声音长度
     */
    @POST("api/xya/user/v1/default/voice_intro")
    suspend fun updateUserSoundBrand(
        @Body body: Map<String, String>,
    ): ApiResponse<UserResponse>

    /**
     * 删除声音名片
     */
    @POST("api/xya/user/v1/default/voice_intro/delete")
    suspend fun deleteUserSoundBrand(): ApiResponse<UserResponse>

    //region 好友相关
    @POST("api/xya/connection/v1/social/friends/add")
    suspend fun addFriend(
        @Body params: Map<String, String>,
    ): ApiResponse<JsonObject>

    @GET("api/xya/connection/v1/social/friends/list")
    suspend fun getFriendList(
        @Query("last_relation_id") lastId: Int = 0,
    ): ApiResponse<UserListResponse>
    //endregion

    @GET("api/xya/user/v1/ja/profile/options")
    suspend fun getJPProfileOptions(): ApiResponse<ProfileEnums>

    @GET("api/xya/user/v1/preuse/check")
    suspend fun getNextAction(
        @Query("scene") scene: String = "0101",
    ): ApiResponse<JsonObject>

    @GET("api/xya/user/v1/chatlist/user/extend/info")
    suspend fun getUserExtendInfo(
        @Query("userids") users: String,
    ): ApiResponse<UserExtendedStatusList>

    @GET("api/xya/member/v1/visitors")
    suspend fun getVisitors(
        @Query("refresh") refresh: Int = 0,
        @Query("page") page: Int = 1,
        @Query("page_size")
        pageSize: Int = 100,
    ): ApiResponse<VisitorRecordResponse>

    @GET("api/xya/topclass/v1/info")
    suspend fun getTopClassInfo(): ApiResponse<TopClassBean>

    @GET("api/xya/topclass/v1/intent_info")
    suspend fun getUserTopClassInfo(
        @Query("target_user_id") user_id: String,
    ): ApiResponse<TargetUserClassBean>

    @GET("api/xya/c2c/v1/session/user/list")
    suspend fun getLatestChatUserList(@Query("last_chat_id") lastChatId: Int): ApiResponse<JsonObject>

    @GET("api/xya/connection/v1/social/friends/list")
    suspend fun getFriendListResp(@Query("last_relation_id") lastId: Int): ApiResponse<JsonObject>
}
