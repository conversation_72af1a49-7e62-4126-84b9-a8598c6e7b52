package com.buque.wakoo.network.api.service

import com.buque.wakoo.bean.DressUpListResponse
import com.buque.wakoo.bean.DressUpMinePropResponse
import com.buque.wakoo.bean.DressUpMineTabResponse
import com.buque.wakoo.bean.DressUpTabsResponse
import com.buque.wakoo.bean.SilverMallResult
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface DressupApiService {
    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<DressupApiService>()
        }
    }

    // 装扮商城样例列表
    @GET("api/xya/decoration/v1/shop/sample/list")
    suspend fun getStoreList(): ApiResponse<DressUpTabsResponse>

    // 装扮商城列表
    @GET("api/xya/decoration/v1/shop/category/list")
    suspend fun getPropList(
        @Query("decoration_type") decorationType: Int,
        @Query("page") page: Int,
        @Query("page_size") pageSize: Int,
    ): ApiResponse<DressUpListResponse>

    /**
     * 购买装扮
     * decoration_type
     * decoration_id
     * days
     */
    @POST("api/xya/decoration/v1/shop/purchase")
    suspend fun buy(
        @Body map: Map<String, @JvmSuppressWildcards Any>,
    ): ApiResponse<JsonObject>

    @GET("api/xya/decoration/v1/my/tab/list")
    suspend fun getMineDressupTabs(): ApiResponse<DressUpMineTabResponse>

    @GET("api/xya/decoration/v1/my/category/list")
    suspend fun getMineDressupList(
        @Query("t") propType: Int,
        @Query("last_id") lastId: Int,
    ): ApiResponse<DressUpMinePropResponse>

    @GET("api/xya/decoration/v1/my/take")
    suspend fun setMineDressupUse(
        @Query("t") propType: Int,
        @Query("prop_id") propId: Int,
        @Query("use") use: Boolean,
    ): ApiResponse<JsonObject>

    @GET("api/xya/moneybag/v1/silver/hall")
    suspend fun getSilverMallInfo(): ApiResponse<SilverMallResult>

    @POST("api/xya/moneybag/v1/silver/exchange")
    suspend fun exchange(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>
}
