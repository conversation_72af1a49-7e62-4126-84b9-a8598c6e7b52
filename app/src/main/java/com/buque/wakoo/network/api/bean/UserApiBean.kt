package com.buque.wakoo.network.api.bean

import com.buque.wakoo.bean.BuddyZone
import com.buque.wakoo.bean.MediaInfo
import com.buque.wakoo.bean.MyLiveRoomInfo
import com.buque.wakoo.bean.NativeProfile
import com.buque.wakoo.bean.chatgroup.UserChatGroup
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.ChatBubble
import com.buque.wakoo.bean.user.CpRelationInfo
import com.buque.wakoo.bean.user.Medal
import com.buque.wakoo.bean.user.UserDecorations
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonNames

/**
{
"status": 0,
"data": {
"album": [],
"short_intro": "",
"online_status": 0,
"is_hidden": false,
"birthday": "2020-01-01",
"fans_cnt": 0,
"follow_cnt": 0,
"friend_cnt": 0,
"attractive_tags": [],
"is_member": false,
"visible_activities": [],
"chat_bubble": null,
"region": "CN",
"region_label": "中国大陆",
"region_reason": 1,
"region_reason_label": "现居地",
"balance": 0,
"best_match_user": null,
"register_timestamp": 1750211197,
"userid": 4393,
"public_id": "105813",
"nickname": "%E6%B6%82%E9%B8%A6%E5%86%92%E9%99%A9%E5%AE%B6%E9%A2%9D",
"avatar_url": "https://s.test.ucoofun.com/aaceEG?x-oss-process=image/format,webp",
"gender": 1,
"age": 5,
"height": 0,
"avatar_frame": "",
"medal": null,
"medal_list": [],
"level": 0,
"country_flag": "https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FCN.png",
"exp_level_info": {
"charm_level": 1,
"wealth_level": 1
},
"have_certified": false,
"native_region": 0,
"need_friend_relation": true,
"is_friend": false,
"can_be_cp": false,
"is_cp": false,
"i_have_followed": false,
"i_have_blacked": false,
"conf_relation_enable": false,
"has_relationship": false,
"relationship_available": false,
"relationship_available_type": "1",
"member": {
"is_valid": false,
"expire_at": 0
},
"can_create_audioroom": false,
"user_audioroom": null,
"group": null,
"gift_wall": {
"total_cnt": 494,
"star_cnt": 0
}
}
}
 */
@Serializable
data class UserResponse(
    @SerialName("age")
    val age: Int = 0, // 5
    @SerialName("avatar_frame")
    val avatarFrame: String = "",
    @SerialName("avatar_url")
    val avatarUrl: String = "", // https://s.test.ucoofun.com/aaceEG?x-oss-process=image/format,webp
    @SerialName("balance")
    @JsonNames("balance", "user_points")
    val balance: Int = 0, // 0
    @SerialName("birthday")
    val birthday: String = "", // 2020-01-01
    @SerialName("can_be_cp")
    val canBeCp: Boolean = false, // false
    @SerialName("can_create_audioroom")
    val canCreateAudioroom: Boolean = false, // false
    @SerialName("user_audioroom")
    val room: MyLiveRoomInfo? = null,
    @SerialName("conf_relation_enable")
    val confRelationEnable: Boolean = false, // false
    @SerialName("country_flag")
    val countryFlag: String = "", // https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FCN.png
    @SerialName("exp_level_info")
    val expLevelInfo: ExpLevelInfo = ExpLevelInfo.Empty,
    @SerialName("fans_cnt")
    val fansCnt: Int = 0, // 0
    @SerialName("follow_cnt")
    val followCnt: Int = 0, // 0
    @SerialName("friend_cnt")
    val friendCnt: Int = 0, // 0
    @SerialName("gender")
    val gender: Int = 0, // 1
    @SerialName("gift_wall")
    val giftWall: GiftWall = GiftWall.EMPTY,
    @SerialName("has_relationship")
    val hasRelationship: Boolean = false, // false
    @SerialName("have_certified")
    val haveCertified: Boolean = false, // false
    @SerialName("height")
    val height: Int = 0, // 0
    @SerialName("i_have_blacked")
    val iHaveBlacked: Boolean = false, // false
    @SerialName("i_have_followed")
    @JsonNames("i_have_followed", "is_follow", "is_followed")
    val iHaveFollowed: Boolean = false, // false
    @SerialName("is_cp")
    val isCp: Boolean = false, // false
    @SerialName("is_friend")
    val isFriend: Boolean = false, // false
    @SerialName("is_hidden")
    val isHidden: Boolean = false, // false
    @SerialName("is_member")
    val isMember: Boolean = false, // false
    @SerialName("level")
    val level: Int = 0, // 0
    @SerialName("member")
    val member: Member = Member.EMPTY,
    @SerialName("native_region")
    val nativeRegion: Int = 0, // 0
    @SerialName("need_friend_relation")
    val needFriendRelation: Boolean = false, // true
    @SerialName("nickname")
    val nickname: String = "", // %E6%B6%82%E9%B8%A6%E5%86%92%E9%99%A9%E5%AE%B6%E9%A2%9D
    @SerialName("online_status")
    val onlineStatus: Int = 0, // 0
    @SerialName("public_id")
    val publicId: String = "", // 105813
    @SerialName("region")
    val region: String = "", // CN
    @SerialName("region_label")
    val regionLabel: String = "", // 中国大陆
    @SerialName("region_reason")
    val regionReason: Int = 0, // 1
    @SerialName("region_reason_label")
    val regionReasonLabel: String = "", // 现居地
    @SerialName("register_timestamp")
    val registerTimestamp: Int = 0, // 1750211197
    @SerialName("relationship_available")
    val relationshipAvailable: Boolean = false, // false
    @SerialName("relationship_available_type")
    val relationshipAvailableType: String = "", // 1
    @SerialName("short_intro")
    val shortIntro: String = "",
    @SerialName("userid")
    val id: String = "0", // 4393
    @SerialName("relation_id")
    @JsonNames("relation_id", "member_id")
    val relationId: Int = 0, // 0
    @SerialName("hqu_type")
    val hquType: Int = 0, // 0 非主播、1 普通主播、2 台湾素人主播
    @SerialName("is_high_quality")
    val isHighQuality: Boolean = false, // 0 非主播、1 普通主播、2 台湾素人主播
    @SerialName("i_follow_back")
    var iFollowBack: Boolean = false, // 我是否回关
    @SerialName("in_blacklist")
    val inRoomBlackList: Boolean? = null, // 是否在房间黑名单列表
    @SerialName("is_admin")
    val isRoomAdmin: Boolean? = null, // 是否在房间管理员列表
    @SerialName("medal_list")
    val medalList: List<Medal>? = null,
    @SerialName("album")
    val albums: List<MediaInfo> = listOf(),
    @SerialName("native_profile")
    val nativeProfile: NativeProfile = NativeProfile.EMPTY,
    @SerialName("moment_images")
    val moments: List<MediaInfo> = listOf(),
    @SerialName("moment_total_count")
    val momentTotalCount: Int = 0,
    @SerialName("sound_count")
    val soundCount: Int = 0,
    @SerialName("sound_like_count")
    val soundLikeCount: Int = 0,
    @SerialName("sound_brand")
    val soundBrand: SoundBrand? = null,
    val group: UserChatGroup? = null,
    val cash: String = "0", // 现金
    val diamond: String = "0", // 积分
    @SerialName("silver_balance")
    val silverBalance: Long = 0,
    @SerialName("cp_card_background")
    val cpCardBackground: String = "",
    @SerialName("cp_card_effect")
    val cpCardEffect: CpRelationInfo.CpCardEffect = CpRelationInfo.CpCardEffect(),
    @SerialName("cp_extra_info")
    val cpExtraInfo: CpRelationInfo.CpExtraInfo = CpRelationInfo.CpExtraInfo(),
    @SerialName("cp_public_billboard_url")
    val cpPublicBillboardUrl: String = "",
    @SerialName("default_cp_rule_url")
    val defaultCpRuleUrl: String = "",
    @SerialName("hide_cp_public_billboard")
    val hideCpPublicBillboard: Boolean = false,
    @SerialName("public_cp")
    val publicCp: BasicUser? = null,
    @SerialName("show_cp_zone")
    val showCpZone: Boolean = false,
    @SerialName("user_has_cp")
    val userHasCp: Boolean = false,
    val cp: BasicUser? = null,
    @SerialName("cp_zone")
    val cpZone: BuddyZone? = null,
    @SerialName("chat_bubble")
    val chatBubble: ChatBubble? = null,
    @SerialName("special_effect_file")
    val entryEffect: String? = null,
    @SerialName("entrance_banner")
    val entranceBanner: String? = null,
    @SerialName("cheer_value") // 助力值,只在pk模式里用到
    val cheerValue: Int? = null,
    @SerialName("is_in_audio_room")
    val isInAudioRoom: Boolean = false,
    @SerialName("room_id")
    val roomId: Int? = null,
    @SerialName("last_visit_at_ts")
    val lastVisitTs: Long? = null,
    @SerialName("location_label")
    val locationLabel: String = "",
    @SerialName("is_top_class_user")
    @JsonNames("is_first_class_user", "is_top_class_user")
    val isTopClassUser: Boolean = false,
    @SerialName("top_class_user_no")
    @JsonNames("top_class_user_no", "first_class_user_no")
    val topClassUserNo: String = "",
    @SerialName("subscript_msg")
    val subScriptMsg: String? = "",
    @SerialName("colorfulNicknameGradient")
    val colorfulNicknameGradient: List<String> = emptyList(),
) {
    val basicUser by lazy { BasicUser.fromResponse(this) }
    val userDecorations by lazy { UserDecorations.fromResponse(this) }
}

@Serializable
data class SoundBrand(
    @SerialName("source_url")
    val sourceUrl: String? = null,
    @SerialName("duration")
    val duration: Long = 0,
)

@Serializable
data class ExpLevelInfo(
    @SerialName("charm_level")
    @JsonNames("charm_level", "appeal_tier")
    val charmLevel: Int = 0, // 1
    @SerialName("wealth_level")
    @JsonNames("wealth_level", "resource_grade")
    val wealthLevel: Int = 0,
) {
    companion object {
        val Empty = ExpLevelInfo()
    }
}

@Serializable
data class GiftWall(
    @SerialName("star_cnt")
    val starCnt: Int = 0, // 0
    @SerialName("total_cnt")
    val totalCnt: Int = 0,
) {
    companion object {
        val EMPTY = GiftWall()
    }
}

@Serializable
data class Member(
    @SerialName("expire_at")
    val expireAt: Long = 0, // 0
    @SerialName("is_valid")
    val isValid: Boolean = false,
) {
    companion object {
        val EMPTY = Member()
    }
}

@OptIn(ExperimentalSerializationApi::class)
@Serializable
data class UserListResponse(
    @SerialName("have_next_page")
    val hasNext: Boolean = true,
    @SerialName("list")
    @JsonNames("list", "blacklist", "followers", "followees", "black_list", "members", "admin_list", "friends")
    val list: List<UserResponse> = listOf(),
)
