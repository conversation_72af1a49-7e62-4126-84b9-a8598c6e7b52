package com.buque.wakoo.network.api.service

import com.buque.wakoo.BuildConfig
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.bean.OssToken
import com.buque.wakoo.bean.PreloadConfigs
import com.buque.wakoo.bean.UIConfig
import com.buque.wakoo.core.pay.GoogleBillingManager
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.AppGlobalSettingsResponse
import com.buque.wakoo.network.executeApiCallExpectingData
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Query

interface SettingsApiService {
    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<SettingsApiService>()
        }

        fun preload(){
            appCoroutineScope.launch {
                executeApiCallExpectingData {
                    instance.getPreloadConfigs()
                }.onSuccess {
                    //init billing
                    GoogleBillingManager.preloadProductDetails(it)

                }
            }
        }
    }

    /**
     * 更新用户信息
     */
    @POST("api/xya/general/v1/advise")
    suspend fun postFeedback(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    /**
     * 上传图片token获取
     */
    @GET("api/xya/general/v1/put/auth")
    suspend fun getUploadToken(
        @Header("Access-Token") token: String?,
    ): ApiResponse<OssToken>

    @Headers("X-Auth-Free: true")
    @GET("api/xya/config/v1/overall/query")
    suspend fun getSystemConfig(
        @Query("version") version: String = BuildConfig.VERSION_NAME,
    ): ApiResponse<AppGlobalSettingsResponse>

    @GET("api/xya/user/v1/user/settings")
    suspend fun getUserConfig(): ApiResponse<UIConfig>

    @GET("api/xya/config/v1/optimize/load")
    suspend fun getPreloadConfigs(): ApiResponse<PreloadConfigs>
}
