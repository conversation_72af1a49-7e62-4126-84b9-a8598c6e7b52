package com.buque.wakoo.network.api.service

import com.buque.wakoo.bean.RankListContainer
import com.buque.wakoo.bean.RecommendLiveRoomResponse
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.CheersGroupListResponse
import com.buque.wakoo.network.api.bean.PrivateRoomInfoResponse
import com.buque.wakoo.network.api.bean.RoomInfoResponse
import com.buque.wakoo.network.api.bean.RoomTokenResponse
import com.buque.wakoo.network.api.bean.UserListResponse
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.network.api.bean.VoiceRoomCreateReq
import com.buque.wakoo.network.api.bean.VoiceRoomCreateResponse
import com.buque.wakoo.network.api.bean.VoiceRoomTagListResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface VoiceRoomApiService {
    companion object {
        val instance by lazy { ApiClient.createuserApiService<VoiceRoomApiService>() }
    }

    /** 语音房标签列表 */
    @GET("api/xya/livehouse/v1/tag/list")
    suspend fun getVoiceRoomTagList(): ApiResponse<VoiceRoomTagListResponse>

    /** 创建语音房 */
    @POST("api/xya/livehouse/v1/livehouse/create")
    suspend fun createVoiceRoom(
        @Body req: VoiceRoomCreateReq,
    ): ApiResponse<VoiceRoomCreateResponse>

    /** 获取语音房信息 */
    @GET("api/xya/livehouse/v1/livehouse/info")
    suspend fun getVoiceRoomInfo(
        @Query("live_house_id") liveHouseId: String,
    ): ApiResponse<RoomInfoResponse>

    /** 获取语音房token */
    @POST("api/xya/livehouse/v1/livehouse/certificate")
    suspend fun getVoiceRoomToken(
        @Body body: Map<String, String>,
    ): ApiResponse<RoomTokenResponse>

    /** 语音房更新接口 */
    @POST("api/xya/livehouse/v1/livehouse/modify")
    suspend fun updateVoiceRoom(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 语音房拉黑观众接口 */
    @POST("api/xya/livehouse/v1/viewer/block")
    suspend fun blockVoiceRoomUser(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 语音房观众列表 */
    @GET("api/xya/livehouse/v1/viewer/list")
    suspend fun getVoiceRoomOnlineList(
        @Query("live_house_id") liveHouseId: String,
        @Query("last_viewer_id") pageId: Int,
    ): ApiResponse<UserListResponse>

    /** 语音房观众详情 */
    @GET("api/xya/livehouse/v1/viewer/detail")
    suspend fun getVoiceRoomMemberDetail(
        @Query("live_house_id") liveHouseId: String,
        @Query("userid") userId: String,
    ): ApiResponse<UserResponse>

    /** 语音房观众拉黑列表 */
    @GET("api/xya/livehouse/v1/block/list")
    suspend fun getVoiceRoomViewerBlackList(
        @Query("live_house_id") liveHouseId: String,
        @Query("last_block_id") lastId: Int,
    ): ApiResponse<UserListResponse>

    /** 主动上麦 */
    @POST("api/xya/livehouse/v1/mic/join")
    suspend fun joinVoiceRoomMic(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 主动下麦 */
    @POST("api/xya/livehouse/v1/mic/leave")
    suspend fun leaveVoiceRoomMic(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 抱下麦 */
    @POST("api/xya/livehouse/v1/mic/kick")
    suspend fun kickVoiceRoomMic(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 管理员同意上麦申请 */
    @POST("api/xya/livehouse/v1/mic/allow_request")
    suspend fun allowVoiceRoomMicRequest(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 申请上麦，管理员同意后，接收上麦 */
    @POST("api/xya/livehouse/v1/mic/take_seat")
    suspend fun takeSeatVoiceRoomMic(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 邀请上麦 */
    @POST("api/xya/livehouse/v1/mic/send_invite")
    suspend fun sendInviteVoiceRoomMic(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 接受邀请上麦 */
    @POST("api/xya/livehouse/v1/mic/join_invited")
    suspend fun joinInvitedVoiceRoomMic(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 麦位申请列表 */
    @GET("api/xya/livehouse/v1/mic/pending_list")
    suspend fun getVoiceRoomMicPendingList(
        @Query("live_house_id") liveHouseId: String,
    ): ApiResponse<JsonObject>

    /** 取消麦位申请 */
    @POST("api/xya/livehouse/v1/mic/cancel_request")
    suspend fun cancelVoiceRoomMicRequest(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 设置/取消管理员 */
    @POST("api/xya/livehouse/v1/admin/set")
    suspend fun setVoiceRoomAdmin(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 设置/取消静音 */
    @POST("api/xya/livehouse/v1/mic/mute")
    suspend fun muteVoiceRoomMic(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 选择用户分享 */
    @POST("api/xya/livehouse/v1/live_house/share")
    suspend fun shareVoiceRoom(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 审核公屏消息 */
    @POST("api/xya/livehouse/v1/message/audit")
    suspend fun auditVoiceRoomMessage(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 房间管理员列表 */
    @GET("api/xya/livehouse/v1/admin/list")
    suspend fun getVoiceRoomAdminList(
        @Query("live_house_id") liveHouseId: String,
        @Query("last_admin_id") lastId: Int,
    ): ApiResponse<UserListResponse>

    /** 清空心动值 */
    @POST("api/xya/livehouse/v1/love/value/reset")
    suspend fun resetLoveValue(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    @GET("api/xya/livehouse/v1/rank")
    suspend fun getRankList(
        @Query("live_house_id") liveHouseId: String,
    ): ApiResponse<RankListContainer>

    @GET("api/xya/livehouse/v1/livehouse/list/recommend")
    suspend fun getRecommendRoomList(): ApiResponse<RecommendLiveRoomResponse>

    /** 获取语音房token */
    @POST("api/xya/c2c/v1/privatespace/token")
    suspend fun getPrivateRoomToken(
        @Body body: Map<String, String>,
    ): ApiResponse<RoomTokenResponse>

    /** 获取语音房信息 */
    @GET("api/xya/c2c/v1/privatespace/info")
    suspend fun getPrivateRoomInfo(
        @Query("space_id") liveHouseId: String,
    ): ApiResponse<PrivateRoomInfoResponse>

    /** 主动上麦 */
    @POST("api/xya/c2c/v1/privatespace/occupy")
    suspend fun joinPrivateRoomMic(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 主动下麦 */
    @POST("api/xya/c2c/v1/privatespace/giveup")
    suspend fun leavePrivateRoomMic(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    //region pk相关
    @POST("api/xya/livehouse/v1/vs/setting")
    suspend fun pkSettings(
        @Body body: Map<String, @JvmSuppressWildcards Any>,
    ): ApiResponse<JsonObject>

    @GET("api/xya/livehouse/v1/vs/support/billbord")
    suspend fun fetchPkContributionBillboard(
        @Query("vs_side") pkSide: Int,
        @Query("live_house_id") roomId: String,
    ): ApiResponse<CheersGroupListResponse>
    //endregion

    //region 语音房任务
    @GET("api/audioroom/v1/audience/tasks/brief/info")
    suspend fun getAudioRoomAudienceInfo(): ApiResponse<JsonObject>
    //endregion
}
