package com.buque.wakoo.network.api.bean

import com.buque.wakoo.bean.user.BasicUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject

@Serializable
data class RoomInfoResponse(
    @SerialName("admins")
    val admins: List<BasicUser> = listOf(),
    @SerialName("audience_cnt")
    val audienceCnt: Int = 0, // 包含麦上和麦下的用户户
    @SerialName("audiences")
    val audiences: List<BasicUser> = listOf(),
    @SerialName("blind_box_filled")
    val blindBoxFilled: Boolean = false, // true
    @SerialName("blind_box_filled_note")
    val blindBoxFilledNote: String = "", // 盲盒存储已满10个请尽快送出
    @SerialName("blind_box_task_duration")
    val blindBoxTaskDuration: Int = 0, // 0
    @SerialName("desc")
    val desc: String? = null, // null
    @SerialName("desc_link")
    val descLink: String = "", // https://api.test.ucoofun.com/h5/xya/connection/v1/social/couples/blindbox/introduce
    @SerialName("happen_timestamp")
    val happenTimestamp: Long, // 1750837057020
    @SerialName("i_am_admin")
    val iAmAdmin: Boolean = false, // false
    @SerialName("i_am_owner")
    val iAmOwner: Boolean = false, // false
    @SerialName("latest_enter_sample_users")
    val latestEnterSampleUsers: List<BasicUser> = listOf(),
    @SerialName("notice")
    val notice: String? = null, // null
    @SerialName("occupy_mic_mode")
    val occupyMicMode: Int = 0, // 1
    @SerialName("mic_apply_cnt")
    val requestMicCount: Int = 0,
    @SerialName("owner")
    val owner: UserResponse,
    @SerialName("paid_items")
    val paidItems: List<PaidItem> = listOf(),
    @SerialName("pk_info")
    val pkInfo: PkInfo? = null, // null
    @SerialName("public_id")
    val publicId: String = "", // 100398
    @SerialName("room_background")
    val roomBackground: String = "", // https://media.ucoofun.com/audioroom%2Fwakoo_audioroom_background.webp
    @SerialName("room_id")
    val roomId: String = "0", // 375
    @SerialName("room_locked")
    val roomLocked: Boolean = false, // false
    @SerialName("room_mode")
    val roomMode: Int = 0, // 4
    @SerialName("seats")
    val seats: List<Seat> = listOf(),
    @SerialName("show_audience_task_popup")
    val showAudienceTaskPopup: Boolean = false,
    @SerialName("audioroom_audience_task_status_sync")
    val audienceData: JsonObject? = null,
    @SerialName("show_blind_box_task")
    val showBlindBoxTask: Boolean = false, // false
    @SerialName("show_cross_pk")
    val showCrossPk: Boolean = false, // false
    @SerialName("sud_game_info")
    val sudGameInfo: JsonObject? = null,
    @SerialName("tags")
    val tags: List<VoiceTag>? = null,
    @SerialName("text_only")
    val textOnly: Boolean = false, // false
    @SerialName("title")
    val title: String = "", // 1111
    @SerialName("wedding_info")
    val weddingInfo: WeddingInfo = WeddingInfo(),
)

@Serializable
data class PaidItem(
    @SerialName("id")
    val id: Int = 0, // 1
    @SerialName("name")
    val name: String = "", // 付费上麦
    @SerialName("price")
    val price: Int = 0, // 20
)

@Serializable
data class Seat(
    @SerialName("has_user")
    val hasUser: Boolean = false, // false
    @SerialName("heart_value") val heartValue: Int? = null,
    val user: UserResponse? = null,
)

// @Serializable
// data class SudGameInfo(
//    @SerialName("game_list")
//    val gameList: List<Any?> = listOf(),
//    @SerialName("ongoing_mg")
//    val ongoingMg: Any? = Any(), // null
// )

@Serializable
class WeddingInfo

@Serializable
data class RoomTokenResponse(
    @SerialName("room_id")
    val roomId: String = "0", // 375
    @SerialName("rtc_channel_name")
    val rtcChannelName: String = "", // audioroom_375_rtc_trtc
    @SerialName("rtc_channel_type")
    val rtcChannelType: Int = 0, // 3
    @SerialName("rtc_config")
    val rtcConfig: RtcConfig = RtcConfig(),
    @SerialName("rtc_token")
    val rtcToken: String = "",
    @SerialName("userid")
    val userid: String = "0", // 4466
    @SerialName("chatspace_id")
    val chatId: String = "", // 4466
) {
    val imId = chatId.ifEmpty { roomId }
}

@Serializable
data class RtcConfig(
    @SerialName("audio_3a")
    val audio3a: Audio3a = Audio3a(),
    @SerialName("quality")
    val quality: Int = 0, // 3
)

@Serializable
data class Audio3a(
    @SerialName("aec_enabled")
    val aecEnabled: Boolean = false, // true
    @SerialName("agc_enabled")
    val agcEnabled: Boolean = false, // false
    @SerialName("ans_enabled")
    val ansEnabled: Boolean = false, // true
)

@Serializable
data class VoiceRoomCreateReq(
    val title: String,
    val desc: String,
    val tag_ids: String,
)

@Serializable
data class VoiceRoomCreateResponse(
    @SerialName("room_id")
    val roomId: String,
    @SerialName("public_id")
    val publicId: String,
)

@Serializable
data class VoiceRoomTagListResponse(
    @SerialName("tags")
    val tags: List<VoiceTag>,
)

@Serializable
data class PrivateRoomInfoResponse(
    @SerialName("chatroom_id")
    val chatroomId: String = "", // privateroom16872
    @SerialName("connection")
    val connection: Connection = Connection(),
    @SerialName("fan_in_room")
    val fanInRoom: Boolean = false, // false
    @SerialName("fan_using_mic")
    val fanUsingMic: Boolean = false, // false
    @SerialName("female_user")
    val femaleUser: UserResponse = UserResponse(),
    @SerialName("happen_timestamp")
    val happenTimestamp: Long = 0, // 1753089388419
    @SerialName("hide_charge_info")
    val hideChargeInfo: Boolean = false, // false
    @SerialName("interact_status")
    val interactStatus: InteractStatus = InteractStatus(),
    @SerialName("is_newbie_try_service")
    val isNewbieTryService: Boolean = false, // false
    @SerialName("is_partner")
    val isPartner: Boolean = false, // false
    @SerialName("male_user")
    val maleUser: UserResponse = UserResponse(),
    @SerialName("mode")
    val mode: Int = 0, // 1
    @SerialName("invite_code")
    val inviteCode: String = "",
    @SerialName("newbie_lock_seconds")
    val newbieLockSeconds: Int = 0, // 0
    @SerialName("newbie_userid")
    val newbieUserid: Int = 0, // 1409
    @SerialName("positions")
    val positions: List<Seat> = listOf(),
    @SerialName("room_background")
    val roomBackground: String = "",
    @SerialName("room_id")
    val roomId: Int = 0, // 16872
    @SerialName("service_userid")
    val serviceUserid: String = "0", // 4592
    @SerialName("star_hqu_type")
    val starHquType: Int = 0, // 1
    @SerialName("star_in_room")
    val starInRoom: Boolean = false, // false
    @SerialName("star_using_mic")
    val starUsingMic: Boolean = false, // false
    @SerialName("from_scene_type")
    val fromSceneType: Int = -1, // 1、 部落 2、语音房 3、私密小屋 4、私聊 5、动态、6、个人主页
    @SerialName("from_scene_id")
    val fromSceneId: Int = -1, // 1、部落ID 2、语音房ID 3、私密小屋ID、4、私聊对方ID 5、忽略此参数 6、主页主人ID
)

@Serializable
data class Connection(
    @SerialName("can_be_partner")
    val canBePartner: Boolean = false, // true
    @SerialName("is_partner")
    val isPartner: Boolean = false, // false
    @SerialName("is_peer")
    val isPeer: Boolean = false, // true
    @SerialName("need_peer")
    val needPeer: Boolean = false, // false
)

@Serializable
data class InteractStatus(
    @SerialName("expire_timestamp")
    val expireTimestamp: Int = 0, // 0
    @SerialName("fan_hint")
    val fanHint: String = "",
    @SerialName("is_cp")
    val isCp: Boolean = false, // false
    @SerialName("is_friend")
    val isFriend: Boolean = false, // true
    @SerialName("is_newbie_try_time")
    val isNewbieTryTime: Boolean = false, // false
    @SerialName("pay_method")
    val payMethod: Int = 0, // 1
    @SerialName("remain_seconds")
    val remainSeconds: Int = 0, // 900
    @SerialName("room_id")
    val roomId: String = "", // 16872
    @SerialName("room_mode")
    val roomMode: Int = 0, // 1
    @SerialName("show_together_mic_timer")
    val showTogetherMicTimer: Boolean = false, // false
    @SerialName("star_hint")
    val starHint: String = "",
    @SerialName("status")
    val status: Int = 0, // 5
    @SerialName("title")
    val title: String = "", // 心动连线
    @SerialName("use_coin_duration")
    val useCoinDuration: Int = 0, // 0
)

//region 房间内pk相关

@Serializable
data class PkInfo(
    @SerialName("blue_side_cheers")
    val blueSideCheers: List<BasicUser> = emptyList(), // 蓝色方助威前三
    @SerialName("blue_side_cheers_cnt")
    val blueSideCheersCnt: Int = 0, // 蓝色方助威人数
    @SerialName("blue_side_value")
    val blueSideValue: Int = 0, //  蓝方心动值
    @SerialName("end_time")
    val endTime: Int = 0, // # 预计结束时间
    @SerialName("pk_status")
    val pkStatus: Int = 1, // # 1未开始 2进行中 3已结束
    @SerialName("red_side_cheers")
    val redSideCheers: List<BasicUser> = emptyList(), // 红色方助威前三
    @SerialName("red_side_cheers_cnt")
    val redSideCheersCnt: Int = 0, // 红色方助威人数
    @SerialName("red_side_value")
    val redSideValue: Int = 0, // 红色方心动值
    @SerialName("remain_duration")
    val remainDuration: Int = 0, // # 剩余时长（秒）
    @SerialName("title")
    val title: String = "", // 惩罚
    @SerialName("duration")
    val pkDuration: Int = 300, // 设置时间
    @SerialName("winner_info")
    val winnerInfo: WinnerInfo? = null, // pk结果
    @SerialName("winner_side")
    val winnerSide: Int? = -1, // 胜利方 1蓝方 2红方 -1平局
) {
    companion object {
        const val STATE_IDE = 1
        const val STATE_RUNNING = 2
        const val STATE_FINISHED = 3
    }
}

@Serializable
data class WinnerInfo(
    @SerialName("cheer_user_info")
    val cheerUserInfo: PkUser? = null, // 助威mvp,
    @SerialName("heart_user_info")
    val heartUserInfo: PkUser? = null, // 选手mvp
)

@Serializable
data class PkUser(
    @SerialName("user_info")
    val user: BasicUser, // 助威mvp,
    @SerialName("value")
    val value: Int = 0, // 选手mvp
)

@Serializable
data class PKEvent(
    @SerialName("action")
    val action: String, //  add_time加时消息 end结束消息
    @SerialName("digest")
    val digest: String, // 房管已设置本轮PK加时5分钟
    @SerialName("winner_info")
    val winnerInfo: WinnerInfo? = null, // pk结果
    @SerialName("winner_side")
    val winnerSide: Int? = -1, // # 胜利方 1蓝方 2红方 -1平局
)

@Serializable
data class CheersGroupListResponse(
//    "support_user_infos": [],
// "desc": "1钻石礼物增加1助威值"
    @SerialName("support_user_infos")
    val suuportUserInfo: List<UserResponse>,
    @SerialName("desc")
    val desc: String,
)

//endregion
