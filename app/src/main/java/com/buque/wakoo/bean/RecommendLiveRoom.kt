package com.buque.wakoo.bean

import androidx.annotation.Keep
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.network.api.bean.VoiceTag
import com.buque.wakoo.ui.screens.liveroom.LiveMicMode
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import com.buque.wakoo.ui.screens.liveroom.toRoomUser
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonNames

@Keep
@Serializable
data class RecommendLiveRoomResponse @OptIn(ExperimentalSerializationApi::class) constructor(
    @SerialName("rooms")
    @JsonNames("rooms","livehouses")
    val data: List<RecommendLiveRoom>,
)

@Keep
@Serializable
data class RecommendLiveRoom(
    @SerialName("has_ongoing_coin_pk_game")
    val hasOngoingCoinPkGame: Boolean = false,
    @SerialName("has_red_packet")
    val hasRedPacket: Boolean = false,
    @SerialName("id")
    val id: Int = 0,
    @SerialName("in_room_user_cnt")
    val inRoomUserCnt: Int = 0,
    @SerialName("owner")
    val owner: BasicUser,
    @SerialName("recommend_user_list")
    val recommendUserList: List<RecommendUser> = listOf(),
    @SerialName("room_mode")
    val roomMode: Int = 0,
    @SerialName("title")
    val title: String = "",
    @SerialName("card_background")
    val cardBackground: String? = null,
) {
    @Keep
    @Serializable
    data class RecommendUser(
        @SerialName("age")
        val age: Int = 0,
        @SerialName("attractive_tags")
        val attractiveTags: List<VoiceTag> = listOf(),
        @SerialName("avatar_url")
        val avatarUrl: String = "",
        @SerialName("gender")
        val gender: Int = 0,
        @SerialName("in_room")
        val inRoom: Boolean = false,
        @SerialName("nickname")
        val nickname: String = "",
        @SerialName("userid")
        val userid: Int = 0,
        @SerialName("using_mic")
        val usingMic: Boolean = false,
    )

    fun toBasicRoomInfo(): BasicRoomInfo =
        BasicRoomInfo(
            id = id.toString(),
            publicId = "",
            title = title,
            owner = owner.toRoomUser(),
            roomMode = LiveRoomMode.valueOf(roomMode),
            micMode = LiveMicMode.UnKnown(-1),
            desc = null,
            notice = null,
            background = null,
            tagIds = null,
        )
}
