package com.buque.wakoo.bean

import androidx.compose.runtime.IntState
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.network.api.bean.PkInfo
import com.buque.wakoo.network.api.bean.RtcConfig
import com.buque.wakoo.ui.screens.liveroom.LiveMicMode
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import com.buque.wakoo.ui.screens.liveroom.RoomUser
import com.buque.wakoo.ui.screens.liveroom.toRoomUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject

@Serializable
data class MyLiveRoomInfo(
    @SerialName("id") val roomId: String = "0",
    @SerialName("public_id") val publicId: String = "",
    @SerialName("title") val roomName: String = "",
    @SerialName("avatar_url") val avatarUrl: String = "",
) {
    fun toBasicRoomInfo(): BasicRoomInfo =
        BasicRoomInfo(
            id = roomId,
            publicId = publicId,
            title = roomName,
            owner = null,
            roomMode = LiveRoomMode.UnKnown(-1),
            micMode = LiveMicMode.UnKnown(-1),
            desc = null,
            notice = null,
            background = null,
            tagIds = null,
        )
}

@Serializable
data class LiveRoomEditInfo(
    val title: String,
    val desc: String,
    val tagIds: List<Int>,
)

/**
 * rtc相关的配置
 */
data class RoomRtcToken(
    val channelName: String = "", // audioroom_375_rtc_trtc
    val channelType: Int = 0, // 3
    val config: RtcConfig = RtcConfig(),
    val token: String = "",
)

data class RoomOnlineInfo(
    val totalCount: IntState,
    val previewList: List<User>,
)

@Serializable
data class BasicRoomInfo(
    val id: String,
    val publicId: String,
    val title: String,
    val owner: RoomUser?, // 私密小屋房主为null
    val roomMode: LiveRoomMode,
    val micMode: LiveMicMode,
    val desc: String?, // 介绍
    val notice: String?, // 公告
    val background: String?,
    val tagIds: List<Int>?,
    val locked: Boolean = false,
    val autoUpMic: Boolean = false,
) {
    companion object {
        val preview: BasicRoomInfo
            get() =
                BasicRoomInfo(
                    id = "375",
                    publicId = "10086",
                    title = "你要约会吗?",
                    owner = BasicUser.sampleBoy.toRoomUser(),
                    roomMode = LiveRoomMode.Normal,
                    micMode = LiveMicMode.UnKnown(-1),
                    desc = "大家来约会吧",
                    notice = "想要上麦请扣1",
                    tagIds = null,
                    background = null,
                )
    }

    val imId: String = "${if (roomMode is LiveRoomMode.Private) "privateroom" else ""}$id"

    val ownerId: String?
        get() = owner?.id

    val micSeatCount
        get() = roomMode.micSeatCount

    val isPrivateRoom
        get() = roomMode.isPrivateRoom

    val isUnKnown
        get() = roomMode.isUnKnown
}

data class LiveRoomExtraInfo(
    val reqUpMicCount: Int = 0,
    val reqUpMicIng: Boolean = false,
    val privateRoomInfo: PrivateRoomBasicInfo? = null,
    val pkRoomInfo: PkInfo? = null,
    val showAudienceTaskPopup: Boolean = false,
    val audioRoomAudienceBean: JsonObject? = null,
)

data class PrivateRoomBasicInfo(
    val mode: Int, // 0: 兼容模式 1: CP模式 2: 语音通话模式(目前应该没有兼容模式, 只有1和2，1其实就是免费模式，2是收费模式)
    val selfUser: RoomUser,
    val targetUser: RoomUser,
    val selfIsStar: Boolean, // 自己是否是主播
    val targetIsStar: Boolean, // 对方是否是主播
    val fromSceneType: Int, // 1、 部落 2、语音房 3、私密小屋 4、私聊 5、动态、6、个人主页
    val fromSceneId: Int, // 1、部落ID 2、语音房ID 3、私密小屋ID、4、私聊对方ID 5、忽略此参数 6、主页主人ID
    val inviteCode: String,
    val newbieLockSeconds: Int,
    val targetInRoom: Boolean,
    val isNewbieTryService: Boolean,
    val interact: PrivateRoomInteractStatus,
)

data class PrivateRoomInteractStatus(
    val isCp: Boolean, // 是否成为了cp,
    val isFriend: Boolean, // 是否成为了好友
    val starHint: String,
    val fanHint: String,
    val status: Int = 0, // 1=开始计时，5=停止计时, -1=时长不足
    val endTimestamp: Int, // 当status==1时有效
    val remindSeconds: Int, // 当status==5时有效
    val isNewbieTryTime: Boolean,
)

@Serializable
data class VoiceCallNotice(
    @SerialName("invite_code")
    val callId: String,
    @SerialName("room_id")
    val roomId: Int,
    @SerialName("invite_method")
    val inviteMethod: Int = 1,
    @SerialName("hint")
    val hint: String = "",
    @SerialName("payer_id")
    val actionUserId: String = "",
    @SerialName("is_newbie_guide")
    val isNewbieGuide: Boolean = false,
    @SerialName("reason_code")
    val reasonCode: Int = 0,
    @SerialName("reason_text")
    val reason: String = "",
)
