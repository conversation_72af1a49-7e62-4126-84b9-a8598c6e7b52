package com.buque.wakoo.bean

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class SilverMallResult(
    @SerialName("silver_balance")
    var silverBalance: Long = 0,
    @SerialName("g_infos")
    val giftInfos: List<ExGift> = emptyList(),
    @SerialName("d_infos")
    val propsList: List<ExProps> = emptyList(),
)

@Serializable
sealed interface ExchangeItem {
    val icon: String
    val id: Int
    val name: String
    val days: Int
    val price: Int

    fun buildRequestMap(): MutableMap<String, String>
}

@Serializable
data class ExGift(
    @SerialName("effect_file")
    val effectFile: String,
    @SerialName("icon")
    override val icon: String,
    @SerialName("id")
    override val id: Int,
    @SerialName("name")
    override val name: String,
    @SerialName("price")
    override val price: Int,
) : ExchangeItem {
    @Transient
    override val days: Int = 0

    override fun buildRequestMap(): MutableMap<String, String> =
        mutableMapOf(
            "exchange_type" to "1",
            "exchange_id" to id.toString(),
        )
}

@Serializable
data class ExProps(
    @SerialName("days")
    override val days: Int,
    @SerialName("icon")
    override val icon: String,
    @SerialName("id")
    override val id: Int,
    @SerialName("name")
    override val name: String,
    @SerialName("price")
    override val price: Int,
    @SerialName("t")
    val propsType: Int,
) : ExchangeItem {
    override fun buildRequestMap(): MutableMap<String, String> =
        mutableMapOf(
            "exchange_type" to "2",
            "exchange_id" to id.toString(),
            "prop_type" to propsType.toString(),
        )
}
