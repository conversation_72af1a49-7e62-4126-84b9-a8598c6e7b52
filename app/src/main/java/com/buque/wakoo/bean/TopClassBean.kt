package com.buque.wakoo.bean

import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject

@Keep
@Serializable
data class TopClassBean(
    @SerialName("top_class_info")
    val firstClassInfo: TopClassInfo = TopClassInfo(),
    @SerialName("show_top_class_info")
    val showFirstClassInfo: Boolean = false,
) {
    @Keep
    @Serializable
    data class TopClassInfo(
        @SerialName("activate_rule_describe")
        val activateRuleDescribe: String = "",
        @SerialName("avatar_url")
        val avatarUrl: String = "",
        @SerialName("can_be_activated")
        val canBeActivated: Boolean = false,
        @SerialName("can_be_renewed")
        val canBeRenewed: Boolean = false,
        @SerialName("describe_image")
        val describeImage: String = "",
        @SerialName("describe_text")
        val describeText: String = "",
        @SerialName("duration")
        val duration: Int = 0,
        @SerialName("end_time")
        val endTime: Long = 0,
        @SerialName("honour")
        val honour: Int = 0,
        @SerialName("is_first_class_user")
        val isFirstClassUser: Boolean = false,
        @SerialName("join_days")
        val joinDays: Int = 0,
        @SerialName("level")
        val level: Int = 0,
        @SerialName("nickname")
        val nickname: String = "",
        @SerialName("no")
        val no: String = "",
        @SerialName("paid_coins")
        val paidCoins: Int = 0,
        @SerialName("privilege")
        val privilege: ClassPrivilege = ClassPrivilege(),
        @SerialName("privilege_items")
        val privilegeItems: List<JsonObject> = listOf(),
        @SerialName("total_coin")
        val totalCoin: Int = 0,
    )
}

@Keep
@Serializable
data class TargetUserClassBean(
    @SerialName("privilege")
    val privilege: ClassPrivilege = ClassPrivilege(),
    @SerialName("is_first_class_user")
    val is_first_class_user: Boolean = false,
    @SerialName("honour")
    val honour: Int = 0,
    @SerialName("no")
    val no: String = "",
)

@Keep
@Serializable
data class ClassPrivilege(
    @SerialName("message_not_disturb")
    val messageNotDisturb: Boolean = false,
    @SerialName("online_notification")
    val onlineNotification: Boolean = false,
    @SerialName("room_invisibility")
    val roomInvisibility: Boolean = false,
)
