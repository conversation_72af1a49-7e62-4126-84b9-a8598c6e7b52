package com.buque.wakoo.bean.message

import com.buque.wakoo.bean.RichItem
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GroupNotice(
    val notice: List<RichItem> = emptyList(),
    @SerialName("action_link")
    val actionLink: String = "",
    @SerialName("action_text")
    val actionText: String = "",
    @SerialName("action_type")
    val actionType: Int = 0,
    @SerialName("digest")
    val digest: String = "",
)
