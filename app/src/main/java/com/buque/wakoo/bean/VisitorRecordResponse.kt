package com.buque.wakoo.bean

import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class VisitorRecordResponse(
    @SerialName("total")
    val count: Int,
    @SerialName("list")
    val list: List<UserResponse>,
    @SerialName("previous_visit")
    val previousVisits: List<UserResponse>,
    @SerialName("is_member")
    val isVip: Boolean,
)
