package com.qyqy.cupid.data


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep

@Keep
@Serializable
data class AudioRoomAudiencePopup(
    @SerialName("bonus")
    val bonus: List<Bonu> = listOf(),
    @SerialName("buttons")
    val buttons: List<Button> = listOf(),
    @SerialName("desc")
    val desc: String = "",
    @SerialName("title")
    val title: String = ""
) {
    @Keep
    @Serializable
    data class Bonu(
        @SerialName("icon")
        val icon: String = "",
        @SerialName("text")
        val text: String = ""
    )

    @Keep
    @Serializable
    data class Button(
        @SerialName("action_link")
        val actionLink: String = "",
        @SerialName("label")
        val label: String = "",
        @SerialName("t")
        val t: Int = 0
    )
}