package com.buque.wakoo.bean

import androidx.annotation.Keep
import com.buque.wakoo.bean.user.BasicUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class MsgGrantRechargeBean(
    @SerialName("user")
    val user: BasicUser,
    @SerialName("target_user")
    val targetUser: BasicUser,
    @SerialName("hint")
    val hint: String = "",
    // 是否充值过新人礼包
    @SerialName("newbie_resource_claimed")
    val hasRechargeNewbie: Boolean = false,
)
