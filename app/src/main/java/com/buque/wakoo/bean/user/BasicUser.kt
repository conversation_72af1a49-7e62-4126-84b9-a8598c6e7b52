package com.buque.wakoo.bean.user

import androidx.compose.runtime.Composable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

interface User {
    val id: String
    val publishId: String
    val name: String
    val age: Int
    val gender: Int
    val avatar: String
    val birthday: String
    val isVip: Boolean // vip
    val height: Int // 身高
    val intro: String // 个人简介
    val type: Int // 0 非主播、1 普通主播、2 台湾素人主播

    val isSelf
        @Composable get() = id == LocalSelfUserProvider.current.id

    val sIsSelf
        get() = id == SelfUser?.id

    val isBoy
        get() = gender == 1

    val isGirl
        get() = gender == 2

    val genderIsSet
        get() = isBoy || isGirl

    val ageIsSet
        get() = age > 0

    val displayGender: String
        @Composable get() =
            if (genderIsSet) {
                if (isBoy) {
                    "男".localized
                } else {
                    "女".localized
                }
            } else {
                "未选择".localized
            }

    val formatHeight: String
        get() = if (height > 0) "${height}cm" else ""

    val isHQU: Boolean
        get() = type > 0

    /**
     * 一般这种数据只有id可用
     */
    val isEmpty: Boolean
        get() = name.isEmpty() && avatar.isEmpty()

    fun toBasic(): BasicUser
}

@Entity(tableName = "users")
@Serializable
data class BasicUser(
    @PrimaryKey @ColumnInfo(name = "userid") @SerialName("userid") override val id: String,
    @ColumnInfo(name = "public_id") @SerialName("public_id") override val publishId: String = "0",
    @ColumnInfo(name = "nickname") @SerialName("nickname") override val name: String = "",
    @ColumnInfo(name = "avatar_url") @SerialName("avatar_url") override val avatar: String = "",
    @ColumnInfo(name = "gender") @SerialName("gender") override val gender: Int = 0,
    @ColumnInfo(name = "age") @SerialName("age") override val age: Int = -1,
    @ColumnInfo(name = "birthday") @SerialName("birthday") override val birthday: String = "",
    @ColumnInfo(name = "is_member") @SerialName("is_member") override val isVip: Boolean = false,
    @ColumnInfo(name = "height") @SerialName("height") override val height: Int = -1,
    @ColumnInfo(name = "short_intro") @SerialName("short_intro") override val intro: String = "",
    @ColumnInfo(name = "type") @SerialName("type") override val type: Int = 0, // 0 非主播、1 普通主播、2 台湾素人主播
) : User {
    companion object {
        val sampleBoy
            get() =
                BasicUser(
                    id = "1",
                    publishId = "10001",
                    name = "小帅",
                    avatar = "https://s.test.wakooclub.com/aaceIm",
                    gender = 1,
                    age = 22,
                )
        val sampleGirl
            get() =
                BasicUser(
                    "2",
                    "10002",
                    "幼儿园班花",
                    "https://s.test.wakooclub.com/aaceHw?x-oss-process=image/format,webp",
                    2,
                    20,
                )

        fun fromUid(uid: String) = BasicUser(uid)

        fun fromResponse(response: UserResponse): BasicUser =
            BasicUser(
                id = response.id,
                publishId = response.publicId,
                name = response.nickname,
                gender = response.gender,
                avatar = response.avatarUrl,
                age = response.age,
                birthday = response.birthday,
                isVip = response.isMember || response.member.isValid,
                height = response.height,
                intro = response.shortIntro,
                type = if (response.isHighQuality) 1 else 0,
            )
    }

    override fun toBasic() = this
}
