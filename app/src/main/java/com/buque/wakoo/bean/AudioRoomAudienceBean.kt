package com.buque.wakoo.bean

import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class AudioRoomAudienceBean(
    @SerialName("gift_task")
    val giftTask: GiftTask = GiftTask(),
    @SerialName("interact_task")
    val interactTask: InteractTask = InteractTask(),
    @SerialName("is_finished")
    val isFinished: Boolean = false,
) {
    @Keep
    @Serializable
    data class GiftTask(
        @SerialName("gold_coin_cnt")
        val goldCoinCnt: Int = 0,
    )

    @Keep
    @Serializable
    data class InteractTask(
        @SerialName("interact_seconds")
        val interactSeconds: Long = 0L,
        @SerialName("interact_status")
        val interactStatus: Int = 0,
    )
}
