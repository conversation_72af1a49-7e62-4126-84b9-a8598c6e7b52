package com.buque.wakoo.bean

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

/**
 * 详细含义查看[https://d03r3qddlre.feishu.cn/docx/PqpZd1z3AoAmtJxEvaqcMcHcnOh]
 */
@Serializable
data class UIConfig(
    @SerialName("deco_store_has_sweat")
    val decoStoreHasSweat: Boolean = false, // false
    @SerialName("discovery_tabs")
    val disCoveryTabs: List<DiscoveryTab> = listOf(),
    // 是否展示评分弹窗
    @SerialName("enable_grade_popup")
    val enableGradePopup: Boolean = false, // false
    // 是否展示礼物墙
    @SerialName("g_showcase_enabled")
    val showGiftWall: Boolean = false, // false
    // 可以修改性别的次数
    @SerialName("gender_switch_card_cnt")
    val genderSwitchCardCnt: Int = 0, // 0
    @SerialName("good_partner_shift_banner_enabled")
    val goodPartnerShiftBannerEnabled: Boolean = false, // 是否展示官宣飘屏
    @SerialName("group_partner_page_link")
    val groupPartnerPageLink: String = "", // 组CP链接
    @SerialName("hot_taste_enabled")
    val hotTasteEnabled: Boolean = false, // // 通用飘屏屏蔽
    @SerialName("ian_enabled")
    val ianEnabled: Boolean = false, // 是否启用应用内通知
    @SerialName("lucky_shift_banner_enabled")
    val luckyShiftBannerEnabled: Boolean = false, // 是否展示抽奖飘屏
    @SerialName("packet_enabled")
    val packetEnabled: Boolean = false, // 是否展示红包相关
    @SerialName("partner_has_escaped")
    val partnerHasEscaped: Boolean = false, // 是否显示cp相关信息
    // 是否展示发布声音的按钮
    @SerialName("publish_sound_btn_exists")
    val showPublishVoice: Boolean = false, // false
    // 是否有搜索按钮
    @SerialName("search_btn_exists")
    val searchBtnExists: Boolean = false, // true
    // 是否展示飘屏
    @SerialName("shift_banner_enabled")
    val shiftBannerEnabled: Boolean = false, // false
    // 是否显示大礼物飘屏
    @SerialName("super_bonus_shift_banner_enabled")
    val superBonusShiftBannerEnabled: Boolean = false, // true
    @SerialName("user_level_entry")
    val userLevelEntry: Boolean = false, // false
    @SerialName("landing_page")
    val landingPage: PageInfo = PageInfo(21, 1),
    @Transient
    val isRemoteData: Boolean = true,
) {
    val showGenderSetting
        get() = genderSwitchCardCnt > 0

    companion object {
        val EMPTY = UIConfig()
    }
}

@Serializable
data class DiscoveryTab(
    @SerialName("name")
    val name: String = "", // 语音派对
    @SerialName("t")
    val t: Int = 0, // 1
)


@Serializable
data class PreloadConfigs(
//    @SerialName("gift_effect_files")
//    val giftEffectList: List<String>,
    @SerialName("otp_product_ids")
    val inAppProductList: List<String>,
    @SerialName("subscription_product_ids")
    val subProductList: List<String>,
//    @SerialName("cp_room_house_effect_files")
//    val cpRoomHouseEffectFiles: List<String>,
//    @SerialName("cp_room_car_effect_files")
//    val cpRoomCarEffectFiles: List<String>
)