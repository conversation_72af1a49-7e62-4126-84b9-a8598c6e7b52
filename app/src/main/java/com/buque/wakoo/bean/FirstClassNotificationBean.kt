package com.buque.wakoo.bean

import androidx.annotation.Keep
import com.buque.wakoo.bean.user.BasicUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class FirstClassNotificationBean(
    @SerialName("action")
    val action: String = "",
    @SerialName("digest")
    val digest: String = "",
    @SerialName("user")
    val user: BasicUser,
    @SerialName("head_message")
    val headMessage: String = "",
    @SerialName("middle_message")
    val middleMessage: String = "",
    @SerialName("message")
    val message: String = "",
    @SerialName("days")
    val days: String = "",
    @SerialName("no")
    val no: String = "",
)
