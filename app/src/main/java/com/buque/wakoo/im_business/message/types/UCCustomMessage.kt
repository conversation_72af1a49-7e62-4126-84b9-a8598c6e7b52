package com.buque.wakoo.im_business.message.types

import com.buque.wakoo.app.AppJson
import com.buque.wakoo.bean.UserRichList
import com.buque.wakoo.ext.getBoolOrNull
import com.buque.wakoo.ext.getFloatOrNull
import com.buque.wakoo.ext.getIntOrNull
import com.buque.wakoo.ext.getLongOrNull
import com.buque.wakoo.ext.getOrNull
import com.buque.wakoo.ext.getStringOrNull
import com.buque.wakoo.ext.parseValue
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.bean.IMUser
import com.buque.wakoo.im.inter.IUCCustomMessage
import com.buque.wakoo.im.inter.IUCMessage
import com.buque.wakoo.im.utils.takeIsNotEmpty
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.utils.LogUtils
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonObject
import java.util.concurrent.ConcurrentHashMap

data class UCCustomMessage(
    override val base: UCMessage,
    override val rawContent: String,
) : UCInstanceMessage,
    IUCMessage by base,
    IUCCustomMessage {
    companion object {
        private const val KEY_CMD = "cmd"
        private const val KEY_DATA = "data"
        private const val KEY_DIGEST = "digest"
    }

    override val cmd: String

    @Deprecated("尽量不要直接使用 customJson")
    override val customJson: JsonObject

    override val summary: String?

    override val user: IMUser?
        get() = getFallbackUser() ?: super.user

    @Volatile
    private var fallbackUser: IMUser? = null

    @Volatile
    var rawTypeData: Any? = null

    val contentCache by lazy {
        ConcurrentHashMap<String, Any?>()
    }

    init {
        val json = AppJson.decodeFromString<JsonObject>(rawContent)
        cmd = json.getStringOrNull(KEY_CMD).orEmpty()
        customJson = json.getOrNull(KEY_DATA)?.jsonObject ?: JsonObject(emptyMap())
        summary = customJson.getStringOrNull(KEY_DIGEST).takeIsNotEmpty()
        getFallbackUser()
    }

    override fun toString(): String = "UCCustomMessage(base=$base, cmd='$cmd', summary='$summary', json='$customJson')"

    override fun getSummaryString(): String = "自定义消息, cmd: $cmd, summary: $summary"

    private fun getFallbackUser(): IMUser? =
        fallbackUser ?: (
            when (cmd) {
                IMEvent.USER_ENTRANCE, IMEvent.USER_EXIT, IMEvent.APPLY_MIC,
                IMEvent.INVITE_PRIVATE_ROOM2, IMEvent.BEST_MATCH_DIALOG,
                IMEvent.MEMBER_JOIN, IMEvent.CHATGROUP_MEMBER_JOIN,
                IMEvent.MEMBER_QUIT, IMEvent.CHATGROUP_MEMBER_QUIT,
                IMEvent.BULLETIN_UPDATE, IMEvent.NAME_UPDATE, IMEvent.USER_LEVEL_CHANGE,
                IMEvent.WEDDING_COMMON_EVENT,
                -> "user"

                IMEvent.INVITE_MIC, IMEvent.AGREE_MIC, IMEvent.REFUSE_MIC, IMEvent.TAKEAWAY_MIC,
                IMEvent.MEMBER_KICKED_OUT, IMEvent.CHATGROUP_MEMBER_KICKED_OUT,
                -> "admin_user"

                else
                -> "sender"
            }.let {
                if (containsKey(it)) {
                    getJsonValue<UserResponse>(it)
                } else {
                    null
                }
            } ?: run {
                if (cmd == IMEvent.COMMON_CHATROOM_PUBLIC_MESSAGES || cmd == IMEvent.FOLLOW_CHATROOM_PUBLIC_MESSAGES) {
                    getJsonValue<List<UserRichList>>("messages")?.firstOrNull()?.sender
                } else {
                    null
                }
            }
        )?.let { IMUser.fromResponse(it) }?.also {
            fallbackUser = it
        }

    fun containsKey(key: String) = customJson.containsKey(key)

    inline fun <reified T> parseDataJson(): T? =
        rawTypeData as? T ?: try {
            AppJson.decodeFromJsonElement<T>(customJson).also {
                rawTypeData = it
            }
        } catch (e: Exception) {
            LogUtils.eTag("parseDataJson", throwable = e)
            null
        }

    inline fun <reified T> getJsonValue(key: String): T? =
        if (customJson.containsKey(key)) {
            try {
                contentCache[key] as? T ?: customJson.parseValue<T?>(key).also {
                    contentCache[key] = it
                }
            } catch (_: Exception) {
                null
            }
        } else {
            null
        }

    inline fun <reified T> getJsonValue(
        key: String,
        default: T,
    ): T = getJsonValue(key) ?: default

    fun getJsonInt(key: String): Int? =
        if (customJson.containsKey(key)) {
            contentCache[key] as? Int ?: customJson.getIntOrNull(key).also {
                contentCache[key] = it
            }
        } else {
            null
        }

    fun getJsonInt(
        key: String,
        default: Int,
    ): Int = getJsonInt(key) ?: default

    fun getJsonLong(key: String): Long? =
        if (customJson.containsKey(key)) {
            contentCache[key] as? Long ?: customJson.getLongOrNull(key).also {
                contentCache[key] = it
            }
        } else {
            null
        }

    fun getJsonLong(
        key: String,
        default: Long,
    ): Long = getJsonLong(key) ?: default

    fun getJsonFloat(key: String): Float? =
        if (customJson.containsKey(key)) {
            contentCache[key] as? Float ?: customJson.getFloatOrNull(key).also {
                contentCache[key] = it
            }
        } else {
            null
        }

    fun getJsonFloat(
        key: String,
        default: Float,
    ): Float = getJsonFloat(key) ?: default

    fun getJsonBoolean(key: String): Boolean? =
        if (customJson.containsKey(key)) {
            contentCache[key] as? Boolean ?: customJson.getBoolOrNull(key).also {
                contentCache[key] = it
            }
        } else {
            null
        }

    fun getJsonBoolean(
        key: String,
        default: Boolean,
    ): Boolean = getJsonBoolean(key) ?: default

    fun getJsonString(key: String): String? =
        if (customJson.containsKey(key)) {
            contentCache[key] as? String ?: customJson.getStringOrNull(key).also {
                contentCache[key] = it
            }
        } else {
            null
        }

    fun getJsonString(
        key: String,
        default: String,
    ): String = getJsonString(key) ?: default

    fun getContentText(): String? =
        getJsonValue<String>("contentText")?.takeIsNotEmpty()
            ?: getJsonValue<String>("content")?.takeIsNotEmpty()
            ?: summary
}
