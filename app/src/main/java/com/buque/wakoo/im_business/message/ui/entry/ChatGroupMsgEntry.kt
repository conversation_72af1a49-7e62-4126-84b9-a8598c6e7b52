package com.buque.wakoo.im_business.message.ui.entry

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.RichItem
import com.buque.wakoo.bean.message.GroupActionHint
import com.buque.wakoo.bean.message.GroupNotice
import com.buque.wakoo.bean.message.appendWithStyle
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.bean.IMUser
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.message.types.UCGiftMessage
import com.buque.wakoo.im_business.message.types.UCImageMessage
import com.buque.wakoo.im_business.message.types.UCTextMessage
import com.buque.wakoo.im_business.message.types.UCVoiceMessage
import com.buque.wakoo.im_business.message.ui.VoiceMessageContent
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.widget.media.previewer.MediaViewerItem
import com.buque.wakoo.ui.widget.richtext.RichTextScope
import com.buque.wakoo.utils.convertHtmlToAnnotatedString

sealed class ChatGroupMsgEntry : MsgUIEntry {
    companion object {
        private val blueSpanStyle = SpanStyle(color = Color(0xFF15ABFF), fontSize = 12.sp)

        fun from(
            message: UCInstanceMessage,
            density: Density,
        ): ChatGroupMsgEntry =
            when (message) {
                is UCTextMessage -> GroupText(message)
                is UCImageMessage -> {
                    val imageElem = message.largeElem ?: message.localElem!!
                    GroupImage(
                        mediaItem = message.toMediaViewerImageItem(),
                        width = imageElem.width,
                        height = imageElem.height,
                        user = message.requireUser(),
                    )
                }

                is UCVoiceMessage -> GroupVoice(VoiceMessageContent(message, true))
                is UCCustomMessage -> {
                    when (message.cmd) {

                        IMEvent.GROUP_COMMON_RICH_TEXT,  -> {
                            val list = message.getJsonValue<List<RichItem>>("rich_text").orEmpty()
                            GroupRichText(list)
                        }

                        IMEvent.CHATGROUP_MEMBER_JOIN ->
                            SystemTip(message.user?.avatar) {
                                withBuilder {
                                    append("恭喜新人".localized)
                                    appendWithStyle(" ${message.user?.name} ", blueSpanStyle)
                                    append("加入群组，大家热烈欢迎！".localized)
                                }
                            }

                        IMEvent.GROUP_ACTION_HINT -> {
                            val data = message.parseDataJson<GroupActionHint>()
                            if (data != null) {
                                val strings = data.richText.map { convertHtmlToAnnotatedString(it.text) }
                                SystemTip(data.avatarUrls.firstOrNull()) {
                                    withBuilder {
                                        strings.forEach {
                                            append(it)
                                        }
                                    }
                                }
                            } else {
                                null
                            }
                        }

                        IMEvent.GROUP_NOTICE_ACTION_CARD -> {
                            val data = message.parseDataJson<GroupNotice>()
                            if (data != null) {
                                Notice(data)
                            } else {
                                null
                            }
                        }

                        else -> Unknown(message)
                    } ?: Unknown(message)
                }

                is UCGiftMessage ->
                    with(message.gift) {
                        Gift(
                            message,
                            buildAnnotatedString {
                                append("送给".localized)
                                append(" ")
                                append(if (receivers.isEmpty()) "所有人".localized else receiverName)
                            },
                            buildAnnotatedString {
                                withStyle(style = SpanStyle(color = WakooGrayText, fontSize = 12.sp)) {
                                    append("${gift.name}x$count")
                                }
                            },
                        )
                    }

                else -> Unknown(message)
            }
    }

    data class Notice(
        val groupNotice: GroupNotice,
    ) : ChatGroupMsgEntry()

    data class Gift(
        val giftMsg: UCGiftMessage,
        val messageReceiver: AnnotatedString,
        val messageGift: AnnotatedString,
    ) : ChatGroupMsgEntry()

    data class SystemTip(
        val avatar: String? = null,
        val content: RichTextScope.() -> Unit = {},
    ) : ChatGroupMsgEntry()

    data class GroupRichText(val richTextList: List<RichItem>) : ChatGroupMsgEntry()

    data class GroupText(
        val data: UCTextMessage,
    ) : ChatGroupMsgEntry()

    data class GroupImage(
        val mediaItem: MediaViewerItem.Image,
        val width: Int,
        val height: Int,
        val user: IMUser,
    ) : ChatGroupMsgEntry()

    data class GroupVoice(
        val content: VoiceMessageContent,
    ) : ChatGroupMsgEntry()

    data class Unknown(
        val message: UCInstanceMessage,
    ) : ChatGroupMsgEntry() {
        val text: String
            get() = message.getSummaryString()
    }
}
