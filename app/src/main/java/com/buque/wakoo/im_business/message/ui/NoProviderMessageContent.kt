package com.buque.wakoo.im_business.message.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.sp
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
import com.buque.wakoo.utils.LogUtils


data class NoProviderMessageContent(val message: UCInstanceMessage) : MsgLayoutContent() {

    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction
    ) {
        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            Text(
                text = message.toMsgString(),
                color = Color(0xFF86909C),
                modifier = Modifier.noEffectClick(onClick = {
                    if (message is UCCustomMessage) {
                        LogUtils.d(message.cmd + "\n" + message.customJson.toString())
                    }
                }),
                fontSize = 12.sp
            )
        }
    }
}