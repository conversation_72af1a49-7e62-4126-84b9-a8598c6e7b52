package com.buque.wakoo.im_business.message.ui.custom

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.im.bean.MsgSendCondition
import com.buque.wakoo.im_business.interf.IC2CAction
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.icons.VipIcon
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.utils.eventBus.EventBus
import com.buque.wakoo.utils.eventBus.tryToLink

class MsgInterceptContent(
    val condition: MsgSendCondition,
    val hint: String,
) : MsgLayoutContent() {
    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction,
    ) {
        val selfUser = LocalSelfUserProvider.current
        val controller = LocalAppNavController.root

        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth(0.72f)
                        .background(color = Color.White, shape = RoundedCornerShape(8.dp))
                        .padding(horizontal = 16.dp, vertical = 20.dp),
            ) {
                Text(
                    hint,
                    color = Color(0xff999999),
                    fontSize = 15.sp,
                    lineHeight = 22.sp,
                    textAlign = TextAlign.Center,
                )
                SizeHeight(20.dp)
                if (condition.triggerBlockCard) {
                    Box(
                        modifier =
                            Modifier
                                .padding(horizontal = 8.dp)
                                .fillMaxWidth(),
                    ) {
                        GradientButton(
                            condition.blockCardBtnTxt,
                            onClick = {
                                (onAction as? IC2CAction)?.onAddOppositeAsFriend()
                            },
                            modifier =
                                Modifier
                                    .padding(top = 4.dp)
                                    .fillMaxWidth()
                                    .height(44.dp),
                        )
                        Image(
                            WakooIcons.VipIcon,
                            contentDescription = null,
                            modifier = Modifier.align(alignment = Alignment.TopEnd),
                        )
                    }
                }
                if (condition.kindlyRemindType == 1) {
                    GradientButton(
                        "充值钻石".localized,
                        onClick = {
                            controller.push(Route.Recharge)
                        },
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(44.dp),
                    )
                    SizeHeight(20.dp)
                    SolidButton(
                        "做任务领取钻石".localized,
                        onClick = {
                            EventBus.tryToLink("wakoo://page/main?tab=task")
                        },
                        textColor = Color(0xff66FE6B),
                        backgroundColor = Color(0xff111111),
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(44.dp),
                    )
                }
            }
        }
    }
}

@Composable
@Preview(showBackground = true)
private fun PreviewMsgInterceptContent() {
    MsgInterceptContent(
        MsgSendCondition(),
        "这是测试用的消息拦截消息展示",
    )
}
