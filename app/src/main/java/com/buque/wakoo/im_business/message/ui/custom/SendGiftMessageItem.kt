package com.buque.wakoo.im_business.message.ui.custom

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.GiftWall
import com.buque.wakoo.bean.IGift
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.im_business.interf.IC2CAction
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.manager.localizedFormatWithKey
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.image.NetworkImage

class SendFreeGiftMessageContent(
    val gift: IGift,
    val message: String,
    val buttonText: String,
    val enable: Boolean = true,
    // 是否为主播端的消息
    val isAnchorMsg: Boolean = false,
) : MsgLayoutContent() {
    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction,
    ) {
        SendGiftMessageItem(gift, message, buttonText, enable = enable) {
            (onAction as? IC2CAction)?.onSendFreeGift(isAnchorMsg)
        }
    }
}

@Composable
private fun SendGiftMessageItem(
    gift: IGift,
    message: String,
    buttonText: String,
    modifier: Modifier = Modifier,
    enable: Boolean = true,
    onClick: () -> Unit = {},
) {
    Row(
        modifier =
            modifier
                .padding(top = 18.dp, start = 12.dp, bottom = 18.dp, end = 28.dp)
                .background(Color.White, RoundedCornerShape(12.dp))
                .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Column(
            modifier =
                Modifier
                    .width(80.dp)
                    .padding(vertical = 6.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            NetworkImage(
                gift.icon,
                modifier =
                    Modifier
                        .border(width = 2.dp, color = Color(0xFFA3FF2C), shape = RoundedCornerShape(size = 12.dp))
                        .size(80.dp)
                        .background(color = Color(0x3B66FE6B), shape = RoundedCornerShape(size = 12.dp))
                        .padding(12.dp),
                contentScale = ContentScale.Inside,
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = gift.name,
                fontSize = 12.sp,
                color = WakooText,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(text = "%s钻石".localizedFormat(gift.price.toString()), fontSize = 12.sp, color = WakooText)
        }
        Spacer(modifier = Modifier.width(16.dp))
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(text = message, fontSize = 14.sp, color = Color(0xFF999999), lineHeight = 22.sp)
            Spacer(modifier = Modifier.height(14.dp))
            SolidButton(
                buttonText,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(40.dp)
                        .graphicsLayer {
                            alpha = if (enable) 1f else 0.5f
                        },
                backgroundColor = if (enable) Color(0xff111111) else Color(0xFFA5A5A5),
                textColor = if (enable) Color(0xFFA3FF2C) else Color.White,
                onClick = onClick,
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun Preview() {
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .background(Color.Black),
    ) {
        val g = GiftWall.GiftWrapper.Gift(name = "礼物名称", price = 5200)
        SendGiftMessageItem(
            gift = g,
            message = "你们聊的很愉快，系统赠送你一个“礼物名称”你可以选择合适的时机送给对方，对方很可能会愿意回礼哦",
            buttonText = "免费赠送",
        )
        Spacer(modifier = Modifier.height(20.dp))
        SendGiftMessageItem(
            gift = g,
            message = "你们聊的很愉快，系统赠送你一个“礼物名称”你可以选择合适的时机送给对方，对方很可能会愿意回礼哦",
            buttonText = "已赠送",
            enable = false,
        )
    }
}
