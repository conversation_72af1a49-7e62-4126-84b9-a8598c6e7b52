package com.buque.wakoo.im_business.message.ui.entry

import android.content.Context
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.sp
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.im_business.UIMessageUtils
import java.util.Locale
import kotlin.math.abs

data class TimeMsgEntry(
    val timestamp: Long,
) : MsgUIEntry {
    private var formatText: String = ""

    private var cacheTimestamp = 0L

    fun getMessageTimeFormatText(
        context: Context,
        locale: Locale = Locale.getDefault(),
    ): String {
        if (formatText.isEmpty() || checkInValid()) {
            formatText =
                UIMessageUtils.getMessageTimeFormatText(timestamp, locale)
            cacheTimestamp = System.currentTimeMillis()
        }
        return formatText
    }

    private fun checkInValid() = abs(System.currentTimeMillis().minus(cacheTimestamp)) >= 60_000
}

@Composable
fun TimeMsgEntry.C2CContent() {
    Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
        Text(
            text = getMessageTimeFormatText(WakooApplication.instance),
            color = Color(0xFF86909C),
            fontSize = 12.sp,
        )
    }
}
