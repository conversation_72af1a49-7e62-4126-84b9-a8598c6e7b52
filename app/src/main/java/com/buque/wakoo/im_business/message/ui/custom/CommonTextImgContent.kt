package com.buque.wakoo.im_business.message.ui.custom

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.ext.click
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.utils.eventBus.tryToLink

class CommonTextImgContent(
    val link: String,
    val title: String,
    val image: String,
) : MsgLayoutContent() {
    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction,
    ) {
        TuwenMessageCard(link, title, image)
    }
}

@Composable
private fun TuwenMessageCard(
    link: String,
    title: String,
    image: String,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(4.dp))
                .padding(horizontal = 16.dp)
                .click(time = 500L) {
                    link.tryToLink()
                },
    ) {
        NetworkImage(
            image,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .aspectRatio(2.35f),
        )
        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .background(color = WakooWhite)
                    .padding(horizontal = 16.dp, vertical = 10.dp),
        ) {
            Text(
                text = title,
                fontSize = 14.sp,
                color = Color(0xFF1D2129),
            )
        }
    }
}
