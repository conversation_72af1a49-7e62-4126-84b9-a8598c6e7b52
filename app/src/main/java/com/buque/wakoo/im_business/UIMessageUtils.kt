package com.buque.wakoo.im_business

import android.content.Context
import android.text.SpannableString
import android.text.Spanned
import androidx.core.graphics.toColorInt
import androidx.core.text.buildSpannedString
import androidx.core.text.color
import androidx.core.util.SizeFCompat
import com.buque.wakoo.bean.RichItem
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.getExtraBoolean
import com.buque.wakoo.im.getExtraTypeValue
import com.buque.wakoo.im.inter.IUCCustomMessage
import com.buque.wakoo.im.inter.IUCMessage
import com.buque.wakoo.im.isRevoked
import com.buque.wakoo.im.utils.takeIsNotEmpty
import com.buque.wakoo.im_business.message.HintExpansionExtra
import com.buque.wakoo.im_business.message.MsgExpansionExtra
import com.buque.wakoo.im_business.message.types.UCEmojiMessage
import com.buque.wakoo.im_business.message.types.UCGiftMessage
import com.buque.wakoo.im_business.message.types.UCImageMessage
import com.buque.wakoo.im_business.message.types.UCTextMessage
import com.buque.wakoo.im_business.message.types.UCVoiceMessage
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import java.util.concurrent.TimeUnit

object UIMessageUtils {
    // 发送方消息提示是否可见
    private const val SENDER_MSG_HINT_VISIBLE = "sender_msg_hint_visible"

    // 接收方消息提示是否可见
    private const val RECEIVER_MSG_HINT_VISIBLE = "receiver_msg_hint_visible"

    // 发送方要显示的富文本提示
    private const val SENDER_MSG_HINT_RICH_TEXT = "sender_msg_hint_rich_text"

    // 接收方要显示的富文本提示
    private const val RECEIVER_MSG_HINT_RICH_TEXT = "receiver_msg_hint_rich_text"

    // 发送方要显示的提示内容
    private const val SENDER_MSG_HINT_CONTENT = "sender_msg_hint_content"

    // 接收方要显示的提示内容
    private const val RECEIVER_MSG_HINT_CONTENT = "receiver_msg_hint_content"

    // 发送方要显示的icon
    private const val SENDER_ICON = "sender_icon"

    // 接收方要显示的icon
    private const val RECEIVER_ICON = "receiver_icon"

    // 是否被标记为垃圾消息
    const val CONTENT_ILLEGAL = "content_illegal"

    // 发送方消息是否要显示自定义摘要
    private const val SENDER_MSG_DIGEST_CUSTOMIZED = "sender_msg_digest_customized"

    // 接收方消息是否要显示自定义摘要
    private const val RECEIVER_MSG_DIGEST_CUSTOMIZED = "receiver_msg_digest_customized"

    // 发送方消息要显示的自定义摘要普通文本
    private const val SENDER_MSG_DIGEST_CONTENT = "sender_msg_digest_content"

    // 接收方消息要显示的自定义摘要普通文本
    private const val RECEIVER_MSG_DIGEST_CONTENT = "receiver_msg_digest_content"

    // 发送方消息要显示的自定义摘要富文本
    private const val SENDER_MSG_DIGEST_RICH_TEXT = "sender_msg_digest_rich_text"

    // 接收方消息要显示的自定义摘要富文本
    private const val RECEIVER_MSG_DIGEST_RICH_TEXT = "receiver_msg_digest_rich_text"

    // 发送方未播放语音消息是否需要自动播放
    private const val SENDER_VOICE_AUTO_PLAY = "sender_voice_auto_play"

    // 接收方未播放语音消息是否需要自动播放
    private const val RECEIVER_VOICE_AUTO_PLAY = "receiver_voice_auto_play"

    // 消息或会话是否展示
    private const val SENDER_VISIBLE = "sender_visible"

    // 消息或会话是否展示
    private const val RECEIVER_VISIBLE = "receiver_visible"

    private const val MinWidthDp = 120

    const val MaxWidthDp = 160

    private const val MinHeightDp = 120

    const val MaxHeightDp = 190

    fun getMessageSummary(
        context: Context,
        hasUnread: Boolean,
        message: UCInstanceMessage?,
    ): Spanned {
        val spannedString =
            buildSpannedString {
                if (message == null) {
                    append("快来和我打个招呼".localized)
                    return@buildSpannedString
                }

                if (message.isRevoked) {
                    val content =
                        if (message.isC2CMsg) {
                            "%s撤回了一条消息".localizedFormat(
                                if (message.isSelf) {
                                    "你".localized
                                } else {
                                    "对方".localized
                                },
                            )
                        } else {
                            "%s撤回了一条消息".localizedFormat(
                                if (message.isSelf) "你".localized else message.user?.name.orEmpty(),
                            )
                        }
                    append(content)
                    return@buildSpannedString
                }

                when {
                    message is UCTextMessage -> {
                        append((message.text.takeIsNotEmpty() ?: "  ").take(100))
                    }

                    message is UCImageMessage -> {
                        if (hasUnread && !message.isSelf) {
                            color("#FFFF469F".toColorInt()) {
                                append("[图片]".localized)
                            }
                        } else {
                            append("[图片]".localized)
                        }
                    }

                    message is UCVoiceMessage -> {
                        if (hasUnread && !message.isSelf) {
                            color("#FFFF469F".toColorInt()) {
                                append("[语音消息]".localized)
                            }
                        } else {
                            append("[语音消息]".localized)
                        }
                    }

                    message is UCGiftMessage -> {
                        append("[礼物消息]".localized)
                    }

                    message is UCEmojiMessage -> {
                        append("[互动表情]".localized)
                    }

//                message is UCVideoCallMessage.Other && message.showInMsgList -> {
//                    if (message.notice.isVoice) {
//                        append(context.getString(if (isUCOO) R.string._语音通话_ else R.string.cpd语音通话))
//                    } else {
//                        append(context.getString(if (isUCOO) R.string._视频通话_ else R.string.cpd视频通话))
//                    }
//                }

                    message is IUCCustomMessage -> {
//                    if (message.cmd == MsgEventCmd.TRIBE_CUSTOM_CONTENT) {
//                        append(context.getString(R.string._部落消息_))
//                    } else {
                        append(message.summary.takeIsNotEmpty() ?: "[系统通知]".localized)
//                    }
                    }

                    else -> Unit
                }
            }
        return spannedString.takeIsNotEmpty() ?: SpannableString.valueOf(
            "暂不支持此消息类型请升级版本".localized,
        )
    }

    fun hasAutoPlayFlagByVoiceMessage(message: UCVoiceMessage): Boolean =
        message.getExtraBoolean(if (message.isSelf) SENDER_VOICE_AUTO_PLAY else RECEIVER_VOICE_AUTO_PLAY, false)

    fun isVisible(message: IUCMessage): Boolean = message.getExtraBoolean(if (message.isSelf) SENDER_VISIBLE else RECEIVER_VISIBLE, true)

    fun resizeImage(
        originalSize: SizeFCompat,
        minSize: SizeFCompat = SizeFCompat(MinWidthDp.toFloat(), MinHeightDp.toFloat()),
        maxSize: SizeFCompat = SizeFCompat(MaxWidthDp.toFloat(), MaxHeightDp.toFloat()),
    ): SizeFCompat {
        val originalWidth = originalSize.width
        val originalHeight = originalSize.height

        val minWidth = minSize.width
        val minHeight = minSize.height

        val maxWidth = maxSize.width
        val maxHeight = maxSize.height

        // Calculate the aspect ratio
        val aspectRatio = originalWidth / originalHeight

        // Initialize the new width and height to original values
        var newWidth = originalWidth
        var newHeight = originalHeight

        // Adjust the size to fit within the max size constraints while maintaining aspect ratio
        if (newWidth > maxWidth) {
            newWidth = maxWidth
            newHeight = newWidth / aspectRatio
        }
        if (newHeight > maxHeight) {
            newHeight = maxHeight
            newWidth = newHeight * aspectRatio
        }

        // Adjust the size to be at least the min size constraints while maintaining aspect ratio
        if (newWidth < minWidth) {
            newWidth = minWidth
            newHeight = newWidth / aspectRatio
        }
        if (newHeight < minHeight) {
            newHeight = minHeight
            newWidth = newHeight * aspectRatio
        }

        // Return the new size rounded to integers
        return SizeFCompat(newWidth, newHeight)
    }

    fun getMessageTimeFormatText(
        timestamp: Long,
        locale: Locale = Locale.getDefault(),
    ): String {
        if (timestamp <= 0L) return ""
        val calendar = Calendar.getInstance()
        val current = calendar.timeInMillis
        val diff = current - timestamp
        val todayZero =
            calendar.run {
                set(Calendar.HOUR_OF_DAY, 0)
                set(Calendar.MINUTE, 0)
                set(Calendar.SECOND, 0)
                set(Calendar.MILLISECOND, 0)
                timeInMillis
            }
        return if (timestamp >= todayZero) { // 今天
            when {
                diff < TimeUnit.MINUTES.toMillis(1) -> {
                    "刚刚".localized
                }

                diff < TimeUnit.HOURS.toMillis(1) -> {
                    "%d分钟前".localizedFormat(TimeUnit.MILLISECONDS.toMinutes(diff))
                }

                else -> {
                    "%d小时前".localizedFormat(TimeUnit.MILLISECONDS.toHours(diff))
                }
            }
        } else if (timestamp >= todayZero.minus(TimeUnit.DAYS.toMillis(1))) { // 昨天
            "昨天 %s".localizedFormat(
                SimpleDateFormat("HH:mm", locale).format(timestamp),
            )
        } else if (timestamp >= todayZero.minus(TimeUnit.DAYS.toMillis(2))) { // 前天
            "前天 %s".localizedFormat(
                SimpleDateFormat("HH:mm", locale).format(timestamp),
            )
        } else {
            val thisYearMillis =
                calendar.run {
                    set(Calendar.MONTH, 0)
                    set(Calendar.DATE, 1)
                    set(Calendar.HOUR_OF_DAY, 0)
                    set(Calendar.MINUTE, 0)
                    set(Calendar.SECOND, 0)
                    set(Calendar.MILLISECOND, 0)
                    timeInMillis
                }
            if (timestamp < thisYearMillis) { // 去年
                SimpleDateFormat("yyyy-MM-dd HH:mm", locale).format(timestamp)
            } else {
                SimpleDateFormat("MM-dd HH:mm", locale).format(timestamp)
            }
        }
    }

    fun parseMessageExtra(message: IUCMessage): MsgExpansionExtra? {
        val visible =
            message.getExtraBoolean(
                if (message.isSelf) SENDER_MSG_HINT_VISIBLE else RECEIVER_MSG_HINT_VISIBLE,
                false,
            )
        if (!visible) {
            return null
        }
        val richList =
            message.getExtraTypeValue<List<RichItem>>(if (message.isSelf) SENDER_MSG_HINT_RICH_TEXT else RECEIVER_MSG_HINT_RICH_TEXT)
        val hintExtra =
            if (!richList.isNullOrEmpty()) {
                HintExpansionExtra(hintVisible = true, isSelf = message.isSelf, hintRichList = richList)
            } else {
                HintExpansionExtra(
                    hintVisible = true,
                    isSelf = message.isSelf,
                    hintText = message.getExtraValue(if (message.isSelf) SENDER_MSG_HINT_CONTENT else RECEIVER_MSG_HINT_CONTENT),
                    hintIcon = message.getExtraValue(if (message.isSelf) SENDER_ICON else RECEIVER_ICON),
                )
            }
        return MsgExpansionExtra(hint = hintExtra)
    }

    fun parseIMConversationExtra(message: IUCMessage): HintExpansionExtra? {
        val custom =
            message.getExtraBoolean(
                if (message.isSelf) SENDER_MSG_DIGEST_CUSTOMIZED else RECEIVER_MSG_DIGEST_CUSTOMIZED,
                false,
            )
        if (!custom) {
            return null
        }
        val richList =
            message.getExtraTypeValue<List<RichItem>>(if (message.isSelf) SENDER_MSG_DIGEST_RICH_TEXT else RECEIVER_MSG_DIGEST_RICH_TEXT)
        val hintExtra =
            if (!richList.isNullOrEmpty()) {
                HintExpansionExtra(hintVisible = true, isSelf = message.isSelf, hintRichList = richList)
            } else {
                HintExpansionExtra(
                    hintVisible = true,
                    isSelf = message.isSelf,
                    hintText = message.getExtraValue(if (message.isSelf) SENDER_MSG_DIGEST_CONTENT else RECEIVER_MSG_DIGEST_CONTENT),
                    hintIcon = message.getExtraValue(if (message.isSelf) SENDER_ICON else RECEIVER_ICON),
                )
            }
        return hintExtra
    }
}
