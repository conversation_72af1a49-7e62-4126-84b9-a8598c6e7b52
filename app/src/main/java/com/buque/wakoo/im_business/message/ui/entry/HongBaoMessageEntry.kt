package com.buque.wakoo.im_business.message.ui.entry

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.ext.click
import com.buque.wakoo.im.bean.IMUser
import com.buque.wakoo.ui.screens.liveroom.HongBaoMessage
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.panel.LiveRoomUserInfoPanel
import com.buque.wakoo.ui.screens.messages.chat.PublishCpMedal
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.VipCrownTag
import com.buque.wakoo.ui.widget.appendUserLevel
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.richtext.RichText

data class HongBaoMessageEntry(
    val user: IMUser,
    val id: String,
    val title: String,
) : MsgUIEntry

@Composable
fun HongBaoMessageEntry.RoomContent(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
    onShow: (String) -> Unit = {},
) {
    val density = LocalDensity.current
    val fontSize =
        with(density) {
            14.dp.toPx().toSp()
        }

    val lineHeight =
        with(density) {
            26.dp.toPx().toSp()
        }

    val isCN = !LocalSelfUserProvider.isJP

    Column(modifier = modifier.padding(end = 35.dp)) {
        Row {
            AvatarNetworkImage(
                user = user,
                size = 28.dp,
                onClick = {
                    roomInfoState.sendEvent(
                        RoomEvent.PanelDialog {
                            LiveRoomUserInfoPanel(user, roomInfoState)
                        },
                    )
                },
            )

            SizeWidth(8.dp)

            RichText(
                fontSize = fontSize,
                color = Color.White,
                lineHeight = lineHeight,
                modifier = Modifier.padding(top = 1.dp),
            ) {
                append(user.name)
                if (user.isVip) {
                    append("  ")
                    InlineSizedContent(48.dp, 18.dp) {
                        VipCrownTag()
                    }
                }

                append(" ")
                appendUserLevel(isCN = isCN, user = user)

                if (isCN) {
                    user.cpUrl?.takeIf { it.isNotBlank() }?.also {
                        append(" ")
                        InlineSizedContent(64.dp, 18.dp) {
                            PublishCpMedal(
                                publicCp = BasicUser("", avatar = user.cpAvatar.orEmpty()),
                                publicCpMedalUrl = it,
                                modifier =
                                    Modifier.size(64.dp, 18.dp),
                            )
                        }
                    }
                }

                user.medalList?.forEach {
                    append(" ")
                    InlineSizedContent(it.width.dp, it.height.dp) {
                        NetworkImage(
                            data = it.icon,
                            modifier = Modifier.size(it.width.dp, it.height.dp),
                        )
                    }
                }
            }
        }

        HongBaoMessage(
            title,
            modifier =
                Modifier
                    .padding(start = 28.dp)
                    .padding(6.dp)
                    .click(onClick = {
                        onShow(id)
                    }),
        )
    }
}
