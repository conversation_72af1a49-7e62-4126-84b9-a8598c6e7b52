package com.buque.wakoo.im_business.tim

import androidx.annotation.IntDef
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.CompactJson
import com.buque.wakoo.ext.getStringOrNull
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.UCMessage.Companion.NoSet
import com.buque.wakoo.im.bean.IMUser
import com.buque.wakoo.im.bean.MessageAtInfo
import com.buque.wakoo.im.bean.MsgSendStatus
import com.buque.wakoo.im.bean.SendIMUser
import com.buque.wakoo.im.isSent
import com.buque.wakoo.im.utils.IMUtils
import com.buque.wakoo.im.utils.asInstance
import com.buque.wakoo.im.utils.takeIsNotEmpty
import com.tencent.imsdk.conversation.ConversationAtInfo
import com.tencent.imsdk.v2.V2TIMManager
import com.tencent.imsdk.v2.V2TIMMessage
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.booleanOrNull
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.doubleOrNull
import kotlinx.serialization.json.floatOrNull
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.longOrNull
import kotlinx.serialization.json.put

class TIMMessage constructor(
    message: V2TIMMessage,
    bundle: MessageBundle? = null,
) : UCMessage {
    val rawMessage: V2TIMMessage = message

    @Volatile
    private var innerImUser: IMUser? = null

    /**
     * 消息id, 一般消息发送后才有
     */
    override val id: String
        get() = rawMessage.msgID

    /**
     * 消息时间戳
     */
    override val timestamp: Long
        get() = rawMessage.timestamp * 1000

    /**
     * 发送者id
     */
    override val sender: String
        get() = rawMessage.sender.orEmpty()

    /**
     * 接受者id
     * 单聊为对方的用户id
     * 群聊为群id
     */
    override val receiver: String
        get() = rawMessage.userID ?: rawMessage.groupID.orEmpty()

    /**
     * 是否是自己发送的
     */
    override val isSelf: Boolean
        get() = rawMessage.isSelf

    /**
     * 消息的发送状态
     */
    override val sendStatus: MsgSendStatus
        get() =
            when (rawMessage.status) {
                V2TIMMessage.V2TIM_MSG_STATUS_SENDING -> MsgSendStatus.Sending
                V2TIMMessage.V2TIM_MSG_STATUS_SEND_FAIL -> MsgSendStatus.Failure
                V2TIMMessage.V2TIM_MSG_STATUS_LOCAL_REVOKED -> MsgSendStatus.Revoked
                V2TIMMessage.V2TIM_MSG_STATUS_HAS_DELETED -> MsgSendStatus.Idea
                V2TIMMessage.V2TIM_MSG_STATUS_SEND_SUCC, V2TIMMessage.V2TIM_MSG_STATUS_LOCAL_IMPORTED -> MsgSendStatus.Sent
                else -> MsgSendStatus.Sending
            }

    /**
     * 消息附带的@信息
     */
    override val groupAtInfo: MessageAtInfo?

    /**
     * 消息发送方的user
     */
    override val senderUser: IMUser?
        get() =
            innerImUser ?: cloudCustomData[CLOUD_CUSTOM_KEY_USER]
                ?.let {
                    it as? IMUser
                        ?: if (it is SendIMUser) {
                            it.toIMUser().also { user ->
                                innerImUser = user
                            }
                        } else {
                            null
                        }
                }?.takeIf { it.id.isNotEmpty() && it.name.isNotEmpty() }

    override val sequence: Long
        get() = rawMessage.seq

    override val isNeedReadReceipt: Boolean
        get() = rawMessage.isNeedReadReceipt && !isC2CRead
    override val isExcludedFromLastMessage: Boolean
        get() = rawMessage.isExcludedFromLastMessage

    /**
     * 单聊消息，消息接收方是否已读
     */
    override val isC2CRead: Boolean
        get() =
            if (isSelf) {
                rawMessage.isPeerRead
            } else {
                rawMessage.isRead
            }

    /**
     * 静态是否已读
     */
    override val isC2CStaticRead: Boolean = isC2CRead

    /**
     * 是否支持消息扩展
     */
    override val isSupportMessageExtension: Boolean
        get() = true

    /**
     * 是否已经播放，比如音频、视频、礼物是否已播放
     */
    override var isPlayed: Boolean
        get() = hasIntFlag(STATE_PLAYED)
        set(value) {
            if (value) {
                markIntFlag(STATE_PLAYED)
            } else {
                clearIntFlag(STATE_PLAYED)
            }
        }

    /**
     * 是否已下载，比如文件是否已下载
     */
    override var isDownloaded: Boolean
        get() = hasIntFlag(STATE_DOWNLOADED)
        set(value) {
            if (value) {
                markIntFlag(STATE_DOWNLOADED)
            } else {
                clearIntFlag(STATE_DOWNLOADED)
            }
        }

    /**
     * 是否是单聊消息，腾讯消息只能区分单聊和群组
     */
    override val isC2CMsg: Boolean
        get() = rawMessage.userID != null

    /**
     * 是否是系统消息
     */
    override val isSystemMsg: Boolean
        get() = sender == SYSTEM_MSG_TYPE

    /**
     * 用于获取扩展字段
     */
    override fun getExtraValue(key: String): String? =
        if (isSupportMessageExtension) {
            extensions?.data?.get(key)?.takeIsNotEmpty()
        } else {
            null
        }

    /**
     * 获取翻译文本
     * @param code 语言代码，如 zh-CN, en-US
     */
    override fun getTranslateTextByCode(code: String): String? =
        localCustomData[LOCAL_CUSTOM_KEY_TRANSLATE]?.asInstance<JsonObject>()?.getStringOrNull(code)

    override fun saveTranslateText(
        code: String,
        text: String,
    ) {
        updateLocalCustomData(
            LOCAL_CUSTOM_KEY_TRANSLATE,
            buildJsonObject {
                localCustomData[LOCAL_CUSTOM_KEY_TRANSLATE]?.asInstance<JsonObject>()?.forEach { key, value ->
                    put(key, value)
                }
                put(code, text)
            },
        )
    }

    /**
     * {
     *    "user": {
     *       "name": "abc"
     *    },
     *    "extensions": {
     *        "content": {
     *            "key1": "value1",
     *            "key2": "value2",
     *            "key3": "value3"
     *        },
     *        "addKeys": [
     *            "key1"
     *        ],
     *        "removeKeys": [
     *            "key4"
     *        ],
     *        "modifyKeys": [
     *            "key3"
     *        ]
     *    }
     * }
     */
    private val cloudCustomData: MutableMap<String, Any?>

    /**
     * {
     *     "translateTexts": {
     *         "code1": "text1",
     *         "code2": "text2"
     *     },
     *     "imageSize": {
     *         "width": 0,
     *         "height": 0
     *     }
     * }
     */
    private val localCustomData: MutableMap<String, Any?>

    internal val extensions: TimMsgExtensions?
        get() =
            cloudCustomData[CLOUD_AND_LOCAL_CUSTOM_KEY_EXTENSIONS]?.asInstance<TimMsgExtensions>()
                ?: localCustomData[CLOUD_AND_LOCAL_CUSTOM_KEY_EXTENSIONS]?.asInstance<TimMsgExtensions>()

    companion object {
        private const val SYSTEM_MSG_TYPE = "system"

        @IntDef(STATE_PLAYED, STATE_DOWNLOADED)
        @Target(
            AnnotationTarget.FIELD,
            AnnotationTarget.VALUE_PARAMETER,
            AnnotationTarget.PROPERTY,
            AnnotationTarget.TYPE,
        )
        @Retention(AnnotationRetention.SOURCE)
        private annotation class Flag

        /**
         * 是否已经播放，比如音频、视频、礼物是否已播放
         */
        private const val STATE_PLAYED = 1 shl 0 // 0001

        /**
         * 是否已下载，比如文件是否已下载
         */
        private const val STATE_DOWNLOADED = 1 shl 1 // 0010

        // /////////////////////////////////////////////////////////////////////////
        // 云端自定义数据预置key
        // /////////////////////////////////////////////////////////////////////////
        const val CLOUD_CUSTOM_KEY_USER = "user"

        const val CLOUD_AND_LOCAL_CUSTOM_KEY_EXTENSIONS = "extensions"
        // /////////////////////////////////////////////////////////////////////////
        // 云端自定义数据预置key
        // /////////////////////////////////////////////////////////////////////////

        // /////////////////////////////////////////////////////////////////////////
        // 本地自定义数据预置key
        // /////////////////////////////////////////////////////////////////////////
        private const val LOCAL_CUSTOM_KEY_TRANSLATE = "translateTexts"

        private const val LOCAL_CUSTOM_KEY_IMAGE_SIZE = "imageSize"
        // /////////////////////////////////////////////////////////////////////////
        // 本地自定义数据预置key
        // /////////////////////////////////////////////////////////////////////////
    }

    init {
        cloudCustomData =
            mutableMapOf<String, Any?>().apply {
                rawMessage.cloudCustomData?.takeIf { it.isNotEmpty() }?.also {
                    try {
                        putAll(
                            IMUtils.parseJsonToMap(it) { key, value ->
                                when (key) {
                                    CLOUD_CUSTOM_KEY_USER -> {
                                        try {
                                            AppJson.decodeFromJsonElement<SendIMUser?>(value).also { user ->
                                                innerImUser = user?.toIMUser()
                                            }
                                        } catch (e: Exception) {
                                            innerImUser = null
                                            null
                                        }
                                    }

                                    CLOUD_AND_LOCAL_CUSTOM_KEY_EXTENSIONS -> {
                                        try {
                                            AppJson.decodeFromJsonElement<TimMsgExtensions?>(value)
                                        } catch (e: Exception) {
                                            null
                                        }
                                    }

                                    else -> null
                                }
                            },
                        )
                    } catch (_: Exception) {
                    }
                }
            }

        localCustomData =
            mutableMapOf<String, Any?>().apply {
                TIMEngine.getLocalCustomData(rawMessage)?.takeIf { it.isNotEmpty() }?.also {
                    try {
                        putAll(
                            IMUtils.parseJsonToMap(it) { key, value ->
                                when (key) {
                                    LOCAL_CUSTOM_KEY_TRANSLATE -> {
                                        value.jsonObject
                                    }

                                    LOCAL_CUSTOM_KEY_IMAGE_SIZE -> {
                                        try {
                                            AppJson.decodeFromJsonElement<ImageSize?>(value)
                                        } catch (e: Exception) {
                                            null
                                        }
                                    }

                                    CLOUD_AND_LOCAL_CUSTOM_KEY_EXTENSIONS -> {
                                        try {
                                            AppJson.decodeFromJsonElement<TimMsgExtensions?>(value)
                                        } catch (e: Exception) {
                                            null
                                        }
                                    }

                                    else -> null
                                }
                            },
                        )
                    } catch (_: Exception) {
                    }
                }
            }

        if (bundle is MessageBundle.Image) {
            updateLocalCustomData(LOCAL_CUSTOM_KEY_IMAGE_SIZE, ImageSize(bundle.width, bundle.height))
        }

        groupAtInfo =
            rawMessage.groupAtUserList?.takeIf { it.isNotEmpty() }?.let { list ->
                var withAtAll = false
                MessageAtInfo(
                    atUsers =
                        list.mapNotNull {
                            if (it == ConversationAtInfo.AT_ALL_TAG) {
                                withAtAll = true
                                null
                            } else {
                                it
                            }
                        },
                    withAtAll = withAtAll,
                )
            }
    }

    private fun markIntFlag(
        @Flag flag: Int,
    ) {
        var state = TIMEngine.getLocalCustomInt(rawMessage)
        state = state or flag
        TIMEngine.setLocalCustomInt(rawMessage, state)
    }

    private fun clearIntFlag(
        @Flag flag: Int,
    ) {
        var state = TIMEngine.getLocalCustomInt(rawMessage)
        state = state and flag.inv()
        TIMEngine.setLocalCustomInt(rawMessage, state)
    }

    private fun hasIntFlag(
        @Flag flag: Int,
    ): Boolean {
        val state = TIMEngine.getLocalCustomInt(rawMessage)
        return state and flag != 0
    }

    /**
     * 更新CloudCustomData
     * 只有发送消息前修改，然后调用消息发送之后才能被持久化保持在本地
     * value 必须是可以支持serialization序列化的类型，否则会报错
     */
    fun updateCloudCustomData(entries: Map<String, Any?>) {
        var changed = false
        entries.forEach { (key, value) ->
            if (value === NoSet) {
                return@forEach
            }
            val oldValue =
                when (key) {
                    CLOUD_CUSTOM_KEY_USER -> {
                        if (value == null || value is SendIMUser) {
                            innerImUser = value?.toIMUser()
                            cloudCustomData.put(key, value)
                        } else {
                            NoSet
                        }
                    }

                    CLOUD_AND_LOCAL_CUSTOM_KEY_EXTENSIONS -> {
                        if (value == null || value is TimMsgExtensions) {
                            cloudCustomData[key] = value
                        } else {
                            NoSet
                        }
                    }

                    else -> {
                        cloudCustomData.put(
                            key,
                            if (value is JsonPrimitive) {
                                // 如果是基本数据类型，返回其原始值
                                value.booleanOrNull
                                    ?: value.intOrNull
                                    ?: value.longOrNull
                                    ?: value.floatOrNull
                                    ?: value.doubleOrNull
                                    ?: value.content
                            } else {
                                value
                            },
                        )
                    }
                }
            if (oldValue === NoSet) {
                return@forEach
            }
            if (!changed && oldValue != value) {
                changed = true
            }
        }
        if (changed) {
            rawMessage.cloudCustomData = CompactJson.encodeToString(cloudCustomData)
            if (isSent) { // 必须是发送成功的消息才能修改
                V2TIMManager.getMessageManager().modifyMessage(rawMessage, null)
            }
        }
    }

    /**
     * 更新CloudCustomData
     * 只有发送消息前修改，然后调用消息发送之后才能被持久化保持在本地
     * value 必须是可以支持serialization序列化的类型，否则会报错
     */
    fun updateCloudCustomData(
        key: String,
        value: Any?,
    ) {
        updateCloudCustomData(mapOf(key to value))
    }

    /**
     * 更新LocalCustomData
     * value 必须是可以支持serialization序列化的类型，否则会报错
     */
    fun updateLocalCustomData(entries: Map<String, Any?>) {
        var changed = false
        entries.forEach { (key, value) ->
            if (value === NoSet) {
                return@forEach
            }
            val oldValue =
                when (key) {
                    LOCAL_CUSTOM_KEY_TRANSLATE -> {
                        if (value == null || value is JsonObject) {
                            localCustomData.put(key, value)
                        } else {
                            NoSet
                        }
                    }

                    LOCAL_CUSTOM_KEY_IMAGE_SIZE -> {
                        if (value == null || value is ImageSize) {
                            localCustomData.put(key, value)
                        } else {
                            NoSet
                        }
                    }

                    CLOUD_AND_LOCAL_CUSTOM_KEY_EXTENSIONS -> {
                        if (value == null || value is TimMsgExtensions) {
                            localCustomData[key] = value
                        } else {
                            NoSet
                        }
                    }

                    else -> {
                        localCustomData.put(
                            key,
                            if (value is JsonPrimitive) {
                                // 如果是基本数据类型，返回其原始值
                                value.booleanOrNull
                                    ?: value.intOrNull
                                    ?: value.longOrNull
                                    ?: value.floatOrNull
                                    ?: value.doubleOrNull
                                    ?: value.content
                            } else {
                                value
                            },
                        )
                    }
                }
            if (oldValue === NoSet) {
                return@forEach
            }
            if (!changed && oldValue != value) {
                changed = true
            }
        }
        if (changed) { // 发送中的消息不能修改
            TIMEngine.setLocalCustomData(rawMessage, CompactJson.encodeToString(localCustomData))
        }
    }

    /**
     * 更新LocalCustomData
     * value 必须是可以支持serialization序列化的类型，否则会报错
     */
    fun updateLocalCustomData(
        key: String,
        value: Any?,
    ) {
        updateLocalCustomData(mapOf(key to value))
    }

    /**
     * 发消息的时候设置自己的用户信息
     */
    fun setSendIMUser(user: SendIMUser) {
        updateCloudCustomData(CLOUD_CUSTOM_KEY_USER, user)
    }

    /**
     * 获取图片临时尺寸
     */
    fun getImageSize() = localCustomData[LOCAL_CUSTOM_KEY_IMAGE_SIZE]?.asInstance<ImageSize>()
}
