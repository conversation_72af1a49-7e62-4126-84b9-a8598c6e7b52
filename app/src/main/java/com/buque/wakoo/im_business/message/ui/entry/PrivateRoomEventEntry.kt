package com.buque.wakoo.im_business.message.ui.entry

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage

data class PrivateRoomEventEntry(
    val id: String,
    val leftUser: User,
    val rightUser: User,
    val content: String,
    val buttonText: String?,
) : MsgUIEntry

@Composable
fun PrivateRoomEventEntry.C2CContent(modifier: Modifier = Modifier) {
    Column(
        modifier =
            modifier
                .padding(horizontal = 40.dp)
                .fillMaxWidth()
                .clip(RoundedCornerShape(12.dp))
                .background(
                    brush =
                        Brush.verticalGradient(
                            0f to Color(0xFFFFEEF5),
                            1f to Color(0xFFFFFFFF),
                        ),
                ).border(0.5.dp, Color(0xFFFFD2E2), RoundedCornerShape(12.dp))
                .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp),
    ) {
        Row(horizontalArrangement = Arrangement.spacedBy((-8).dp)) {
            AvatarNetworkImage(
                user = leftUser,
                size = 48.dp,
                enabled = false,
                border = BorderStroke(1.dp, Color(0xFFFFD2E2)),
            )

            AvatarNetworkImage(
                user = rightUser,
                size = 48.dp,
                enabled = false,
                border = BorderStroke(1.dp, Color(0xFFFFD2E2)),
            )
        }

        Text(
            text = content,
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFFFE669E),
            textAlign = TextAlign.Center,
        )

        if (!buttonText.isNullOrBlank()) {
            SolidButton(
                text = buttonText,
                backgroundColor = Color(0xFFFE669E),
                textColor = WakooWhite,
                fontSize = 14.sp,
                minWidth = 136.dp,
                height = 32.dp,
                onClick = {
                    LiveRoomManager.joinRoom(roomId = id, isPrivateRoom = true)
                },
            )
        }
    }
}

@Preview
@Composable
private fun PreviewPrivateRoomEventEntryC2cContent() {
    PrivateRoomEventEntry(
        "",
        BasicUser.sampleBoy,
        BasicUser.sampleBoy,
        "幼儿园搬花已经进入了私密小屋",
        "加入互动",
    ).C2CContent()
}
