package com.buque.wakoo.im_business.message.ui.entry

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.ui.screens.crony.CronyMessage
import com.buque.wakoo.ui.screens.crony.CronyMessageCard

/**
 * 亲友团消息
 */
data class CronyMessageEntry(val accepted: <PERSON><PERSON><PERSON>, val message: CronyMessage) : MsgUIEntry {

    @Composable
    fun C2CContent() {
        CronyMessageCard(accepted, message)
    }
}

/**
 * 接受邀请
 */
data class CronyAcceptEntry(val annotatedString: AnnotatedString) : MsgUIEntry


@Composable
fun CronyAcceptEntry.C2CContent() {
    Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
        Box(
            modifier = Modifier
                .widthIn(max = 312.dp)
                .background(Color(0xFFEBEEF4), RoundedCornerShape(8.dp))
                .padding(12.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(annotatedString, fontSize = 16.sp, lineHeight = 22.sp)
        }
    }
}