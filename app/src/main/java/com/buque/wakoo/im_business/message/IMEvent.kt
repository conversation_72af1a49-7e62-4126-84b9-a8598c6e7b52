package com.buque.wakoo.im_business.message

object IMEvent {
    // 完成任务活动奖励
    const val TASK_REWARD = "japan_task_finished"

    const val REGISTER_MALE_REWARD = "japan_male_register_bonus_popup"
    const val REGISTER_FEMALE_REWARD = "japan_female_register_bonus_popup"

    const val EXCHANGE_CONTACT = "exchange_contact"

    const val FAMILY_MIC_TASK_IN_VOICE_ROOM = "tribe_common_mic_status_sync"

    const val FAMILY_MIC_TASK_IN_VOICE_ROOM_DONE = "tribe_common_mic_task_done"

    const val JAPAN_FIRST_CHARGE_AWARD = "japan_first_charge_award"

    const val PRIVATECHAT_JP_FEMALE_FIRST_REPLY_MSG = "privatechat_jp_female_first_reply_msg"

    const val JAPAN_WITHDRAW_COUPON_CODE = "japan_withdraw_coupon_code"

    const val JAPAN_AUDIOROOM_OWNER_GET_MIC_POPUP_SHARE = "japan_audioroom_owner_get_mic_popup_share"

    const val PRIVATECHAT_JP_INTIMATE_GIFT_MSG = "privatechat_jp_intimate_gift_msg"

    const val PRIVATECHAT_JP_TRANSFER_RECORD = "transfer_record"

    const val VOICE_MIC_ABANDONED = "mic_abandoned"

    const val CP_INTER_PRIVATE_ROOM = "cp_inter_private_room"

    const val AUDIOROOM_WISHLIST_CHANGE = "audioroom_wishlist_change"

    const val INTIMATE_SCORE_CHANGE = "intimate_score_change"

    // 用户加入
    const val USER_ENTRANCE = "user_enterance"

    // 用户离开
    const val USER_EXIT = "user_exit"

    // 房间属性修改
    const val ROOM_SETTINGS = "room_settings_change"

    // 房间座位情况变化
    const val SEATS_CHANGE = "seats_change"

    // 用户申请上麦
    const val APPLY_MIC = "apply_mic"

    // 上麦申请数量变化
    const val MIC_APPLY_COUNT = "mic_apply_cnt_change"

    // 邀请用户上麦
    const val INVITE_MIC = "invite_mic"

    // 同意用户上麦申请
    const val AGREE_MIC = "agree_mic"

    // 拒绝用户上麦申请
    const val REFUSE_MIC = "refuse_mic_apply"

    // 房主抱下麦
    const val TAKEAWAY_MIC = "takeaway_mic"

    // 授权管理员
    const val GRANT_ADMIN = "grant_admin"

    // 取消管理员
    const val REVOKE_ADMIN = "revoke_admin"

    // 拉黑
    const val BLACK_USER = "black_user"

    // 礼物
    const val GIVE_GIFT = "give_gift"

    // 乞讨礼物
    const val BEG_GIFT = "beg_gift"

    // 需要添加好友提示
    const val NEED_ADD_FRIENDS = "need_add_friends"

    // 已经成为好友提示
    const val WE_BECAME_FRIENDS = "we_became_friends"

    // 私密小屋邀请
    const val INVITE_PRIVATE_ROOM = "invite_private_room"

    // 私密小屋邀请（cp模式房间下，全局显示）
    const val INVITE_PRIVATE_ROOM2 = "invite_join_privateroom"

    // 私密小屋已进入
    const val JOINED_THE_PRIVATE_ROOM = "accept_private_room"

    // 拒绝进入私密小屋
    const val REFUSE_THE_PRIVATE_ROOM = "refuse_private_room"

    // 部落邀请
    const val INVITE_TO_TRIBE = "tribe_invite"

    // 语音房邀请
    const val INVITE_TO_ROOM = "audioroom_share"

    // 表白礼物
    const val GIVE_CONFESSION_GIFT = "give_confession_gift"

    // 表白回礼，结成cp
    const val CONFIRM_CP = "confirm_cp"

    // 最佳匹配系统通知
    const val BEST_MATCH_DIALOG = "best_match"

    // 私聊系统提示
    const val PRIVATE_SYSTEM_HINT = "privatechat_system_hint"

    const val PRIVATE_SYSTEM_HINT_V2 = "privatechat_system_hint_v2"

    // 成为了好友
    const val MAKE_FRIENDS_HINTS = "make_friends"

    // 邀请加好友
    const val INVITE_MAKE_FRIEND = "invite_make_friend"

    // 部落成员加入
    const val MEMBER_JOIN = "member_join"

    // 部落成员被踢
    const val MEMBER_KICKED_OUT = "member_kicked_out"

    // 成员退出部落
    const val MEMBER_QUIT = "member_quit"

    // 家族公告更新
    const val BULLETIN_UPDATE = "bulletin_update"

    // 家族名变更
    const val NAME_UPDATE = "name_update"

    // 男生为女生开会员
    const val PRIVATE_ROOM_TIPS = "open_member_for_you"

    // 部落申请数变化
    const val MEMBER_APPLY_CNT_CHANGE = "member_apply_cnt_change"

    // 部落销毁
    const val TRIBE_DESTROYED = "tribe_destroyed"

    // 私密小屋互动
    const val PRIVATE_ROOM_INTERACT = "private_room_interact_status_sync"

    // 官宣
    const val CP_PUBLIC = "cp_public"

    // 注册成功任务弹窗
    const val COMMON_H5_DIALOG_1 = "new_broadcaster_instruct_task_popup"

    // 任务完成奖励弹窗
    const val COMMON_H5_DIALOG_2 = "broadcaster_task_finished_popup"

    // 通用h5弹窗
    const val COMMON_H5_DIALOG = "common_h5_link_popup"

    // 好友变化
    const val FRIENDSHIP_CHANGE = "friendship_change"

    // 盲盒信息变化
    const val CP_BLIND_BOX_DURATION_SYNC = "cp_blind_box_duration_sync"

    // 最佳匹配
    const val BEST_MATCH_ACCEPT = "best_match_accept"

    // 新人私密小屋邀请
    const val NEWBIE_INVITE_PRIVATE_ROOM = "newbie_invite_private_room"

    // 互动表情
    const val INTERACTIVE_MSG = "interactiveMsg"

    // 语音房麦位互动表情
    const val AUDIOROOM_SEND_EMOJI = "audioroom_send_emoji"

    // 语音匹配邀请
    const val VOICE_MATCH_INVITE = "random_speech_invite"

    // 语音匹配确认
    const val VOICE_MATCH_CONFIRM = "random_speech_invitee_accept"

    // 语音匹配成功
    const val VOICE_MATCH_SUCCESS = "random_speech_start"

    // 语音匹配失败
    const val VOICE_MATCH_FAIL = "random_speech_match_fail"

    // 私密小屋提示
    const val PRIVATE_HINT_MSG = "private_room_try_hint"

    // 幸运球礼物消息
    const val GIVE_LUCKY_GIFT = "give_lucky_gift"

    // 充值引导
    const val RECHARGE_METHOD_EVENT = "recharge_method_event"

    // 签到成功
    const val MEMBER_SIGN_IN = "member_sign_in"

    // 领取奖励
    const val MEMBER_GET_REWARD = "member_get_reward"

    // 视频通话邀请
    const val VIDEO_CALL_INVITE = "video_chat_invite"

    // 视频通话接听
    const val VIDEO_CALL_ANSWER = "video_chat_invitee_accept"

    // 视频通话拒绝
    const val VIDEO_CALL_REFUSE = "video_chat_invitee_refuse"

    // 视频通话忙线
    const val VIDEO_CALL_BUSY = "video_chat_invitee_busy"

    // 视频通话取消
    const val VIDEO_CALL_CANCEL = "video_chat_inviter_cancel"

    // 视频通话呼叫超时
    const val VIDEO_CALL_TIMEOUT = "video_chat_invite_timeout"

    // 视频通话开始
    const val VIDEO_CALL_START = "video_chat_start"

    // 视频通话余额不足
    const val VIDEO_CALL_WILL_OUT_OF_BALANCE = "video_chat_will_out_of_balance"

    // 通话账户信息同步事件
    const val VIDEO_CALL_ACCOUNT_INFO = "video_chat_account_info"

    // 视频通话结束
    const val VIDEO_CALL_FINISH = "video_chat_end"

    // pk提示
    const val TEAM_PK_EVENT = "audioroom_pk"

    // 用户等级变更
    const val USER_LEVEL_CHANGE = "user_level_change"

    // 喜欢你人数变化
    const val LIKE_YOU_NEW_CNT_UPDATE = "like_you_new_cnt_update"

    // 新人推荐事件
    const val RECOMMAND_NEWBIE_UPDATE = "broadcaster_recommend_newbie_user"

    // 极速赛车
    const val SPEED_RACING_WIN = "speed_racing_win"

    // 邀请加入亲友团
    const val INVITE_TO_JOIN_RELATIVE_GROUP = "invite_to_join_relative_group"

    // 接受亲友团
    const val ACCEPT_TO_JOIN_RELATIVE_GROUP = "accept_to_join_relative_group"

    // 被移除亲友团
    const val RELATIVE_GROUP_BROKEN = "relative_group_broken"

    // app前台响应
    const val USER_IN_APP_CHECK = "user_in_app_check"

    // 完成新人任务
    const val NEWBIE_TASK_FINISHED = "newbie_task_finished"

    // 邀请开会员
//    {"cmd": "invite_to_join_ucmember", "data": {"invite_id": 77, "inviter": {"userid": 4847, "public_id": "107233", "nickname": "\u534e\u8bed\u533a0001\u5973\u7684\u80e1\u561f\u561f\u561f\u561f\u51fa\u561f\u561f\u561f\u561f\u561f", "avatar_url": "https://s.test.wakooclub.com/aaceLj?x-oss-process=image/format,webp", "gender": 2, "age": 18, "avatar_frame": "", "is_high_quality": true, "level": 50, "exp_level_info": {"charm_level": 50, "wealth_level": 50}, "is_member": true}, "invitee": {"userid": 4911, "public_id": "107396", "nickname": "A\u534e\u8bed\u533a0005", "avatar_url": "https://s.test.wakooclub.com/aaceL2?x-oss-process=image/format,webp", "gender": 1, "age": 18, "avatar_frame": "", "is_high_quality": false, "level": 0, "exp_level_info": {"charm_level": 1, "wealth_level": 1}, "is_member": false}, "digest": "[\u9080\u8bf7\u5f00\u901a\u4f1a\u5458\u6d88\u606f]"}}
    const val INVITE_TO_JOIN_UCMEMBER = "invite_to_join_ucmember"

    // 邀请加入群聊
    const val INVITE_TO_JOIN_GROUP = "chatgroup_invite"

    // 邀请开会员
    const val PUSH_NOTIFICATION = "push_notification"

    // 弹窗
    const val COMMON_ERROR_POPUP = "common_error_popup"

    // 弹窗
    const val CHRISTMAS_2023_DECORATE_PROP_ACHIEVEMENT = "christmas_2023_decorate_prop_achievement"

    // 通用语音房系统消息
    const val COMMON_CHATROOM_PUBLIC_MESSAGES = "common_chatroom_public_messages"

    // 跟随进入语音房系统消息
    const val FOLLOW_CHATROOM_PUBLIC_MESSAGES = "follower_enter_room_messages"

    // 通用语音房系统消息
    const val AUDIOROOM_RECOMMENDATION = "audioroom_recommendation"

    // 虚拟女友匹配成功
    const val VIRTUAL_GIRLFRIEND_MATCH_SUCCESS = "virtual_girlfriend_match_success"

    // 虚拟女友匹配失败
    const val VIRTUAL_GIRLFRIEND_MATCH_FAILED = "virtual_girlfriend_match_failed"

    // 匿名闪聊匹配成功
    const val FLASH_CHAT_MATCH_SUCCESS = "flash_chat_match_success"

    // 闪聊匹配失败
    const val FLASH_CHAT_MATCH_FAILED = "flash_chat_match_failed"

    // 闪聊解锁
    const val FLASH_CHAT_PROFILE_UNLOCKED = "flash_chat_profile_unlocked"

    // 红包消息
    const val RED_PACKET_CREATED = "red_packet_created"

    // 抢红包消息
    const val RED_PACKET_START_GRAB = "start_grabbing_red_packet"

    // 上报adjust
    const val REPORT_ADJUST_EVENTS = "report_adjust_events"

    // 龙宝任务完成
    const val KEY_SPRING_PRAY_2024_DRAGON_GROW = "spring_pray_2024_dragon_grow"

    // 通用弹窗
    const val COMMON_POPUP_PUBLIC_MESSAGES = "common_popup_public_messages"

    // 视频引导 2.15.0
    const val GUIDE_VIDEO_CHAT = "guide_video_chat"

    // 私密小屋引导 2.15.0
    const val GUIDE_AUDIO_CHAT = "guide_audio_chat"

    const val GUIDE_CP = "guide_cp"

    // 语音通话邀请
    const val VOICE_CALL_INVITE = "private_room_call_invite"

    // 语音通话接听
    const val VOICE_CALL_ANSWER = "private_room_call_accept"

    // 语音通话拒绝
    const val VOICE_CALL_REFUSE = "private_room_call_refuse"

    // 语音通话忙线
    const val VOICE_CALL_BUSY = "private_room_call_busy"

    // 语音通话取消
    const val VOICE_CALL_CANCEL = "private_room_call_cancel"

    // 语音通话呼叫超时
    const val VOICE_CALL_TIMEOUT = "private_room_call_timeout"

    // 语音通话开始
    const val VOICE_CALL_START = "private_room_call_start"

    // 语音通话开始扣金币
    const val VOICE_CALL_WILL_USE_COIN = "private_room_call_will_use_coin"

    // 语音通话结束
    const val VOICE_CALL_FINISH = "private_room_call_end"

    // 互动游戏邀请
    const val GAME_MATCH_INVITE = "audioroom_play_game_invitation"

    // 互动游戏完成
    const val GAME_MATCH_DONE = "join_audioroom"

    // 会话列表配置
    const val CHAT_LIST_CONFIG_CHANGE = "chat_list_config_change"

    // 文本消息
    const val TXT_MSG = "txt_msg"

    // 风险提示
    const val PRIVATECHAT_SYSTEM_ALERT = "privatechat_system_alert"

    // 图文消息
    const val PRIVATECHAT_OFFICIAL_H5_LINK = "privatechat_official_h5_link"

    const val MIC_SPEECH_TEXT = "mic_speech_text"

    const val STOP_SEND_ASR_PUBLIC_MESSAGE = "stop_send_asr_public_message"

    const val FORCE_LOGOUT = "force_logout"

    /**
     * - 守护消息通知
     *```
     * {
     *     "from_user": {},
     *     "to_user": {},
     *     "change_type": 1,    // 1成为守护，2被守护，3失去守护身份
     *     "guard_threshold": 3000  // 失去守护时，要多少金币成为守护
     * }
     *```
     */
    const val GUARD_CHANGE_EVENT = "guard_change_event"

    const val GIVE_GIFT_COMBO_FINISHED = "japan_give_lucky_gift_combo"

    //region 金币大赢家相关

    const val COIN_PK_CREATE = "japan_coin_pk_create_game"
    const val COIN_PK_CLOSE = "japan_coin_pk_close_game"
    const val COIN_PK_JOIN = "japan_coin_pk_join"
    const val COIN_PK_LOSE = "japan_coin_pk_player_lose"
    const val COIN_PK_WIN = "japan_coin_pk_player_win"
    const val COIN_PK_STAGE_CHANGE = "japan_coin_pk_stage_change"
    const val COIN_PK_RAISE = "japan_coin_pk_raise"
    const val COIN_PK_OWNER_REWARD = "japan_coin_pk_audioroom_owner_bonus"

    //region 头等舱俱乐部
    const val FIRSTCLASS_WELCOME_MESSAGE = "welcome_first_class_user"
    const val FIRSTCLASS_ACTIVE_MESSAGE = "activate_first_class_user"
    //endregion

    // 会员背包礼物未使用提示弹窗
    const val GIFT_UNUSED_POPUP = "member_privileged_gift_remind_popup"

    // 语音房观众任务获得奖励弹窗
    const val AUDIOROOM_AUDIENCE_TASK_BONUS_POPUP = "audioroom_audience_task_bonus_popup"
    const val AUDIOROOM_AUDIENCE_TASK_STATUS_SYNC = "audioroom_audience_task_status_sync"

    // 新人专属女友接待，互动解锁消息
    // {
    //  "mask_pics": true,  // 默认值为true,
    //  "progress_value": 2,  // 默认值为0,
    // }
    const val INTERACT_UNLOCK_MESSAGE = "interact_unlock_message"

    // 春节活动点亮3场舞会动效消息
    const val CONFIRM_SPRING_FESTIVAL_2025_TASK_FINISHED =
        "confirm_spring_festival_2025_task_finished"

    // 2025春节活动中奖
    const val GET_GREETING_PRIZE = "GET_GREETING_PRIZE"

    // 部落自定义提示
    const val TRIBE_CUSTOM_CONTENT = "tribe_custom_content"

    // 主播收到的赠送免费礼物的事件
    const val FREE_GIFT_ANCHORS_GIVE = "free_gift_anchors_give"

    // 男用户收到的回赠礼物的事件
    const val FREE_GIFT_MALE_RETURN = "free_gift_male_return"

    // 语音房送礼引导事件定义
    const val AUDIOROOM_GIFT_GIVING_GUIDE = "audioroom_gift_giving_guide"

    // 服务端对客户端远程调用事件
    const val REMOTE_CALL = "remote_call"

    // 部落富文本
    const val TRIBE_COMMON_RICH_TEXT = "tribe_common_rich_text"
    const val GROUP_COMMON_RICH_TEXT = "group_common_rich_text"

    // 自动移出部落
    const val INACTIVE_MEMBER_KICKED_OUT = "inactive_member_kicked_out"

    // 私聊富文本
    const val COMMON_RICH_TEXT = "common_rich_text"

    // 一段文本下面带个跳转
    const val WEDDING_COMMON_EVENT = "wedding_common_event"

    //region 2.41.0

    // 成功点亮戒指
    const val WEDDING_RING_SEND_SUCCESS = "wedding_ring_send_success"

    // 婚礼开始
    const val WEDDING_START = "wedding_start"

    // 预约婚礼提醒
    const val BOOKING_WEDDING_ROOM = "booking_wedding_room"

    //endregion
    const val CMD_INVITE_CROSS_ROOM_PK = "invite_cross_room_pk_event"
    const val CMD_REFUSE_CROSS_ROOM_PK_INVITE = "refuse_cross_room_pk_invite_event"
    const val CMD_INVITE_CROSS_ROOM_PK_POPUP_CLOSE_EVENT = "invite_cross_room_pk_popup_close_event"
    const val CMD_CROSS_ROOM_PK_START = "cross_room_pk_start_event"
    const val CMD_CROSS_ROOM_PK_SYNC = "cross_room_pk_sync_event"
    const val CMD_CROSS_ROOM_PK_MIC_MUTED = "cross_room_pk_mic_muted_event"
    const val CMD_CROSS_ROOM_PK_ADD_TIME = "cross_room_pk_add_time_event"
    const val CMD_CROSS_ROOM_PK_RESULT = "cross_room_pk_result_event"

    // 分享瞬间
    const val SHARE_MOMENT = "share_moment"

    // 点赞瞬间
    const val LIKE_MOMENT = "like_moment"

    const val CHATGROUP_CREATED = "chatgroup_created"
    const val CHATGROUP_MEMBER_JOIN = "chatgroup_member_join"
    const val CHATGROUP_MEMBER_QUIT = "chatgroup_member_quit"
    const val CHATGROUP_MEMBER_KICKED_OUT = "chatgroup_member_kicked_out"
    const val CHATGROUP_MEMBER_APPLY_CNT_CHANGE = "chatgroup_member_apply_cnt_change"
    const val CHATGROUP_DISBANDED = "chatgroup_disbanded"
    const val CHATGROUP_NAME_CHANGE = "chatgroup_name_change"
    const val GROUP_ACTION_HINT = "group_action_hint"
    const val GROUP_NOTICE_ACTION_CARD = "group_notice_action_card"

    // {"cmd":"common_text_with_action_card","data":{"content":"恭喜您充值成功，获得钻石x96、梦幻摩天轮*1，礼物已放在您的背包中，可赠送给你想赠送的人","btn_txt":"免费赠送","app_link":"wakoo://page/private_chat?userid=4834&action=open_gift_panel&gift_tab=-1&gift_id=7","digest":"新人礼包"}}
    const val COMMON_TEXT_WITH_ACTION_CARD = "common_text_with_action_card"

    // 日区填写邀请码奖励弹窗
    const val JAPAN_COMMON_BONUS_POPUP = "japan_common_bonus_popup"

    const val PRIVATE_ROOM_GUIDE_POPUP = "private_room_guide_popup"

    // 新人推荐
    const val PRIVATECHAT_RECOMMEND_POPUP = "privatechat_recommend_popup"

    // 有人进入私密小屋
    const val PRIVATE_ROOM_PARTNER_ENTER = "private_room_partner_enter"

    // 私密小屋邀请弹窗事件
    const val PRIVATE_ROOM_INVITE_POPUP = "private_room_invite_popup"

    // 运营者任务 奖励
    const val COMMON_BONUS_POPUP = "common_bonus_popup"

    // 装扮佩戴变化通知
    const val DRESSUP_CHANGE = "dressup_change"
}
