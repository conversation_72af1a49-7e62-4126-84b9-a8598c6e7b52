package com.buque.wakoo.im_business.message.ui.custom

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.BegGiftBean
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.im_business.interf.IC2CAction
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.MessageTheme
import com.buque.wakoo.im_business.message.MessageThemeBubble
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
import com.buque.wakoo.ui.widget.image.NetworkImage

class BegGiftContent(
    val bean: BegGiftBean,
    val isSelf: Boolean,
) : MsgLayoutContent() {
    override val supportReadReceipt: Boolean = false

    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction,
    ) {
        baseBox {
            MessageThemeBubble(
                defaultMsgTheme =
                    MessageTheme(
                        painter = ColorPainter(Color.White),
                        paddingValues = PaddingValues(12.dp),
                        shape = RoundedCornerShape(8.dp),
                        contentColor = Color(0xff111111),
                        16.sp,
                    ),
            ) {
                Row(
                    modifier =
                        Modifier
                            .width(220.dp)
                            .noEffectClick {
                                (onAction as? IC2CAction)?.onShowCharityGiftPanel(bean, isSelf)
                            },
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Column {
                        Text(
                            bean.digest,
                            maxLines = 1,
                            fontSize = 16.sp,
                            lineHeight = 16.sp,
                            overflow = TextOverflow.Ellipsis,
                            color = Color(0xff1d2129),
                        )
                        Spacer(modifier = Modifier.height(12.dp))
                        Text(
                            "${bean.gift.name} x${bean.count}",
                            color = Color(0xFF999999),
                            fontWeight = FontWeight.Normal,
                            fontSize = 12.sp,
                        )
                    }
                    Spacer(Modifier.width(12.dp))
                    NetworkImage(bean.gift.icon, modifier = Modifier.size(48.dp))
                }
            }
        }
    }
}
