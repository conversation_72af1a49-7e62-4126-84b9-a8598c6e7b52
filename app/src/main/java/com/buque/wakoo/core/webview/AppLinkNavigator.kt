package com.buque.wakoo.core.webview

import android.net.Uri
import androidx.annotation.Keep
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.core.net.toUri
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.core.pay.GoogleBillingManager
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.ext.toastWhenError
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.navigation.AppNavController
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.HomeTabRoute
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.DialogController
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.ui.dialog.AnyPopDialogProperties
import com.buque.wakoo.ui.dialog.FirstRechargeDialog
import com.buque.wakoo.ui.screens.WebViewDialogContent
import com.buque.wakoo.ui.widget.gift.GiftPosition
import com.buque.wakoo.ui.widget.gift.OpenGiftPanelAction
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.eventBus.AppEvent
import com.buque.wakoo.utils.eventBus.EventBus
import com.buque.wakoo.utils.eventBus.easyPostDialog
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.net.URLDecoder
import kotlin.reflect.full.companionObjectInstance
import kotlin.reflect.full.memberFunctions

@Keep
object AppLinkNavigator {
    private val mapRouteObject = mutableMapOf<String, (Uri) -> AppNavKey>()

    init {
        val routeClasses = Route::class.nestedClasses
        try {
            routeClasses.forEach { rc ->
                val path = "/${camelToUnderscore(rc.simpleName.orEmpty())}"
                // data object
                if (rc.objectInstance != null) {
                    mapRouteObject[path] = {
                        rc.objectInstance as AppNavKey
                    }
                } else {
                    val comp = rc.companionObjectInstance
                    var func: ((Uri) -> AppNavKey)? = null
                    if (comp != null) {
                        val funFromUri = comp::class.memberFunctions.firstOrNull { it.name == "fromUri" }
                        if (funFromUri != null) {
                            func = {
                                try {
                                    funFromUri.call(comp, it) as AppNavKey
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                    Route.AboutApp
                                }
                            }
                        }
                    }
                    if (func == null) {
                        func = {
                            throw IllegalAccessError("${rc.simpleName} 类未找到 companion.fromUri Method")
                        }
                    }
                    mapRouteObject[path] = func
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun initialize() = Unit

    fun isSupport(link: String): Boolean = link.startsWith("wakoo://") || link.startsWith("http://") || link.startsWith("https://")

    fun go(
        link: String?,
        appNavController: AppNavController,
        dialogController: DialogController? = null,
    ) {
        if (link.isNullOrBlank()) {
            return
        }
        if (link.startsWith("http://") || link.startsWith("https://")) {
            appNavController.push(Route.Web(link))
            return
        }
        if (link.startsWith("wakoo://")) {
            try {
                val uri = link.toUri()
                val path = uri.path ?: return

                if (handleSpecialPath(appNavController, dialogController, uri)) {
                    return
                }
                mapRouteObject[path]?.invoke(uri)?.also {
                    appNavController.push(it)
                } ?: run {
                    LogUtils.e("not support:$link")
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        } else {
            LogUtils.e("not support:$link")
        }
    }

    private fun handleSpecialPath(
        appNavController: AppNavController,
        dialogController: DialogController?,
        uri: Uri,
    ): Boolean =
        when (uri.path.orEmpty()) {
            "/home" -> {
                appNavController.popUntil { it is Route.Home }
                uri.getQueryParameter("sub_route_page")?.also { tab ->
                    appCoroutineScope.launch {
                        delay(200)
                        when (tab) {
                            "13" -> HomeTabRoute.Task
                            "11" -> HomeTabRoute.Home
                            "14" -> HomeTabRoute.Message
                            "15" -> HomeTabRoute.Mine
                            "16" -> HomeTabRoute.Discover
                            else -> null
                        }?.also {
                            EventBus.send(it)
                        }
                    }
                }
                true
            }

            "/main" -> {
                appNavController.popUntil { it is Route.Home }
                // tab
                uri.getQueryParameter("tab")?.also { tab ->
                    appCoroutineScope.launch {
                        delay(200)
                        when (tab) {
                            "task" -> HomeTabRoute.Task
                            "home" -> HomeTabRoute.Home
                            "message" -> HomeTabRoute.Message
                            "mine" -> HomeTabRoute.Mine
                            "discover" -> HomeTabRoute.Discover
                            else -> null
                        }?.also {
                            EventBus.send(it)
                        }
                    }
                }
                true
            }

            "/dressup_mall" -> {
                appNavController.push(Route.DressUpSample)
                true
            }

            "/c2c", "/private_chat" -> {
                val uid = uri.getQueryParameter("userId") ?: uri.getQueryParameter("userid").orEmpty()
                appNavController.push(Route.Chat(BasicUser.fromUid(uid)))

                val action = uri.getQueryParameter("action") ?: ""
                val tab = uri.getQueryParameter("gift_tab")?.toIntOrNull() ?: -100
                val id = uri.getQueryParameter("gift_id")?.toIntOrNull() ?: -100
                appCoroutineScope.launch {
                    delay(200)
                    when (action) {
                        "open_gift_panel" -> {
                            EventBus.trySend(OpenGiftPanelAction(uid, GiftPosition(id, tabId = tab)))
                        }

                        else -> Unit
                    }
                }
                true
            }

            "/web_frame" -> {
                try {
                    val json = uri.getQueryParameter("info")?.let { URLDecoder.decode(it) } ?: "{}"
                    LogUtils.d("web frame:$json")
                    val webFrameInfo = AppJson.decodeFromString<WebFrameInfo>(json)
                    dialogController?.easyPost(
                        dialogProperties =
                            AnyPopDialogProperties(
                                useSystemDialog = false,
                                dismissOnClickOutside = webFrameInfo.cancelable,
                                dismissOnBackPress = webFrameInfo.cancelable,
                            ),
                    ) {
                        val align =
                            when (webFrameInfo.gravity) {
                                "top" -> Alignment.TopCenter
                                "bottom" -> Alignment.BottomCenter
                                else -> Alignment.Center
                            }
                        Box(
                            modifier =
                                Modifier
                                    .fillMaxSize()
                                    .noEffectClick(enabled = webFrameInfo.cancelable, onClick = {
                                        dismiss()
                                    })
                                    .background(Color.Black.copy(alpha = 0.3f)),
                            contentAlignment = align,
                        ) {
                            WebViewDialogContent(webFrameInfo, onOpenPage = {
                                appNavController.push(it)
                            }) {
                                dismiss()
                            }
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                true
            }

            "/webview" -> {
                val url = uri.getQueryParameter("url")?.let { URLDecoder.decode(it) }.orEmpty()
                if (url.isNotEmpty()) {
                    appNavController.push(Route.Web(url))
                } else {
                    showToast("WARNING:URL IS EMPTY")
                }
                true
            }

            "/common" -> {
                val action = uri.getQueryParameter("action")

                when (action) {
                    "joinAudioRoom" -> {
                        val roomId = uri.getQueryParameter("roomId")
                        if (!roomId.isNullOrBlank()) {
                            LiveRoomManager.joinRoom(roomId)
                        }
                    }
                }

                true
            }
            // 7.30, 莫名其妙的又来了一个加入语音房?
            "/chatroom" -> {
                val roomId = uri.getQueryParameter("roomId")
                if (!roomId.isNullOrBlank()) {
                    LiveRoomManager.joinRoom(roomId)
                }
                true
            }

            "/user_home" -> {
                val userid = uri.getQueryParameter("userid")
                if (!userid.isNullOrBlank()) {
                    appNavController.push(Route.UserProfile(BasicUser(id = userid)))
                }
                true
            }

            "/tribe_square" -> {
                appNavController.push(Route.ChatGroupSquare)
                true
            }

            "/beginner_pack" -> {
                val position = (uri.getQueryParameter("position") ?: "1").toIntOrNull() ?: 1
                appCoroutineScope.launch {
                    GoogleBillingManager
                        .fetchFirstRecharge()
                        .onSuccess { info ->
                            EventBus.easyPostDialog(
                                dialogProperties =
                                    AnyPopDialogProperties(
                                        useSystemDialog = false,
                                        useCustomAnimation = true,
                                    ),
                            ) {
                                FirstRechargeDialog(info, position)
                            }
                        }.toastWhenError()
                }
                true
            }

            "/profile_edit" -> {
                appNavController.push(Route.EditUserInfo)
                true
            }

            "/group_chat" -> {
//                /group_chat?group_id=590&action=show_gift_panel
                val groupId = uri.getQueryParameter("group_id") ?: return false
                val route = Route.ChatGroup(groupId, false)
                val pop = appNavController.popUntil { it == route }
                if (!pop) {
                    appNavController.push(route)
                }
                appCoroutineScope.launch {
                    EventBus.send(AppEvent.Action("show_group_home"))
                    delay(100)
                    EventBus.send(AppEvent.Action("show_group_chat"))
                    uri.getQueryParameter("action")?.also { action ->
                        delay(100)
                        EventBus.send(AppEvent.Action(action))
                    }
                }
                true
            }

            "/first_pendant" -> {
                appCoroutineScope.launch {
                    GoogleBillingManager
                        .fetchFirstRecharge()
                        .onSuccess { info ->
                            EventBus.easyPostDialog(
                                dialogProperties =
                                    AnyPopDialogProperties(
                                        useSystemDialog = false,
                                        useCustomAnimation = true,
                                    ),
                            ) {
                                FirstRechargeDialog(info)
                            }
                        }.toastWhenError()
                }
                true
            }

            else -> {
                false
            }
        }

    private fun camelToUnderscore(input: String): String {
        // 正则匹配大写字母，并在前面插入下划线，最后统一转小写
        return input.replace(Regex("([A-Z])"), "_$1").trimStart('_').lowercase()
    }

    suspend fun goByEvent(link: String) {
        if (link.isEmpty()) return
        EventBus.send(AppEvent.DeepLink(link))
    }
}
