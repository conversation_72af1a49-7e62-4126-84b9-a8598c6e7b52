package com.buque.wakoo.core.pay

import android.app.Activity
import android.content.Intent
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.core.net.toUri
import com.android.billingclient.api.BillingClient
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.consts.Pay
import com.buque.wakoo.ext.getStringOrNull
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.ext.toast
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.utils.AppUtils
import com.buque.wakoo.utils.LogUtils
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

object AppPayCoreKit : IPurchasesUpdatedListener {
    /**
     * 打开了bi shang c2c
     */
    const val ACTION_OPEN_AGENT_CHAT = "action_open_agent_chat"
    const val ACTION_BUY_COMPLETE = "action_buy_complete"
    private val _actionFlow = MutableSharedFlow<String>(extraBufferCapacity = 5)
    val actionFlow = _actionFlow.asSharedFlow()

    private val repository = GlobalRepository.walletRepo

    private val _loading: MutableState<Boolean> = mutableStateOf(false)
    val loading: State<Boolean> = _loading

    private var isLoading: Boolean
        get() = _loading.value
        set(value) {
            _loading.value = value
        }

    private fun checkProduct(request: PayRequest): Boolean =
        when (request.type) {
            Pay.CALL_TYPE_GOOGLE_SDK -> {
                GoogleBillingManager.checkProduct(request.productId)
            }

            Pay.CALL_TYPE_WXSDK -> {
                false
            }

            Pay.CALL_TYPE_LINK_WEB, Pay.CALL_TYPE_LINK_WEB1, Pay.CALL_TYPE_LINK_WEBVIEW -> {
                request.payLink.isNotBlank()
            }

            Pay.CALL_TYPE_ORDER_LINK -> {
                true
            }

            else -> false
        }

    private suspend fun pay(
        activity: Activity,
        request: PayRequest,
        listener: IPurchasesUpdatedListener,
    ) {
        when (request.type) {
            Pay.CALL_TYPE_GOOGLE_SDK -> {
                val orderNo = request.orderNo
                val productId = request.productId
                val code =
                    try {
                        GoogleBillingManager.buy(activity, request.productId, request.orderNo)
                    } catch (e: Exception) {
                        e.printStackTrace()
                        BillingClient.BillingResponseCode.ERROR
                    }
                when (code) {
                    BillingClient.BillingResponseCode.OK -> {
                        repository.recordBillingEvent(orderNo)
                        GoogleBillingManager.bindOrder(orderNo, productId, listener)
                    }

                    else -> {
                        showToast("您的设备不支持此充值方式请联系充值客服".localized)
                        appCoroutineScope.launch {
                            repository.reportRechargeError()
                        }
                    }
                }
            }

            Pay.CALL_TYPE_WXSDK -> {
//                WechatPay.pay(request.extra!!)
                showToast("Not Support WXPay...")
            }

            Pay.CALL_TYPE_LINK_WEB, Pay.CALL_TYPE_LINK_WEB1, Pay.CALL_TYPE_ORDER_LINK -> {
                runCatching {
                    val intent = Intent(Intent.ACTION_VIEW, request.payLink.toUri())
                    activity.startActivity(intent)
                }
            }

            else -> Unit
        }
    }

    suspend fun buy(
        activity: Activity,
        request: PayRequest,
    ) {
        val productId = request.productId
        LogUtils.d("buy:$request")
        if (!checkProduct(request)) {
            showToast("您的设备不支持此充值方式请联系充值客服".localized)
            appCoroutineScope.launch {
                repository.reportRechargeError()
            }
            return
        }
        val orderType = request.orderType
        val result =
            when (request.type) {
                Pay.CALL_TYPE_GOOGLE_SDK, Pay.CALL_TYPE_WXSDK -> {
                    isLoading = true
                    repository.createOrder(productId, orderType).fold({
                        if (it.first.isNotBlank() && (request.type == 1 || it.second != null)) {
                            request.copy(orderNo = it.first, extra = it.second)
                        } else {
                            showToast("创建订单失败，productId: %s, orderType: %d".localizedFormat(productId, orderType))
                            null
                        }
                    }) {
                        it.toast()
                        null
                    }
                }

                6 -> {
                    if (request.payLink.isNotEmpty()) {
                        AppUtils.openExternalBrowser(activity, request.payLink)
                    }
                    return
                }

                7 -> {
                    isLoading = true
                    repository.createOrder(productId, request.orderType).fold({
                        val url = it.second?.getStringOrNull("redirect_url")
                        if (!url.isNullOrEmpty()) {
                            request.copy(payLink = url)
                        } else {
                            showToast("创建订单失败，productId: $productId, orderType: ${request.orderType}")
                            null
                        }
                    }) {
                        it.toast()
                        null
                    }
                }

                8 -> {
                    isLoading = true
                    var success = false
//                    executeApiCallExpectingData {
//                        chatApi.getChargeInfo()
//                    }.run {
//                        toastError()
//                        getOrNull()
//                    }?.also {
//                        if (AppUserPartition.isUCOO) {
//                            activity.startActivity(ChatActivity.createIntent(activity, it))
//                        } else {
//                            AppLinkManager.controller?.navigate(CupidRouters.C2CChat, mapOf("user" to it))
//                        }
//                        success = true
//                    }
                    isLoading = false
                    if (success) {
                        _actionFlow.emit(ACTION_OPEN_AGENT_CHAT)
                    }
                    return
                }

                else -> {
                    request
                }
            }
        if (result != null) {
            pay(activity, result, this)
        }
        isLoading = false
    }

    override fun onPurchasesUpdated(completed: Boolean) {
        if (completed) {
            isLoading = false
            GoogleBillingManager.clearListener()
            _actionFlow.tryEmit(ACTION_BUY_COMPLETE)
        } else {
            isLoading = true
        }
    }

    override fun onPurchasesMessage(message: String) {
        showToast(message)
    }
}
