package com.buque.wakoo.utils.eventBus

import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.dialog.DialogDestination
import com.buque.wakoo.navigation.dialog.OnDialogContent
import com.buque.wakoo.ui.dialog.AnyPopDialogProperties

sealed interface AppEvent {
    data class Refresh(
        val flag: Any,
    ) : AppEvent

    data class DeepLink(
        val uri: String,
    ) : AppEvent

    sealed interface Dialog : AppEvent

    sealed interface EasyDialog : Dialog {
        val dialogProperties: AnyPopDialogProperties
        val content: OnDialogContent
    }

    data class CustomDialog(
        override val dialogProperties: AnyPopDialogProperties = AnyPopDialogProperties(),
        override val content: OnDialogContent,
    ) : EasyDialog

    data class PanelDialog(
        override val dialogProperties: AnyPopDialogProperties =
            AnyPopDialogProperties(
                useSystemDialog = false,
                useCustomAnimation = true,
            ),
        override val content: OnDialogContent,
    ) : EasyDialog

    data class RestorableDialog(
        val destination: DialogDestination,
    ) : Dialog

    data class Route(
        val navKey: AppNavKey,
    )

    data class Action(
        val actionName: String,
    )
}

fun EventBus.tryToLink(uri: String) {
    trySend(AppEvent.DeepLink(uri))
}

suspend fun EventBus.toLink(uri: String) {
    send(AppEvent.DeepLink(uri))
}

fun String.tryToLink() {
    EventBus.trySend(AppEvent.DeepLink(this))
}

suspend fun String.toLink() {
    EventBus.send(AppEvent.DeepLink(this))
}

fun EventBus.tryEasyPostDialog(
    useSystemDialog: Boolean = true,
    dialogProperties: AnyPopDialogProperties = AnyPopDialogProperties(useSystemDialog = useSystemDialog),
    content: OnDialogContent,
) {
    trySend(AppEvent.CustomDialog(dialogProperties, content))
}

suspend fun EventBus.easyPostDialog(
    useSystemDialog: Boolean = true,
    dialogProperties: AnyPopDialogProperties = AnyPopDialogProperties(useSystemDialog = useSystemDialog),
    content: OnDialogContent,
) {
    send(AppEvent.CustomDialog(dialogProperties, content))
}
