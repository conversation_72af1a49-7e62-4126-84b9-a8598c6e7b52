package com.buque.wakoo.utils

import android.graphics.Typeface
import android.text.Html
import android.text.Spanned
import android.text.style.AbsoluteSizeSpan
import android.text.style.BackgroundColorSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import android.text.style.UnderlineSpan
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 将包含基本 H5 标签和样式的字符串转换为 Compose 的 AnnotatedString。
 * 此方法适配常见的文本样式，如颜色、大小、粗细、斜体、下划线、删除线和背景色。
 * 它利用 Android 的 Html.fromHtml() 进行初步解析。
 *
 * @param htmlString 包含 H5 文本的字符串。
 * @return 转换后的 AnnotatedString。
 */
fun convertHtmlToAnnotatedString(htmlString: String): AnnotatedString {
    // 使用 Html.fromHtml 解析 H5 字符串为 Spanned。
    // FROM_HTML_MODE_LEGACY 模式通常提供较好的兼容性，能处理大部分基本标签。
    val spanned: Spanned = Html.fromHtml(htmlString, Html.FROM_HTML_MODE_LEGACY)

    return buildAnnotatedString {
        // 1. 首先，将 Spanned 对象的文本内容完整地添加到 AnnotatedString.Builder 中。
        // Html.fromHtml 会自动处理 <p> 和 <br> 标签为 '\n' 或 '\n\n'。
        append(spanned.toString())

        // 2. 然后，遍历 Spanned 对象中所有的 Span，并将其转换为 Compose 的 SpanStyle
        //    然后应用到 AnnotatedString 对应的文本范围。
        spanned.getSpans(0, spanned.length, Any::class.java).forEach { span ->
            val start = spanned.getSpanStart(span)
            val end = spanned.getSpanEnd(span)

            // 确保 span 的起始和结束索引在合法范围内
            if (start >= 0 && end <= length && start < end) {
                when (span) {
                    is ForegroundColorSpan -> {
                        addStyle(SpanStyle(color = Color(span.foregroundColor)), start, end)
                    }

                    is BackgroundColorSpan -> {
                        addStyle(SpanStyle(background = Color(span.backgroundColor)), start, end)
                    }

                    is StyleSpan -> {
                        val fontWeight =
                            when (span.style) {
                                Typeface.BOLD -> FontWeight.Bold
                                Typeface.BOLD_ITALIC -> FontWeight.Bold
                                else -> null // 对于非粗体，不指定权重
                            }
                        val fontStyle =
                            if (span.style == Typeface.ITALIC || span.style == Typeface.BOLD_ITALIC) {
                                FontStyle.Italic
                            } else {
                                null // 对于非斜体，不指定样式
                            }
                        // 仅当样式实际存在时才添加
                        if (fontWeight != null || fontStyle != null) {
                            addStyle(SpanStyle(fontWeight = fontWeight, fontStyle = fontStyle), start, end)
                        }
                    }

                    is UnderlineSpan -> {
                        addStyle(SpanStyle(textDecoration = TextDecoration.Underline), start, end)
                    }

                    is StrikethroughSpan -> {
                        addStyle(SpanStyle(textDecoration = TextDecoration.LineThrough), start, end)
                    }

                    is AbsoluteSizeSpan -> {
                        // Html.fromHtml 解析的 AbsoluteSizeSpan.size 是像素 (px)
                        // Compose 推荐使用 sp。这里需要进行转换。
                        // 如果 span.dip 为 true，说明 fromHtml 已经将其转换为密度无关像素（DIP），可以直接用 sp
                        // 否则，它可能是像素值，需要根据屏幕密度转换为 sp
                        val fontSizePx = span.size.toFloat()
                        val fontSizeSp =
                            if (span.dip) {
                                fontSizePx.sp
                            } else {
                                // 这里需要注意：在没有 Context (例如 Composable 函数内部) 的情况下，
                                // 准确的像素到 SP 转换需要 LocalDensity.current.density。
                                // 在这个独立方法中，我们只能做近似处理，例如假设一个标准密度 (160dpi, 1x)
                                // 实际项目中，如果这个方法在 Composable 内部调用，可以传入 LocalDensity.current
                                // 或者让调用方确保尺寸的合理性。这里为了方法独立性，给一个粗略的转换。
                                // (fontSizePx / 1.5f).sp // 假设屏幕密度为 1.5x (中等密度)
                                // 更好的做法是，如果尺寸非常重要，考虑让调用者提供 density 或使用基于 dp 的单位。
                                fontSizePx.sp // 简单处理，直接当作 sp，可能不准确
                            }
                        addStyle(SpanStyle(fontSize = fontSizeSp), start, end)
                    }
                    //  如果需要支持更多 Span 类型（例如 URLSpan 用于链接，ImageSpan 用于图片），
                    // 你需要在这里添加更多的 `when` 分支，并将其转换为对应的 Compose 样式或 Composable。
                    // 对于 URLSpan，你可以使用 AnnotatedString 的 annotation 来存储 URL，
                    // 然后在 Text 的 onClick 或 TextRenderer 中处理点击事件。
                }
            }
        }
    }
}

// 辅助函数：将十六进制颜色字符串转换为 Compose Color
// 支持 "#RRGGBB", "#AARRGGBB", "RRGGBB" 等格式
fun String.toComposeColor(): Color =
    try {
        if (this.startsWith("#")) {
            Color(android.graphics.Color.parseColor(this))
        } else {
            // 假设是 "RRGGBB" 或 "AARRGGBB" 格式，尝试补齐 #
            Color(android.graphics.Color.parseColor("#" + this))
        }
    } catch (e: IllegalArgumentException) {
        // 如果颜色字符串格式不正确，返回一个透明或未指定颜色，避免崩溃
        Color.Unspecified // Unspecified 是 Compose 中表示颜色未定义的特殊值
    }

@Composable
fun HtmlTextDisplay(htmlContent: String) {
    val annotatedString =
        remember(htmlContent) {
            convertHtmlToAnnotatedString(htmlContent)
        }
    Text(text = annotatedString)
}

@Preview(showBackground = true)
@Composable
fun PreviewHtmlTextDisplay() {
    Column(modifier = Modifier.padding(16.dp)) {
        // 示例 1: 包含 <font> 标签和颜色
        HtmlTextDisplay(htmlContent = "<font color='#15ABFF'>&#8201;helloYa&#8201;</font>")
        Text(text = "---", modifier = Modifier.padding(vertical = 8.dp))

        // 示例 2: 包含 <span> 标签和 style 属性 (颜色、粗细)
        HtmlTextDisplay(
            htmlContent = "<span style=\"color:#FF0000; font-weight:bold;\">Hello Bold Red</span> from <span style=\"font-size: 24px; color: green;\">Compose</span>!",
        )
        Text(text = "---", modifier = Modifier.padding(vertical = 8.dp))

        // 示例 3: 包含 <p>, <i>, <u>, <s> 标签
        HtmlTextDisplay(
            htmlContent = "<p>This is a paragraph with <i>italic</i>, <u>underline</u> and <s>strikethrough</s> text.</p><p>New paragraph.</p>",
        )
        Text(text = "---", modifier = Modifier.padding(vertical = 8.dp))

        // 示例 4: 混合样式和 br
        HtmlTextDisplay(
            htmlContent = "Line 1<br><span style=\"color:#0000FF;\">Line 2 in blue</span><br>Line 3 with <font size=\"20px\">large</font> text.",
        )
    }
}


inline fun AnnotatedString.Builder.color(color: Color, builder: (AnnotatedString.Builder).() -> Unit) {
    withStyle(SpanStyle(color = color)) {
        builder()
    }
}

fun buildAString(fullString: Pair<String, Color>, specialString: Pair<String, Color>, split: String = "|") =
    buildAString(fullString, mapOf(specialString), split)

fun buildAString(fullString: Pair<String, Color>, highLightTextConfigs: Map<String, Color>, split: String = "|"): AnnotatedString {
    val fullText = fullString.first
    var temText = fullText
    highLightTextConfigs.forEach {
        temText = temText.replace(it.key, "$split${it.key}$split")
    }
    val arr = temText.split(split)
    return buildAnnotatedString {
        arr.forEach { text ->
            withStyle(
                SpanStyle(
                    color = highLightTextConfigs[text] ?: fullString.second
                )
            ) {
                append(text)
            }
        }
    }
}