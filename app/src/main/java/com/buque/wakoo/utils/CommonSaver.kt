package com.buque.wakoo.utils

import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.SaverScope
import com.buque.wakoo.app.AppJson
import kotlinx.serialization.KSerializer
import kotlinx.serialization.SerializationException
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.encodeToJsonElement

/**
 * 通用的数据类持久化存储Saver
 * 用于将可序列化的data class保存到SavedState中
 *
 * @param T 需要持久化的数据类型，必须是可序列化的
 * @param serializer 对应类型的序列化器
 */
class CommonSaver<T : Any>(
    private val serializer: KSerializer<T>,
) : Saver<T?, String> {
    override fun SaverScope.save(value: T?): String? =
        value?.let {
            try {
                AppJson.encodeToString(serializer, it)
            } catch (e: SerializationException) {
                null
            }
        }

    override fun restore(value: String): T? =
        try {
            AppJson.decodeFromString(serializer, value)
        } catch (e: SerializationException) {
            null
        }

    companion object {
        /**
         * 创建一个通用的data class Saver
         *
         * 使用示例：
         * ```
         * @Serializable
         * data class UserInfo(val id: String, val name: String)
         *
         * val userInfoSaver = CommonSaver.create(UserInfo.serializer())
         *
         * @Composable
         * fun MyScreen() {
         *     var userInfo by rememberSaveable(stateSaver = userInfoSaver) {
         *         mutableStateOf(UserInfo("123", "张三"))
         *     }
         * }
         * ```
         */
        fun <T : Any> create(serializer: KSerializer<T>): CommonSaver<T> = CommonSaver(serializer)

        /**
         * 为可空类型创建Saver
         */
        fun <T : Any> createNullable(serializer: KSerializer<T>): Saver<T?, String> =
            Saver(
                save = { value ->
                    value?.let {
                        try {
                            AppJson.encodeToString(serializer, it)
                        } catch (e: SerializationException) {
                            null
                        }
                    }
                },
                restore = { value ->
                    value.let {
                        try {
                            AppJson.decodeFromString(serializer, it)
                        } catch (e: SerializationException) {
                            null
                        }
                    }
                },
            )
    }
}
