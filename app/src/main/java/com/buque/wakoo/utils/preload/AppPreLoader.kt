package com.buque.wakoo.utils.preload

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.api.bean.VoicePublishConfig
import com.buque.wakoo.network.api.bean.VoiceTag
import com.buque.wakoo.network.api.service.VoiceApiService
import com.buque.wakoo.network.api.service.VoiceRoomApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.executeStatefulApiCall
import com.buque.wakoo.ui.widget.state.executeStatefulCall
import com.buque.wakoo.ui.widget.state.shouldRequestData
import com.buque.wakoo.utils.upload.CosTransferProvider
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.supervisorScope

object AppPreLoader {
    fun loadAfterLoggedIn() {
        // 直播房预加载
        LiveRoomPreload.preload()
        // 声音发布预加载
        PublishVoicePreload.preload()
        // 初始化上传默认配置
        CosTransferProvider.initialize()
        // 拉取全局设置
        AppConfigManager.landState.value = false
        AppConfigManager.fetchAppConfig()
    }
}

object PublishVoicePreload {
    private val configStateFlow = MutableStateFlow<CState<VoicePublishConfig>>(CState.Idle)

    private var tempConfig: VoicePublishConfig? = null

    private var tempTags: List<VoiceTag>? = null

    fun preload() {
        refreshPublishConfig(false)
    }

    fun getPublishConfigFlow(needRefresh: Boolean): StateFlow<CState<VoicePublishConfig>> {
        val shouldRequestData = configStateFlow.value.shouldRequestData
        if (needRefresh || shouldRequestData) {
            refreshPublishConfig(!shouldRequestData)
        }
        return configStateFlow
    }

    fun refreshPublishConfig(isRefresh: Boolean) {
        appCoroutineScope
            .launch {
                executeStatefulCall(
                    stateFlow = configStateFlow,
                    isRefresh = isRefresh,
                    call = {
                        supervisorScope {
                            if (isRefresh || tempConfig == null) {
                                launch {
                                    executeApiCallExpectingData {
                                        VoiceApiService.instance.getVoiceConf()
                                    }.onSuccess {
                                        tempConfig = it
                                    }
                                }
                            }
                            if (isRefresh || tempTags == null) {
                                launch {
                                    executeApiCallExpectingData {
                                        VoiceApiService.instance.getOfficialTags()
                                    }.onSuccess {
                                        tempTags = it.list
                                    }
                                }
                            }
                        }
                        if (tempConfig != null && tempTags != null) {
                            tempConfig!!.copy(tags = tempTags!!)
                        } else {
                            throw IllegalStateException("配置获取失败，请重试！".localized)
                        }
                    },
                )
            }
    }
}

object LiveRoomPreload {
    private val tagsStateFlow = MutableStateFlow<CState<List<VoiceTag>>>(CState.Idle)

    fun preload() {
        refreshTags(false)
    }

    @Composable
    fun rememberTagsState(needRefresh: Boolean): State<CState<List<VoiceTag>>> {
        LaunchedEffect(Unit) {
            val shouldRequestData = tagsStateFlow.value.shouldRequestData
            if (needRefresh || shouldRequestData) {
                refreshTags(!shouldRequestData)
            }
        }
        return tagsStateFlow.collectAsStateWithLifecycle()
    }

    fun refreshTags(isRefresh: Boolean) {
        appCoroutineScope
            .launch {
                executeStatefulApiCall(
                    stateFlow = tagsStateFlow,
                    isRefresh = isRefresh,
                    apiCall = {
                        VoiceRoomApiService.instance.getVoiceRoomTagList()
                    },
                    transform = {
                        it.tags
                    },
                )
            }
    }
}
