package com.buque.wakoo

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation3.runtime.NavEntry
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.MyLiveRoomInfo
import com.buque.wakoo.bean.chatgroup.ChatGroupBean
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.im.utils.WatchMessageEventEffect
import com.buque.wakoo.im_business.AppEventMessageHandler
import com.buque.wakoo.im_business.viewmodel.C2CChatViewModel
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.AppNavDisplay
import com.buque.wakoo.navigation.GalleryScreenKey
import com.buque.wakoo.navigation.HomeBottomNavKey
import com.buque.wakoo.navigation.HomeTabRoute
import com.buque.wakoo.navigation.RootNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.appEntry
import com.buque.wakoo.navigation.appEntryProvider
import com.buque.wakoo.navigation.getVerticalTransitionMetadata
import com.buque.wakoo.ui.screens.WebScreen
import com.buque.wakoo.ui.screens.WebViewModel
import com.buque.wakoo.ui.screens.chatgroup.ChatGroupHostScreen
import com.buque.wakoo.ui.screens.chatgroup.screen.GroupSquareScreen
import com.buque.wakoo.ui.screens.commons.LocationSelectorScreen
import com.buque.wakoo.ui.screens.crony.CronySettingsScreen
import com.buque.wakoo.ui.screens.debug.DebugScreen
import com.buque.wakoo.ui.screens.dressup.DressupCenterScreen
import com.buque.wakoo.ui.screens.dressup.DressupShopListScreen
import com.buque.wakoo.ui.screens.dressup.DressupShopTabScreen
import com.buque.wakoo.ui.screens.dressup.SilverShopListScreen
import com.buque.wakoo.ui.screens.home.HomeHostScreen
import com.buque.wakoo.ui.screens.japan.boost.BoostPage
import com.buque.wakoo.ui.screens.japan.boost.PointsRecordScreen
import com.buque.wakoo.ui.screens.liveroom.screen.InviteToPrivateRoomScreen
import com.buque.wakoo.ui.screens.liveroom.screen.LiveRoomHostScreen
import com.buque.wakoo.ui.screens.messages.NtfFullScreenAlertState
import com.buque.wakoo.ui.screens.messages.chat.C2CChatScreen
import com.buque.wakoo.ui.screens.moments.MomentPublishScreen
import com.buque.wakoo.ui.screens.moments.UserMomentListScreen
import com.buque.wakoo.ui.screens.profile.EditTextUserInfoScreen
import com.buque.wakoo.ui.screens.profile.EditUserInfoScreen
import com.buque.wakoo.ui.screens.profile.ProfileVoiceEditScreen
import com.buque.wakoo.ui.screens.profile.SelectUserFromChatAndFriends
import com.buque.wakoo.ui.screens.profile.SelectUserScreen
import com.buque.wakoo.ui.screens.profile.UserAlbumScreen
import com.buque.wakoo.ui.screens.profile.UserProfileScreen
import com.buque.wakoo.ui.screens.profile.UserRelationsScreen
import com.buque.wakoo.ui.screens.profile.UserSearchScreen
import com.buque.wakoo.ui.screens.recharge.RechargeRecordScreenUI
import com.buque.wakoo.ui.screens.recharge.RechargeScreen
import com.buque.wakoo.ui.screens.settings.AboutAppScreen
import com.buque.wakoo.ui.screens.settings.BlackListScreen
import com.buque.wakoo.ui.screens.settings.FeedbackScreen
import com.buque.wakoo.ui.screens.settings.ReportScreen
import com.buque.wakoo.ui.screens.settings.SettingsScreen
import com.buque.wakoo.ui.screens.vip.MemberScreen
import com.buque.wakoo.ui.screens.vip.VisitorHistoryPage
import com.buque.wakoo.ui.screens.voice.VoicePublishHostScreen
import com.buque.wakoo.ui.widget.media.previewer.GalleryScreen
import com.buque.wakoo.ui.widget.media.selector.MediaSelectorScreen
import com.buque.wakoo.utils.LogUtils
import com.buque.webview.AppBridgeWebView
import com.kevinnzou.web.rememberSaveableWebViewState

@Composable
fun LoggedInHostScreen(controller: RootNavController) {
    val globalEventMessageHandler =
        remember(controller) {
            AppEventMessageHandler(controller)
        }
    WatchMessageEventEffect(globalEventMessageHandler)
    val context = LocalContext.current
    var web by remember {
        mutableStateOf(AppBridgeWebView(context).apply {
            distinctLoadUrl = true
        })
    }
    val last = controller.loggedInBackStack.lastOrNull()
    LaunchedEffect(last) {
        LogUtils.d("last app nav=====>$last")
        controller.loggedInBackStack.filter { it is Route.Web }.also {
            if (it.isEmpty() && !web.url.isNullOrEmpty()) {
                web = AppBridgeWebView(context).apply {
                    distinctLoadUrl = true
                }
            }
        }
    }
    val webViewState = rememberSaveableWebViewState()
    val webViewModel = viewModel<WebViewModel>(initializer = {
        WebViewModel(store = { web })
    })
    LocalSelfUserProvider.Provider {
        AppNavDisplay(
            backStack = controller.loggedInBackStack,
            modifier = Modifier.fillMaxSize(),
            entryProvider =
                appEntryProvider(fallback = { unknownScreen ->
                    NavEntry(Route.Fallback("Unknown screen $unknownScreen")) {
                        LaunchedEffect(Unit) {
                            if (EnvironmentManager.isProdRelease) {
                                controller.popIf(unknownScreen)
                            } else {
                                throw IllegalStateException("Unknown screen $unknownScreen")
                            }
                        }
                    }
                }) {
                    appEntry<Route.Web> { route ->
                        Box(modifier = Modifier.fillMaxSize()) {
                            WebScreen(
                                route, onOpenPage = {
                                    controller.push(it)
                                },
                                webViewState = webViewState,
                                vm = webViewModel
                            ) {
                                controller.pop()
                            }
                        }
                    }
                    appEntry<Route.Home> {
                        val user = LocalSelfUserProvider.current
                        var tab: HomeBottomNavKey by remember { mutableStateOf(HomeTabRoute.Home) }
                        HomeHostScreen(
                            onTabChanged = { tab = it },
                            toEditUserInfo = {
                                controller.push(Route.EditUserInfo)
                            },
                            toSettings = {
                                controller.push(Route.Settings)
                            },
                            toUserRelations = {
                                controller.push(Route.UserRelations(it, user.id))
                            },
                            toPublishVoice = {
                                controller.push(Route.VoicePublish)
                            },
                            onRechargeClick = {
                                controller.push(Route.Recharge)
                            },
                            onMemberClick = {
                                controller.push(Route.Member)
                            },
                            toLiveRoom = {
                                LiveRoomManager.joinRoom(it)
                            },
                            toDressUpCenter = {
                                controller.push(Route.DressUpMine())
                            },
                            toDressUpShop = {
                                controller.push(Route.DressUpSample)
                            },
                            toSearchScreen = {
//                                viewModel.mockCPPublicFloating()
                                controller.push(Route.UserSearch)
                            },
                            toC2CChat = {
                                controller.push(Route.Chat(it.toBasic()))
                            },
                            toChatGroup = {
                                if (it == null) {
                                    controller.push(Route.ChatGroupSquare)
                                } else {
                                    controller.push(Route.ChatGroup(it, false))
                                }
                            },
                            onNavigateTo = {
                                controller.push(it)
                            },
                        )
                        if (tab == HomeTabRoute.Message) {
                            NtfFullScreenAlertState()
                        }
                    }
                    appEntry<Route.VoicePublish>(metadata = getVerticalTransitionMetadata()) {
                        VoicePublishHostScreen {
                            controller.popIf(Route.VoicePublish)
                        }
                    }
                    appEntry<Route.LiveRoom> {
                        LiveRoomHostScreen(it.basicInfo)
                    }
                    appEntry<Route.InviteToPrivateRoom> {
                        InviteToPrivateRoomScreen(it.info, it.firstEnter)
                    }
                    appEntry<Route.MediaSelector> {
                        MediaSelectorScreen(
                            resultKey = it.resultKey,
                            mediaType = it.mediaType,
                            maxSelectCount = it.maxSelectCount.coerceAtLeast(1),
                            selectedItem = it.selectedItem,
                        )
                    }
                    appEntry<Route.UserProfile> {
                        val user = it.user
                        UserProfileScreen(
                            user = user,
                            toEditUserInfo = {
                                controller.push(Route.EditUserInfo)
                            },
                            toSettings = {
                                controller.push(Route.Settings)
                            },
                            toUserRelations = {
                                // 暂时不支持看他人关注信息
                                if (user.sIsSelf) {
                                    controller.push(Route.UserRelations(it, user.id))
                                }
                            },
                            toNavigation = {
                                controller.push(it)
                            },
                        )
                    }
                    appEntry<Route.EditUserInfo> {
                        EditUserInfoScreen(
                            onNavigateTo = {
                                controller.push(it)
                            },
                        )
                    }
                    appEntry<Route.EditTextUserInfo> {
                        EditTextUserInfoScreen(it.edit)
                    }
                    appEntry<Route.UserRelations> {
                        UserRelationsScreen(it.initIndex, it.userId)
                    }
                    appEntry<Route.Report> {
                        ReportScreen(it)
                    }
                    appEntry<Route.Settings> {
                        SettingsScreen(
                            onBlacklistClick = {
                                controller.push(Route.BlackList)
                            },
                            onFeedbackClick = {
                                controller.push(Route.Feedback)
                            },
                            onAboutAppClick = {
                                controller.push(Route.AboutApp)
                            },
                            onDebug = {
                                controller.push(Route.Debug)
                            },
                        )
                    }
                    appEntry<Route.BlackList> {
                        BlackListScreen()
                    }
                    appEntry<Route.Feedback> {
                        FeedbackScreen()
                    }
                    appEntry(Route.RechargeRecord) {
                        RechargeRecordScreenUI()
                    }
                    appEntry(Route.Recharge) {
                        RechargeScreen(onRecordClick = {
                            if (SelfUser?.isCN == true) {
                                controller.push(Route.RechargeRecord)
                            } else {
                                controller.push(Route.JaDiamondRecord)
                            }
                        }, onOpenWeb = {
                            controller.push(Route.Web(it))
                        })
                    }
                    appEntry(Route.Member) {
                        MemberScreen(onOpenWeb = {
                            controller.push(Route.Web(it))
                        })
                    }
                    appEntry<Route.AboutApp> {
                        AboutAppScreen(
                            onUserAgreementClick = {
                                controller.push(
                                    Route.Web(
                                        url = "${EnvironmentManager.current.apiUrl}h5/agreement",
                                        title = "用户服务协议".localized,
                                    ),
                                )
                            },
                            onPrivacyPolicyClick = {
                                controller.push(
                                    Route.Web(
                                        url = "${EnvironmentManager.current.apiUrl}h5/privacy",
                                        title = "隐私协议".localized,
                                    ),
                                )
                            },
                        )
                    }
                    appEntry<Route.Chat> {
                        val viewModel =
                            viewModel<C2CChatViewModel>(initializer = {
                                C2CChatViewModel(it.user)
                            })
                        C2CChatScreen(it.user, viewModel)
                    }
                    appEntry<Route.SelectUser> {
                        SelectUserScreen(it)
                    }
                    appEntry<Route.SelectUserV2> {
                        SelectUserFromChatAndFriends(it)
                    }
                    appEntry<Route.Debug> {
                        DebugScreen(controller)
                    }
                    appEntry<Route.ChatGroup> {
                        ChatGroupHostScreen(it.groupId, it.detail)
                    }
                    appEntry<Route.ChatGroupSquare> {
                        GroupSquareScreen()
                    }

//                    appEntry<Route.GiftWall> {
//                        GiftWallScreen(it.userId)
//                    }
                    appEntry(Route.DressUpSample) {
                        DressupShopTabScreen(
                            toTabListScreen = { tabName, tabType ->
                                controller.push(Route.DressUpShopList(tabType, tabName))
                            },
                            toDressUpCenter = {
                                val ret = controller.popUntil { it is Route.DressUpMine }
                                if (!ret) {
                                    controller.push(Route.DressUpMine(it))
                                }
                            },
                        )
                    }
                    appEntry<Route.DressUpShopList> {
                        DressupShopListScreen(it.type, it.title, toDressUpCenter = {
                            controller.push(Route.DressUpMine(-1))
                        })
                    }
                    appEntry<Route.DressUpMine> {
                        DressupCenterScreen(initTab = it.initialTab, toDressShop = { tabType, tabName ->
                            if (tabType == null) {
                                val ret = controller.popUntil { it is Route.DressUpSample }
                                if (!ret) {
                                    controller.push(Route.DressUpSample)
                                }
                            } else {
                                controller.push(Route.DressUpShopList(tabType, tabName))
                            }
                        })
                    }
                    appEntry<Route.UserSearch> {
                        UserSearchScreen(onCancel = {
                            controller.pop()
                        }, onItemClick = {
                            when (it.type) {
                                0 -> {
                                    (it.origin as? BasicUser)?.apply {
                                        controller.push(Route.UserProfile(this))
                                    }
                                }

                                1 -> {
                                    (it.origin as? MyLiveRoomInfo)?.apply {
                                        LiveRoomManager.joinRoom(this.toBasicRoomInfo())
                                    }
                                }

                                2 -> {
                                    (it.origin as? ChatGroupBean)?.apply {
                                        controller.push(Route.ChatGroup(this.id))
                                    }
                                }
                            }
                        })
                    }
                    appEntry<Route.UserAlbum> {
                        UserAlbumScreen()
                    }
                    appEntry<Route.BoostPage> {
                        BoostPage(it.type)
                    }

                    appEntry<GalleryScreenKey> {
                        GalleryScreen(it)
                    }
                    appEntry<Route.MomentPublish> {
                        MomentPublishScreen()
                    }
                    appEntry<Route.MomentList> {
                        UserMomentListScreen(it.userId)
                    }

                    //region 通用页面
                    appEntry<Route.LocationSelector> { it ->
                        LocationSelectorScreen(it.resultKey)
                    }
                    //endregion

                    //region 用户资料
                    appEntry<Route.UserProfileVoiceEdit> {
                        ProfileVoiceEditScreen()
                    }
                    //endregion

                    appEntry<Route.PointsRecord> {
                        PointsRecordScreen()
                    }
                    appEntry<Route.JaDiamondRecord> {
                        PointsRecordScreen("充值消费记录".localized, 21)
                    }
                    appEntry<Route.VisitorHistory> {
                        VisitorHistoryPage(toNavigate = {
                            controller.push(it)
                        })
                    }
                    appEntry<Route.SilverShopList> {
                        SilverShopListScreen(it.tab)
                    }
                    appEntry<Route.CronySetting> {
                        CronySettingsScreen()
                    }
                },
        )
    }
}
