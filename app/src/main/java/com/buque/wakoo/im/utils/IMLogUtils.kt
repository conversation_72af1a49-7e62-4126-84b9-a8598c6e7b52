package com.buque.wakoo.im.utils

import android.util.Log
import com.buque.wakoo.manager.EnvironmentManager

object IMLogUtils {
    fun i(
        tag: String,
        content: String,
    ) {
        if (EnvironmentManager.current.enableLog) {
            Log.i("imclient", "[$tag]$content")
        }
    }

    fun i(content: String) {
        i("IM", content)
    }

    fun w(content: String) {
        if (EnvironmentManager.current.enableLog) {
            Log.w("imclient", "[IM]$content")
        }
    }
}
