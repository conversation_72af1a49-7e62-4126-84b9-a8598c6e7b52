package com.buque.wakoo.im.push

import android.content.Intent
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.ext.getOrNull
import com.buque.wakoo.ext.getStringOrNull
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.bean.SendParams
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.utils.takeIsNotEmpty
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.AppManager
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.utils.eventBus.AppEvent
import com.buque.wakoo.utils.eventBus.EventBus
import com.buque.wakoo.utils.eventBus.tryToLink
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject
import retrofit2.http.Body
import retrofit2.http.POST
import kotlin.coroutines.cancellation.CancellationException

interface PushApi {
    @POST("api/xya/general/v1/push/bridge")
    suspend fun jump(
        @Body map: Map<String, String>,
    ): ApiResponse<JsonObject>
}

object PushRouteHandler {
    //        ('user_home', '用户个人主页'),  # 参数：{"user_id": "11111"}
//        ('tribe_detail', '部落详情'),  # 参数：{"tribe_id": "11111"}
//        ('private_chat', '私聊'),  # 参数：{"user_id": "11111", "msg": "要发送的消息"}
//        ('message_list', '消息列表'),  # 参数：{}
//        ('new_user_task', '新人任务页面'),  # 参数：{"user_id": "11111"}
    private const val ACTION_FOR_USER_HOME = "user_home"
    private const val ACTION_FOR_TRIBE_DETAIL = "tribe_detail"
    private const val ACTION_FOR_PRIVATE_CHAT = "private_chat"
    private const val ACTION_FOR_NOTIFICATION_MESSAGE = "message_list"
    private const val ACTION_FOR_NEW_USER_TASK = "new_user_task"
    private const val ACTION_CALL_JUMP_API = "call_jump_api"

    private val api = ApiClient.createuserApiService<PushApi>()
    private val sAppJson: Json
        get() = AppJson

    fun handle(ext: String?) {
        if (ext.isNullOrEmpty()) {
            "wakoo://page/main?tab=message".tryToLink()
            return
        }
        try {
            val json = sAppJson.parseToJsonElement(ext).jsonObject
            val action = json.getStringOrNull("open_page")
            val jsonObject = json.getOrNull("page_params")?.jsonObject
            if (!action.isNullOrEmpty()) {
                handle(action, jsonObject)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun handle(
        action: String,
        jsonObject: JsonObject?,
    ) {
        jsonObject ?: return
        if (AppManager.appActivity == null) { // Activity没启动，需要拉起入口Activity
            WakooApplication.instance.apply {
                packageManager
                    .getLaunchIntentForPackage(packageName)
                    ?.component
                    ?.let {
                        Intent.makeRestartActivityTask(it)
                    }?.also {
                        startActivity(it)
                    }
            }
            if (AccountManager.isLoggedIn) { // 已登录状态，监听主Activity启动后再路由
                AppManager.appActivityFlow
                    .onEach { activity ->
                        if (activity != null) {
                            delay(200)
                            handle(action, jsonObject)
                            throw CancellationException("")
                        }
                    }.launchIn(appCoroutineScope)
            }
            return
        }
        if (!AccountManager.isLoggedIn) {
            return
        }

        when (action) {
            ACTION_CALL_JUMP_API -> {
                appCoroutineScope.launch {
                    sAppJson.encodeToString(jsonObject).takeIsNotEmpty()?.also {
                        executeApiCallExpectingData {
                            api.jump(mapOf("jump_params" to it))
                        }.onSuccess { obj ->
                            val link = obj.getStringOrNull("action_link") ?: return@launch
                            link.tryToLink()
                        }
                    }
                }
            }

            ACTION_FOR_NEW_USER_TASK -> {
                // WelfareCenterNavigator
            }

            ACTION_FOR_TRIBE_DETAIL -> {
                val id = jsonObject.getStringOrNull("tribe_id").orEmpty()
                if (id.isEmpty()) {
                    return
                }
                EventBus.trySend(AppEvent.Route(Route.ChatGroup(id)))
            }

            ACTION_FOR_USER_HOME -> {
                appCoroutineScope.launch {
                    val id = jsonObject.getStringOrNull("user_id").orEmpty()
                    if (id.isNotEmpty()) {
                        EventBus.send(AppEvent.Route(Route.UserProfile(BasicUser.fromUid(id))))
                    }
                }
            }

            ACTION_FOR_PRIVATE_CHAT -> {
                val id = jsonObject.getStringOrNull("user_id").orEmpty()
                if (id.isEmpty()) {
                    return
                }
                val msg = jsonObject.getStringOrNull("msg").orEmpty()
                if (msg.isNotEmpty()) {
                    IMCompatCore.sendMessage(SendParams(id, ConversationType.C2C), MessageBundle.Text.create(msg))
                }
                appCoroutineScope.launch {
                    delay(200)
                    EventBus.send(AppEvent.Route(Route.Chat(BasicUser.fromUid(id))))
                }
            }

            ACTION_FOR_NOTIFICATION_MESSAGE -> {
                "wakoo://page/main?tab=message".tryToLink()
            }
        }
    }
}
