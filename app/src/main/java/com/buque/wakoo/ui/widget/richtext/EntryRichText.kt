package com.buque.wakoo.ui.widget.richtext

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.LocalTextStyle
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withLink
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.core.text.HtmlCompat
import com.buque.wakoo.bean.RichItem
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.utils.eventBus.AppEvent
import com.buque.wakoo.utils.eventBus.EventBus
import com.buque.wakoo.utils.eventBus.tryToLink

@Composable
fun EntryRichText(
    rich: List<RichItem>,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    fontStyle: FontStyle? = null,
    fontWeight: FontWeight? = null,
    fontFamily: FontFamily? = null,
    letterSpacing: TextUnit = TextUnit.Unspecified,
    textDecoration: TextDecoration? = null,
    textAlign: TextAlign? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    onTextLayout: (TextLayoutResult) -> Unit = {},
    style: TextStyle = LocalTextStyle.current,
) {
    RichText(
        modifier = modifier,
        key = rich,
        color = color,
        fontSize = fontSize,
        fontStyle = fontStyle,
        fontWeight = fontWeight,
        fontFamily = fontFamily,
        letterSpacing = letterSpacing,
        textDecoration = textDecoration,
        textAlign = textAlign,
        lineHeight = lineHeight,
        overflow = overflow,
        softWrap = softWrap,
        maxLines = maxLines,
        minLines = minLines,
        onTextLayout = onTextLayout,
        style = style,
    ) {
        rich.forEachIndexed { index, item ->
            if (item.type == 1) {
                val key = "text_1_$index"
                withBuilder {
                    val html =
                        HtmlCompat.fromHtml(item.text, HtmlCompat.FROM_HTML_OPTION_USE_CSS_COLORS).toAnnotatedString()
                    if (!item.jumpLink.isNullOrBlank()) {
                        val clickableAnnotation =
                            LinkAnnotation.Clickable(
                                tag = key, // 一个唯一的标识符，用于区分不同的链接
                                linkInteractionListener = {
                                    item.jumpLink?.tryToLink()
                                },
                            )
                        withLink(clickableAnnotation) {
                            append(html)
                        }
                    } else if (item.action.isNotEmpty()) {
                        val clickableAnnotation =
                            LinkAnnotation.Clickable(
                                tag = key, // 一个唯一的标识符，用于区分不同的链接
                                linkInteractionListener = {
                                    EventBus.trySend(AppEvent.Action(item.action))
                                },
                            )
                        withLink(clickableAnnotation) {
                            append(html)
                        }
                    } else {
                        append(html)
                    }
                }
            } else if (item.type == 2) {
                InlineSizedContent(item.width.dp, item.height.dp) {
                    NetworkImage(
                        data = item.icon,
                        modifier =
                            Modifier
                                .fillMaxSize()
                                .run {
                                    if (item.radius > 0) {
                                        clip(RoundedCornerShape(item.radius.dp))
                                    } else {
                                        this
                                    }
                                    if (!item.link.isNullOrEmpty()) {
                                        clickable {
                                            // TODO: 路由实现
                                        }
                                    } else {
                                        this
                                    }
                                },
                    )
                }
            }
        }
    }
}
