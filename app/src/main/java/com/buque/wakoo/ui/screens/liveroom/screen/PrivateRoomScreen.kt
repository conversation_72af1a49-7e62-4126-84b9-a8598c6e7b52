package com.buque.wakoo.ui.screens.liveroom.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.consts.SceneType
import com.buque.wakoo.ext.formatSecondsDuration
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.icons.More
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.liveroom.InputTextState
import com.buque.wakoo.ui.screens.liveroom.LiveMicLayout
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInputLayout
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMessageLayout
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import com.buque.wakoo.ui.screens.liveroom.LiveRoomOverlayLayout
import com.buque.wakoo.ui.screens.liveroom.PrivateRoomBottomLayout
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.panel.LiveRoomSettingPanelDialog
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.WakooTitleBar
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults
import com.buque.wakoo.ui.widget.gift.GiftViewModel
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.utils.DateTimeUtils
import com.buque.wakoo.viewmodel.AudioRoomDailyViewModel
import com.buque.wakoo.viewmodel.HongBaoViewModel
import com.buque.wakoo.viewmodel.liveroom.LiveRoomViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

@Composable
fun PrivateRoomScreen(
    viewModel: LiveRoomViewModel,
    giftViewModel: GiftViewModel,
) {
    val roomInfoState = viewModel.roomInfoState
    Box(
        modifier = Modifier.fillMaxSize(),
    ) {
        val data =
            if (!roomInfoState.basicInfo.background.isNullOrBlank()) {
                roomInfoState.basicInfo.background
            } else {
                R.drawable.bg_live_room_private
            }
        NetworkImage(
            data = data,
            modifier = Modifier.fillMaxSize(),
            placeholder = painterResource(R.drawable.bg_live_room_private),
            error = painterResource(R.drawable.bg_live_room_private),
        )

        if (roomInfoState.basicInfo.roomMode.isUnKnown) {
            return
        }

        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .systemBarsPadding(),
            verticalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            // 语音房顶部操作栏
            WakooTitleBar(
                title = "CP小屋".localized,
                titleContentColor = WakooWhite,
                actions = {
                    WakooTitleBarDefaults.IconButtonAction(
                        imageVector = WakooIcons.More,
                        iconModifier = Modifier.size(24.dp),
                        colors = IconButtonDefaults.iconButtonColors(contentColor = WakooWhite),
                        onClick = {
                            roomInfoState.sendEvent(
                                RoomEvent.RestorableDialog(LiveRoomSettingPanelDialog()),
                            )
                        },
                    )
                },
            )

            Column(modifier = Modifier.fillMaxWidth()) {
                // 语音房麦位
                LiveMicLayout(roomInfoState)

                Column(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(133.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    val privateRoomInfo = roomInfoState.extraInfo.privateRoomInfo
                    if (privateRoomInfo?.mode == 2) {
                        Box(
                            modifier =
                                Modifier
                                    .padding(top = 40.dp)
                                    .height(26.dp)
                                    .background(Color(0x26FFFFFF), CircleShape)
                                    .padding(horizontal = 12.dp),
                            contentAlignment = Alignment.Center,
                        ) {
                            if (privateRoomInfo.interact.status == 1) {
                                var time by remember(privateRoomInfo.interact.endTimestamp) {
                                    mutableStateOf(
                                        privateRoomInfo.interact.endTimestamp
                                            .minus(DateTimeUtils.currentTimeSeconds())
                                            .coerceAtLeast(0)
                                            .formatSecondsDuration,
                                    )
                                }
                                LaunchedEffect(privateRoomInfo.interact.endTimestamp) {
                                    while (isActive) {
                                        delay(1000)
                                        time =
                                            privateRoomInfo.interact.endTimestamp
                                                .minus(DateTimeUtils.currentTimeSeconds())
                                                .coerceAtLeast(0)
                                                .formatSecondsDuration
                                    }
                                }
                                Text(
                                    text = "${"剩余通话时长".localized} $time",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = WakooWhite,
                                )
                            } else {
                                val time =
                                    remember(privateRoomInfo.interact.status, privateRoomInfo.interact.remindSeconds) {
                                        if (privateRoomInfo.interact.status == 5) {
                                            privateRoomInfo.interact.remindSeconds.formatSecondsDuration
                                        } else {
                                            "00:00"
                                        }
                                    }
                                Text(
                                    text = "${"剩余通话时长".localized} $time",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = WakooWhite,
                                )
                            }
                        }

                        if (!privateRoomInfo.selfIsStar) {
                            Column(
                                modifier =
                                    Modifier
                                        .padding(top = 20.dp)
                                        .widthIn(min = 146.dp)
                                        .background(
                                            brush =
                                                Brush.verticalGradient(
                                                    listOf(Color(0xFFFFA5D6), Color(0xFFFF358A)),
                                                ),
                                            CircleShape,
                                        ).border(1.dp, Color(0x80FFFFFF), CircleShape)
                                        .clickable {
                                            roomInfoState.sendEvent(
                                                RoomEvent.AddFriend(
                                                    userId = privateRoomInfo.targetUser.id,
                                                    sceneType = 3,
                                                    sceneId = roomInfoState.id.toInt(),
                                                ),
                                            )
                                        }.padding(horizontal = 20.dp, vertical = 4.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.spacedBy(3.dp, Alignment.CenterVertically),
                            ) {
                                Text(
                                    text = "加好友".localized,
                                    style = MaterialTheme.typography.bodySmall,
                                    color = WakooWhite,
                                    fontWeight = FontWeight.Medium,
                                )
                                Text(
                                    text = "不限通话时长".localized,
                                    style = MaterialTheme.typography.labelMedium,
                                    color = WakooWhite,
                                )
                            }
                        } else {
                            Box(
                                modifier =
                                    Modifier
                                        .padding(top = 12.dp)
                                        .height(26.dp)
                                        .background(Color(0x26FFFFFF), RoundedCornerShape(8.dp))
                                        .padding(horizontal = 12.dp),
                                contentAlignment = Alignment.Center,
                            ) {
                                Text(
                                    text = privateRoomInfo.interact.starHint,
                                    style = MaterialTheme.typography.labelLarge,
                                    color = Color(0xFFFFD683),
                                )
                            }
                        }
                    }
                }
            }

            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .weight(1f),
            ) {
                LiveRoomMessageLayout(viewModel)
            }

            PrivateRoomBottomLayout(roomInfoState, giftViewModel) {
                viewModel.setInputTextState(InputTextState.Visible())
            }

            SizeHeight(10.dp)
        }

        LiveRoomInputLayout(
            inputState = viewModel.inputTextState.value,
            onInputStateChange = {
                viewModel.setInputTextState(it)
            },
            modifier =
                Modifier
                    .imePadding()
                    .fillMaxWidth()
                    .background(Color.White),
        ) {
            viewModel.sendMessage(MessageBundle.Text.create(it))
        }

        LiveRoomOverlayLayout(
            roomInfoState = roomInfoState,
            viewModel = viewModel,
            giftViewModel = giftViewModel,
            hongBaoViewModel =
                viewModel(initializer = {
                    HongBaoViewModel(SceneType.WAKOO_PRIVATE_ROOM, roomInfoState.id)
                }),
        )
    }
}

@Preview
@Composable
private fun PrivateRoomScreenPreview() {
    WakooTheme {
        PrivateRoomScreen(
            viewModel<LiveRoomViewModel>(
                factory =
                    LiveRoomViewModel.Factory(
                        BasicRoomInfo.preview.copy(
                            roomMode = LiveRoomMode.Private,
                        ),
                    ),
            ),
            giftViewModel =
                viewModel<GiftViewModel>(initializer = {
                    GiftViewModel(
                        bid = "10086",
                        rcId = "10086",
                        type = ConversationType.CHATROOM,
                    )
                }),
        )
    }
}
