package com.buque.wakoo.ui.icons

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.Fire: ImageVector
    get() {
        if (_Fire != null) {
            return _Fire!!
        }
        _Fire = ImageVector.Builder(
            name = "Fire",
            defaultWidth = 20.dp,
            defaultHeight = 20.dp,
            viewportWidth = 20f,
            viewportHeight = 20f
        ).apply {
            path(
                fill = Brush.linearGradient(
                    colorStops = arrayOf(
                        0f to Color(0xFFFFE9A1),
                        1f to Color(0xFFFF423D)
                    ),
                    start = Offset(10f, 1.25f),
                    end = Offset(10f, 19.167f)
                )
            ) {
                moveTo(10f, 19.167f)
                curveTo(6.548f, 19.167f, 3.75f, 16.368f, 3.75f, 12.917f)
                curveTo(3.75f, 11.122f, 4.507f, 9.504f, 5.718f, 8.364f)
                curveTo(6.837f, 7.311f, 9.583f, 5.416f, 9.167f, 1.25f)
                curveTo(14.167f, 4.583f, 16.667f, 7.917f, 11.667f, 12.917f)
                curveTo(12.5f, 12.917f, 13.75f, 12.917f, 15.833f, 10.858f)
                curveTo(16.058f, 11.503f, 16.25f, 12.195f, 16.25f, 12.917f)
                curveTo(16.25f, 16.368f, 13.452f, 19.167f, 10f, 19.167f)
                close()
            }
        }.build()

        return _Fire!!
    }

@Suppress("ObjectPropertyName")
private var _Fire: ImageVector? = null
