package com.buque.wakoo.ui.screens.chatgroup.member.task

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.state.StateListPaginateLayout

@Composable
fun CPMemberNotPublicScreen() {
    val title = "群组内尚未官宣的CP".localized
    val vm: TaskCPListViewModel = viewModel()
    val count by vm.stateOfCount
    TitleScreenScaffold(title) {
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(it),
        ) {
            StateListPaginateLayout<Int, CpRelation, TaskCPListViewModel>(viewModel = vm) { state, data ->
                LazyColumn(
                    modifier =
                        Modifier
                            .fillMaxSize()
                            .padding(horizontal = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                ) {
                    item {
                        Row(
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .height(44.dp)
                                    .padding(horizontal = 16.dp),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            Text(title, fontSize = 12.sp, color = WakooGrayText)
                            SizeWidth(4.dp)
                            Text("%d对".localizedFormat(count), fontSize = 12.sp, color = Color(0xFFFF385C))
                        }
                    }
                    items(data) { item ->
                        CPItem(item)
                    }
                }
            }
        }
    }
}

@Composable
fun CPItem(cpRelation: CpRelation) {
    Row(
        modifier =
            Modifier
                .fillMaxWidth()
                .aspectRatio(343 / 122f)
                .background(Brush.verticalGradient(listOf(Color(0xFFFFC3E9), Color(0xFFFF3E8E))), RoundedCornerShape(12.dp))
                .border(1.dp, Color(0xFFFFF4B0), RoundedCornerShape(12.dp))
                .padding(vertical = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        CPUserItem(
            cpRelation.user1,
            modifier =
                Modifier
                    .fillMaxHeight()
                    .weight(1f),
        )

        Image(
            painter = painterResource(R.drawable.icon_cp_heart),
            modifier = Modifier.size(56.dp),
            contentDescription = null,
            contentScale = ContentScale.FillBounds,
        )

        CPUserItem(
            cpRelation.user2,
            modifier =
                Modifier
                    .fillMaxHeight()
                    .weight(1f),
        )
    }
}

@Composable
private fun CPUserItem(
    userResponse: UserResponse,
    modifier: Modifier,
) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally, verticalArrangement = Arrangement.SpaceBetween) {
        AvatarNetworkImage(userResponse.basicUser, size = 64.dp, border = BorderStroke(1.dp, WakooWhite))
        Text(userResponse.nickname, color = Color.White, maxLines = 1, overflow = TextOverflow.Ellipsis, textAlign = TextAlign.Center)
        GenderAgeTag(userResponse.basicUser)
    }
}
