package com.buque.wakoo.ui.widget.coordinatorlayout

import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.CubicBezierEasing
import androidx.compose.animation.core.DecayAnimationSpec
import androidx.compose.animation.core.animateDecay
import androidx.compose.animation.core.animateTo
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.gestures.rememberDraggableState
import androidx.compose.foundation.layout.Box
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TopAppBarScrollBehavior
import androidx.compose.material3.TopAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Velocity
import androidx.compose.ui.unit.isSpecified
import kotlin.math.roundToInt

/**
 * 一个高度可定制、状态驱动的可收缩头部组件。
 *
 * 其核心行为由传入的 `state: CustomCollapsibleHeaderState` 和 `scrollBehavior: TopAppBarScrollBehavior` 驱动。
 * 组件会自动处理高度的测量和状态的更新，并通过 `state` 对象暴露所有可观察的数据（如 `collapsedFraction`）
 * 和可调用的方法（如 `expand()` / `collapse()`）。
 *
 * @param modifier The [Modifier] to be applied to this header.
 * @param state 一个 [CustomCollapsibleHeaderState] 对象，用于控制和观察头部状态。
 *              通过 `rememberCustomCollapsibleHeaderState()` 创建和获取。
 * @param scrollBehavior 一个 [TopAppBarScrollBehavior]，用于将头部与滚动列表（如 `LazyColumn`）联动。
 *                       **重要**: 创建此 behavior 时，必须将其内部的 state 指向我们自己的 `state.topAppBarState`，
 *                       例如：`TopAppBarDefaults.exitUntilCollapsedScrollBehavior(state.topAppBarState)`。
 * @param collapsedContent 当头部完全收起时显示的内容的 Composable lambda。
 * @param expandedContent 当头部完全展开时显示的内容的 Composable lambda。此内容会随着滚动向上移动并淡出。
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustomCollapsibleHeader(
    modifier: Modifier = Modifier,
    state: CustomCollapsibleHeaderState,
    scrollBehavior: TopAppBarScrollBehavior,
    applyAlphaModifier: Boolean = true,
    collapsedContent: @Composable () -> Unit = {},
    expandedContent: @Composable () -> Unit = {},
) {
    val density = LocalDensity.current
    val collapsedFraction = state.collapsedFraction

    val expandedContentAlpha = 1f - collapsedFraction
    val collapsedContentAlpha = TopTitleAlphaEasing.transform(collapsedFraction)

    val appBarDragModifier =
        if (!scrollBehavior.isPinned) {
            Modifier.draggable(
                orientation = Orientation.Vertical,
                state =
                    rememberDraggableState { delta ->
                        state.topAppBarState.heightOffset += delta
                    },
                onDragStopped = { velocity ->
                    // 拖拽停止后，调用 settleAppBar 执行动画
                    // 直接从 scrollBehavior 获取动画规格，该规格已经由 remember 函数根据开关配置好了
                    settleAppBar(
                        state.topAppBarState,
                        velocity,
                        scrollBehavior.flingAnimationSpec,
                        scrollBehavior.snapAnimationSpec,
                    )
                },
            )
        } else {
            Modifier
        }

    SubcomposeLayout(
        modifier =
            modifier
                .then(appBarDragModifier)
                .clipToBounds(),
    ) { constraints ->
        // 1. 测量阶段
        val collapsedPlaceable =
            subcompose("collapsed") {
                val finalAlpha = if (state.collapsedHeight.isSpecified) collapsedContentAlpha else 0f
                Box(modifier = if (applyAlphaModifier) Modifier.graphicsLayer { alpha = finalAlpha } else Modifier) { collapsedContent() }
            }.first()
                .measure(
                    constraints.copy(
                        minWidth = 0,
                        minHeight = 0,
                    ),
                )

        val expandedPlaceable =
            subcompose("expanded") {
                val finalAlpha = if (state.expandedHeight.isSpecified) expandedContentAlpha else 1f
                Box(modifier = if (applyAlphaModifier) Modifier.graphicsLayer { alpha = finalAlpha } else Modifier) { expandedContent() }
            }.first()
                .measure(
                    constraints.copy(
                        minWidth = 0,
                        minHeight = 0,
                    ),
                )

        val measuredCollapsedHeight = with(density) { collapsedPlaceable.height.toDp() }
        val measuredExpandedHeight = with(density) { expandedPlaceable.height.toDp() }

        // 2. 状态更新与布局计算
        val currentHeightPx: Float
        if (state.collapsedHeight != measuredCollapsedHeight || state.expandedHeight != measuredExpandedHeight) {
            state.collapsedHeight = measuredCollapsedHeight
            state.expandedHeight = measuredExpandedHeight
            currentHeightPx = expandedPlaceable.height.toFloat()
        } else {
            if (state.expandedHeight.isSpecified) {
                val collapsedHeightPx = with(density) { state.collapsedHeight.toPx() }
                val expandedHeightPx = with(density) { state.expandedHeight.toPx() }
                state.topAppBarState.heightOffsetLimit = collapsedHeightPx - expandedHeightPx
                currentHeightPx = expandedHeightPx + state.topAppBarState.heightOffset
            } else {
                currentHeightPx = expandedPlaceable.height.toFloat()
            }
        }
        val layoutHeight =
            currentHeightPx
                .roundToInt()
                .coerceAtLeast(0)

        // 3. 布局放置
        layout(
            constraints.maxWidth,
            layoutHeight,
        ) {
            collapsedPlaceable.placeRelative(
                x = 0,
                y = 0,
            )
            val expandedContentY = layoutHeight - expandedPlaceable.height
            expandedPlaceable.placeRelative(
                x = 0,
                y = expandedContentY,
            )
        }
    }
}

/**
 * 私有的辅助函数，用于在拖拽或快速滑动结束后，通过动画将头部平滑地固定到最终状态。
 *
 * @param state 当前的 TopAppBarState。
 * @param velocity 拖拽或滑动停止时的瞬时速度。
 * @param flingAnimationSpec 惯性滑动动画的规格。
 * @param snapAnimationSpec 吸附动画的规格。如果为 null，则不执行吸附动画。
 * @return 未被消费的速度。
 */
@OptIn(ExperimentalMaterial3Api::class)
suspend fun settleAppBar(
    state: TopAppBarState,
    velocity: Float,
    flingAnimationSpec: DecayAnimationSpec<Float>?,
    snapAnimationSpec: AnimationSpec<Float>?,
): Velocity {
    if (state.collapsedFraction < 0.01f || state.collapsedFraction == 1f) {
        return Velocity.Zero
    }
    var remainingVelocity = velocity
    if (flingAnimationSpec != null && kotlin.math.abs(velocity) > 1f) {
        var lastValue = 0f
        androidx.compose.animation.core
            .AnimationState(
                initialValue = 0f,
                initialVelocity = velocity,
            ).animateDecay(flingAnimationSpec) {
                val delta = value - lastValue
                val initialHeightOffset = state.heightOffset
                state.heightOffset = initialHeightOffset + delta
                val consumed = kotlin.math.abs(initialHeightOffset - state.heightOffset)
                lastValue = value
                remainingVelocity = this.velocity
                if (kotlin.math.abs(delta - consumed) > 0.5f) this.cancelAnimation()
            }
    }
    if (snapAnimationSpec != null) {
        if (state.heightOffset < 0 && state.heightOffset > state.heightOffsetLimit) {
            androidx.compose.animation.core
                .AnimationState(initialValue = state.heightOffset)
                .animateTo(
                    if (state.collapsedFraction < 0.5f) 0f else state.heightOffsetLimit,
                    animationSpec = snapAnimationSpec,
                ) {
                    state.heightOffset = value
                }
        }
    }
    return Velocity(
        0f,
        remainingVelocity,
    )
}

val TopTitleAlphaEasing =
    CubicBezierEasing(
        .8f,
        0f,
        .8f,
        .15f,
    )
