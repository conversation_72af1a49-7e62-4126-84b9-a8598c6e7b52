/*
 * Copyright 2024 Wakoo
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.buque.wakoo.ui.widget.image

import android.graphics.Bitmap
import coil3.size.Size
import coil3.transform.Transformation
import kotlin.math.max
import kotlin.math.min

/**
 * 基于Stack Blur算法的高效模糊变换
 *
 * Stack Blur算法是Gaussian Blur和Box Blur的折中方案，
 * 提供比Box Blur更好的视觉效果，同时比Gaussian Blur快7倍。
 *
 * 算法原理：通过创建一个移动的颜色堆栈来扫描图像，
 * 只需要在堆栈右侧添加新的颜色块，并移除最左侧的颜色。
 *
 * 原始算法作者：<PERSON> <<EMAIL>>
 * Android移植：Ya<PERSON>
 * Ko<PERSON>优化版本：Wakoo Team
 */
class BlurTransformation(
    private val radius: Int = 10,
    private val sampling: Int = 1,
) : Transformation() {
    init {
        require(radius > 0) { "Blur radius must be positive" }
        require(sampling > 0) { "Sampling must be positive" }
    }

    override val cacheKey: String = "${BlurTransformation::class.java.name}-$radius-$sampling"

    override suspend fun transform(
        input: Bitmap,
        size: Size,
    ): Bitmap {
        val scaledBitmap =
            if (sampling > 1) {
                // 缩小图片以提高性能
                val scaledWidth = max(1, input.width / sampling)
                val scaledHeight = max(1, input.height / sampling)
                Bitmap.createScaledBitmap(input, scaledWidth, scaledHeight, true)
            } else {
                input.copy(input.config ?: Bitmap.Config.ARGB_8888, true)
            }

        // 应用Stack Blur算法
        val blurredBitmap = stackBlur(scaledBitmap, radius)

        return if (sampling > 1) {
            // 将模糊后的图片放大回原始尺寸
            Bitmap.createScaledBitmap(blurredBitmap, input.width, input.height, true)
        } else {
            blurredBitmap
        }
    }

    /**
     * Stack Blur算法实现
     *
     * @param bitmap 输入位图
     * @param radius 模糊半径 (1-250)
     * @return 模糊后的位图
     */
    private fun stackBlur(
        bitmap: Bitmap,
        radius: Int,
    ): Bitmap {
        if (radius < 1) return bitmap

        val actualRadius = min(radius, 250)
        val result = bitmap.copy(bitmap.config ?: Bitmap.Config.ARGB_8888, true)

        val w = result.width
        val h = result.height
        val wm = w - 1
        val hm = h - 1
        val wh = w * h
        val div = actualRadius + actualRadius + 1

        val pixels = IntArray(wh)
        result.getPixels(pixels, 0, w, 0, 0, w, h)

        val r = IntArray(wh)
        val g = IntArray(wh)
        val b = IntArray(wh)

        val vmin = IntArray(max(w, h))

        var divsum = (div + 1) shr 1
        divsum *= divsum
        val dv = IntArray(256 * divsum)
        for (i in 0 until 256 * divsum) {
            dv[i] = i / divsum
        }

        var yw = 0
        var yi = 0

        val stack = Array(div) { IntArray(3) }
        var stackpointer: Int
        var stackstart: Int
        var sir: IntArray
        var rbs: Int
        val r1 = actualRadius + 1
        var routsum: Int
        var goutsum: Int
        var boutsum: Int
        var rinsum: Int
        var ginsum: Int
        var binsum: Int

        for (y in 0 until h) {
            rinsum = 0
            ginsum = 0
            binsum = 0
            routsum = 0
            goutsum = 0
            boutsum = 0
            var rsum = 0
            var gsum = 0
            var bsum = 0

            for (i in -actualRadius..actualRadius) {
                val p = pixels[yi + min(wm, max(i, 0))]
                sir = stack[i + actualRadius]
                sir[0] = (p and 0xff0000) shr 16
                sir[1] = (p and 0x00ff00) shr 8
                sir[2] = p and 0x0000ff

                rbs = r1 - kotlin.math.abs(i)
                rsum += sir[0] * rbs
                gsum += sir[1] * rbs
                bsum += sir[2] * rbs

                if (i > 0) {
                    rinsum += sir[0]
                    ginsum += sir[1]
                    binsum += sir[2]
                } else {
                    routsum += sir[0]
                    goutsum += sir[1]
                    boutsum += sir[2]
                }
            }
            stackpointer = actualRadius

            for (x in 0 until w) {
                r[yi] = dv[rsum]
                g[yi] = dv[gsum]
                b[yi] = dv[bsum]

                rsum -= routsum
                gsum -= goutsum
                bsum -= boutsum

                stackstart = stackpointer - actualRadius + div
                sir = stack[stackstart % div]

                routsum -= sir[0]
                goutsum -= sir[1]
                boutsum -= sir[2]

                if (y == 0) {
                    vmin[x] = min(x + actualRadius + 1, wm)
                }
                val p = pixels[yw + vmin[x]]

                sir[0] = (p and 0xff0000) shr 16
                sir[1] = (p and 0x00ff00) shr 8
                sir[2] = p and 0x0000ff

                rinsum += sir[0]
                ginsum += sir[1]
                binsum += sir[2]

                rsum += rinsum
                gsum += ginsum
                bsum += binsum

                stackpointer = (stackpointer + 1) % div
                sir = stack[stackpointer % div]

                routsum += sir[0]
                goutsum += sir[1]
                boutsum += sir[2]

                rinsum -= sir[0]
                ginsum -= sir[1]
                binsum -= sir[2]

                yi++
            }
            yw += w
        }

        // 垂直方向模糊
        for (x in 0 until w) {
            rinsum = 0
            ginsum = 0
            binsum = 0
            routsum = 0
            goutsum = 0
            boutsum = 0
            var rsum = 0
            var gsum = 0
            var bsum = 0

            var yp = -actualRadius * w
            for (i in -actualRadius..actualRadius) {
                yi = max(0, yp) + x

                sir = stack[i + actualRadius]
                sir[0] = r[yi]
                sir[1] = g[yi]
                sir[2] = b[yi]

                rbs = r1 - kotlin.math.abs(i)

                rsum += r[yi] * rbs
                gsum += g[yi] * rbs
                bsum += b[yi] * rbs

                if (i > 0) {
                    rinsum += sir[0]
                    ginsum += sir[1]
                    binsum += sir[2]
                } else {
                    routsum += sir[0]
                    goutsum += sir[1]
                    boutsum += sir[2]
                }

                if (i < hm) {
                    yp += w
                }
            }

            yi = x
            stackpointer = actualRadius
            for (y in 0 until h) {
                pixels[yi] = (0xff000000.toInt() or (dv[rsum] shl 16) or (dv[gsum] shl 8) or dv[bsum])

                rsum -= routsum
                gsum -= goutsum
                bsum -= boutsum

                stackstart = stackpointer - actualRadius + div
                sir = stack[stackstart % div]

                routsum -= sir[0]
                goutsum -= sir[1]
                boutsum -= sir[2]

                if (x == 0) {
                    vmin[y] = min(y + r1, hm) * w
                }
                val p = x + vmin[y]

                sir[0] = r[p]
                sir[1] = g[p]
                sir[2] = b[p]

                rinsum += sir[0]
                ginsum += sir[1]
                binsum += sir[2]

                rsum += rinsum
                gsum += ginsum
                bsum += binsum

                stackpointer = (stackpointer + 1) % div
                sir = stack[stackpointer]

                routsum += sir[0]
                goutsum += sir[1]
                boutsum += sir[2]

                rinsum -= sir[0]
                ginsum -= sir[1]
                binsum -= sir[2]

                yi += w
            }
        }

        result.setPixels(pixels, 0, w, 0, 0, w, h)
        return result
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is BlurTransformation) return false
        return radius == other.radius && sampling == other.sampling
    }

    override fun hashCode(): Int {
        var result = radius
        result = 31 * result + sampling
        return result
    }

    override fun toString(): String = "BlurTransformation(radius=$radius, sampling=$sampling)"
}
