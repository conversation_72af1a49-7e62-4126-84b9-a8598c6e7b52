package com.buque.wakoo.ui.screens.crony

import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.ui.widget.state.toCState
import com.buque.wakoo.viewmodel.CSViewModel

class CronySettingsViewModel(val userId: String) : CSViewModel<CronySettings>() {

    private val repo = GlobalRepository.cronyRepo

    override suspend fun loadState(): CState<CronySettings> {
        return repo.getSettings(userId).toCState()
    }


    override suspend fun afterLoadState() {
        val st = rawState.value.dataOrNull ?: return
        if (st.enable && st.settings.relationshipNums > 0) {
            val items = loadCronyItems()
            val list = buildList {
                addAll(items)
                val avi = st.settings.relativeSeatAvailableCnt - st.settings.relationshipNums
                for (i in 0 until avi) {
                    add(EmptyCronyItem)
                }
            }
            rawState.value = CState.Success(st.copy(items = list))
        } else {
            val settings = st.settings
            rawState.value = CState.Success(st.copy(items = buildList {
                for (i in 0 until settings.relationshipNums) {
                    add(LoadingCronyItem)
                }
                for (i in 0 until settings.relativeSeatAvailableCnt) {
                    add(EmptyCronyItem)
                }
            }))
        }

    }


    private suspend fun loadCronyItems(): List<CronyItem> {
        var lastId = 0
        val list = mutableListOf<CronyItem>()
        while (true) {
            val result = repo.getRelations(userId, lastId)
            if (result.isSuccess) {
                val l = result.getOrNull().orEmpty()
                list.addAll(l)
                lastId = l.lastOrNull()?.id ?: -1
                if (lastId == -1) {
                    break
                }
            } else {
                break
            }
        }
        return list
    }

    suspend fun buySeat(count: Int) = repo.buyRelationshipSeat(count)
}