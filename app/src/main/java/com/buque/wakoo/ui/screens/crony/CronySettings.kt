package com.buque.wakoo.ui.screens.crony


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class CronySettings(
    @SerialName("enable")
    val enable: Boolean = false,
    @SerialName("settings")
    val settings: Settings = Settings(),
    @Transient
    val items: List<ICronyItem> = buildList {
        for (i in 0 until settings.relationshipNums) {
            add(LoadingCronyItem)
        }
        val avi = settings.relativeSeatAvailableCnt - settings.relationshipNums
        for (i in 0 until avi) {
            add(EmptyCronyItem)
        }
    }
) {
    @Serializable
    data class Settings(
        @SerialName("relationship_nums")
        val relationshipNums: Int = 0,
        @SerialName("relationship_rule_page_link")
        val relationshipRulePageLink: String = "",
        @SerialName("relative_seat_available_cnt")
        val relativeSeatAvailableCnt: Int = 0,
        @SerialName("relative_seat_free_cnt")
        val relativeSeatFreeCnt: Int = 0,
        @SerialName("relative_seat_most_paid_cnt")
        val relativeSeatMostPaidCnt: Int = 0,
        @SerialName("relative_seat_price")
        val relativeSeatPrice: Int = 0
    )
}