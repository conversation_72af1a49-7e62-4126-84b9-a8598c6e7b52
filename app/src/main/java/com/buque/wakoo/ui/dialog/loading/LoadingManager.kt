package com.buque.wakoo.ui.dialog.loading

import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.Stable
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color
import com.buque.wakoo.manager.EnvironmentManager
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Job
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.async
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withTimeout

// --- 数据类与配置 ---

/**
 * Loading 浮层的行为和非内容样式配置。
 *
 * @param dismissOnBackPress 是否在点击返回键时关闭。
 * @param dismissOnClickOutside 是否在点击遮罩时关闭 (仅在非点击穿透模式下有效)。
 * @param scrimColor 遮罩颜色 (仅在非点击穿透模式下有效)。
 * @param isClickPenetrate 是否允许点击穿透背景。如果为 true，背景可交互，遮罩将被忽略。
 */
data class LoadingProperties(
    val dismissOnBackPress: Boolean = true,
    val dismissOnClickOutside: Boolean = false,
    val scrimColor: Color = Color.Transparent,
    val isClickPenetrate: Boolean = false,
)

/**
 * 持有全局 Loading 实例的内部状态。
 *
 * @param properties UI 行为配置。
 * @param compositeJob 复合任务 Job。这是 Loading 生命周期的核心，通常是第一个被提交的任务的 Job。
 *                     当这个 Job 完成时 (意味着它的所有子任务都已完成)，Loading 会自动关闭。
 * @param progress 任务进度状态，一个可被 Compose 观察的 State，用于驱动 UI 更新。
 * @param content 要显示的 Composable 视图内容。
 */
internal data class LoadingState(
    val properties: LoadingProperties,
    val compositeJob: Job,
    val progress: State<Float?>,
    val content: @Composable (progress: Float?) -> Unit,
)

// --- 逻辑核心 ---

/**
 * 一个用于集中管理全局单例 Loading 状态的管理器。
 */
@Stable
class LoadingManager(
    private val scope: CoroutineScope,
) {
    /**
     * 持有当前唯一的 Loading 状态。
     * 当其值为非 null 时，UI 层会渲染出 Loading 浮层。
     * 当其值被设为 null 时，Loading 浮层会消失。
     */
    internal val state: MutableState<LoadingState?> = mutableStateOf(null)

    /**
     * 显示一个 Loading，或为已显示的 Loading 附加一个新任务。
     * Loading 会一直显示，直到所有通过此方法提交的任务都执行完毕。
     *
     * @param scope 任务运行的协程作用域。若为 null，则使用 Manager 内部的全局 scope。
     * @param text 当使用默认 `content` 时显示的文本。
     * @param properties UI 行为配置。仅在首次显示 Loading 时（即当前没有 Loading 显示时）生效。
     * @param timeoutMillis 单个任务的超时时间（毫秒）。
     * @param onTimeout 任务因超时被取消时触发的回调。
     * @param onError 任务执行中抛出未捕获的异常时触发的回调 (不包括 `CancellationException`)。
     * @param content 自定义 Loading UI 的 Composable。仅在首次显示 Loading 时生效。
     * @param block 要执行的后台任务。它接收一个 `updateProgress` 函数，可在任务内部调用以更新进度。
     * @return 返回代表此次提交任务的 `Job`，可以用于单独取消这个任务。
     */
    fun show(
        scope: CoroutineScope?,
        text: String? = null,
        properties: LoadingProperties = LoadingProperties(),
        timeoutMillis: Long? = null,
        onTimeout: (() -> Unit)? = null,
        onError: ((Throwable) -> Unit)? = null,
        content: @Composable (progress: Float?) -> Unit = { progress ->
            DefaultLoadingLayout(text = text, progress = progress)
        },
        block: suspend CoroutineScope.(updateProgress: (Float) -> Unit) -> Unit,
    ): Job = showWithResult(scope, text, properties, timeoutMillis, onTimeout, onError, content, block)

    /**
     * 显示一个 Loading，或为已显示的 Loading 附加一个新任务。
     * Loading 会一直显示，直到所有通过此方法提交的任务都执行完毕。
     *
     * @param scope 任务运行的协程作用域。若为 null，则使用 Manager 内部的全局 scope。
     * @param text 当使用默认 `content` 时显示的文本。
     * @param properties UI 行为配置。仅在首次显示 Loading 时（即当前没有 Loading 显示时）生效。
     * @param timeoutMillis 单个任务的超时时间（毫秒）。
     * @param onTimeout 任务因超时被取消时触发的回调。
     * @param onError 任务执行中抛出未捕获的异常时触发的回调 (不包括 `CancellationException`)。
     * @param content 自定义 Loading UI 的 Composable。仅在首次显示 Loading 时生效。
     * @param block 要执行的后台任务。它接收一个 `updateProgress` 函数，可在任务内部调用以更新进度。
     * @return 返回代表此次提交任务的 `Job`，可以用于单独取消这个任务。
     */
    fun <T> showWithResult(
        scope: CoroutineScope?,
        text: String? = null,
        properties: LoadingProperties = LoadingProperties(),
        timeoutMillis: Long? = null,
        onTimeout: (() -> Unit)? = null,
        onError: ((Throwable) -> Unit)? = null,
        content: @Composable (progress: Float?) -> Unit = { progress ->
            DefaultLoadingLayout(text = text, progress = progress)
        },
        block: suspend CoroutineScope.(updateProgress: (Float) -> Unit) -> T,
    ): Deferred<T?> {
        val targetScope = scope ?: this.scope
        val currentState = state.value

        // 如果 Loading 已经显示，将新任务作为子任务附加到现有的 compositeJob 上。
        // 这确保了 Loading 不会消失，直到这个新任务和所有其他任务都完成。
        if (currentState != null) {
            return targetScope.async(currentState.compositeJob) {
                executeBlock(this, timeoutMillis, onTimeout, onError) {
                    // 对于附加的任务，进度更新会影响同一个UI
                    block { progressValue ->
                        if (isActive) (currentState.progress as? MutableState)?.value = progressValue.coerceIn(0f, 1f)
                    }
                }
            }
        }

        // 如果 Loading 未显示，则创建并显示一个新的 Loading 实例。
        val progressState = mutableStateOf<Float?>(null)

        // 启动第一个任务。这个任务的 Job 将成为整个 Loading 生命周期的 `compositeJob`。
        val initialJob =
            targetScope.async {
                executeBlock(this, timeoutMillis, onTimeout, onError) {
                    block { progressValue -> if (isActive) progressState.value = progressValue.coerceIn(0f, 1f) }
                }
            }

        // 创建新的 Loading 状态，并将 initialJob 作为其生命周期控制器。
        val newState = LoadingState(properties, initialJob, progressState, content)

        // 设置一个回调，当 initialJob 完成时（即其自身和所有子任务都结束时），
        // 自动将 manager 的 state 置为 null，从而关闭 Loading 界面。
        initialJob.invokeOnCompletion {
            if (this.state.value?.compositeJob == initialJob) {
                this.state.value = null
            }
        }

        // 将新创建的状态赋值给 manager 的 state，这会触发 Composable UI 的渲染。
        this.state.value = newState

        // 返回这个初始任务的 Job。
        return initialJob
    }

    /**
     * 内部函数，用于封装任务的执行逻辑，包括超时和异常处理。
     */
    private suspend fun <T> executeBlock(
        scope: CoroutineScope,
        timeoutMillis: Long?,
        onTimeout: (() -> Unit)?,
        onError: ((Throwable) -> Unit)?,
        block: suspend CoroutineScope.() -> T,
    ): T? =
        try {
            if (timeoutMillis != null) {
                withTimeout(timeoutMillis) { block() }
            } else {
                scope.block()
            }
        } catch (e: TimeoutCancellationException) {
            onTimeout?.invoke()
            throw e
        } catch (e: CancellationException) {
            throw e
        } catch (e: Throwable) {
            if (!EnvironmentManager.isProdRelease) {
                throw e
            }
            onError?.invoke(e)
            null
        }

    /**
     * 手动关闭当前显示的 Loading。
     * 这会取消其根 `Job`，进而导致所有关联的子任务也一并被取消。
     */
    fun dismiss() {
        state.value?.compositeJob?.cancel()
        state.value = null
    }

    /**
     * 检查 Loading 是否正在显示。
     * @return 如果 Loading 正在显示，则返回 true。
     */
    fun isShowing(): Boolean = state.value != null

    /**
     * 独立更新当前 Loading 的进度。
     *
     * @param progress 新的进度值 (0.0 到 1.0)。传入 null 可将指示器切换为不确定（无限旋转）模式。
     */
    fun updateProgress(progress: Float?) {
        val currentState = state.value ?: return
        (currentState.progress as? MutableState<Float?>)?.value = progress?.coerceIn(0f, 1f)
    }
}

// --- Composable 集成 ---

/**
 * 在 Composable 函数中创建并记住一个 LoadingManager 实例。
 * 它利用 `rememberCoroutineScope` 来获取一个与 Composable 生命周期绑定的协程作用域。
 */
@Composable
fun rememberLoadingManager(): LoadingManager {
    val scope = rememberCoroutineScope()
    return remember { LoadingManager(scope) }
}

/**
 * CompositionLocal，用于在 Composable 树中隐式地向下传递 LoadingManager 实例。
 * 这避免了在组件层级间手动传递 manager 的需要。
 */
val LocalLoadingManager =
    staticCompositionLocalOf<LoadingManager> {
        error("LoadingManager not provided. Please wrap your app in ProvideLoadingManager.")
    }
