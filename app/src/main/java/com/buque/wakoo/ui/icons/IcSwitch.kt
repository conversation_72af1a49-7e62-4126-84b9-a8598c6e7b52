package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.Switch: ImageVector
    get() {
        if (_Switch != null) {
            return _Switch!!
        }
        _Switch = ImageVector.Builder(
            name = "Switch",
            defaultWidth = 12.dp,
            defaultHeight = 12.dp,
            viewportWidth = 12f,
            viewportHeight = 12f
        ).apply {
            path(
                stroke = SolidColor(Color(0xFFFFFFFF)),
                strokeLineWidth = 1f
            ) {
                moveTo(0.5f, 4.5f)
                horizontalLineTo(10.5f)
                lineTo(7.5f, 1.5f)
            }
            path(
                stroke = SolidColor(Color(0xFFFFFFFF)),
                strokeLineWidth = 1f
            ) {
                moveTo(11.5f, 7.5f)
                horizontalLineTo(1.5f)
                lineTo(4.5f, 10.5f)
            }
        }.build()

        return _Switch!!
    }

@Suppress("ObjectPropertyName")
private var _Switch: ImageVector? = null
