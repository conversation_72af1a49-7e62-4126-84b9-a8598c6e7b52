package com.buque.wakoo.ui.screens.chatgroup.member


import android.icu.text.DecimalFormat
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.UserDecorations
import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OnlineMember(
    @SerialName("is_online")
    val isOnline: Boolean = false,
    @SerialName("member_id")
    val memberId: Int = 0,
    @SerialName("role")
    val role: Int = 0,
    @SerialName("total_active_point")
    val totalActivePoint: Int = 0,
    @SerialName("week_active_point")
    val weekPoint: Int = 0,
    val user: UserResponse
) {
    val basicUser: BasicUser by lazy { BasicUser.fromResponse(user) }
    val userDecor: UserDecorations by lazy { UserDecorations.fromResponse(user) }
    val weekValue = weekPoint.formatToW()
    val totalValue = totalActivePoint.formatToW()
}

fun Number.formatToW(): String {
    return if (this.toLong() >= 10_000) {
        val df = DecimalFormat("#.##w") // 定义格式
        df.format(this.toDouble() / 10_000)
    } else {
        this.toString()
    }
}


@Serializable
data class OnlineMemberContainer(
    @SerialName("total_count")
    val totalCount: Int,
    @SerialName("online_count")
    val onlineCount: Int,
    val fellows: List<OnlineMember>
)