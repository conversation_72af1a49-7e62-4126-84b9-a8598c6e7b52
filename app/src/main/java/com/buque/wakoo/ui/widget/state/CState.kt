package com.buque.wakoo.ui.widget.state

import androidx.compose.runtime.MutableState
import com.buque.wakoo.ext.isNetworkException
import com.buque.wakoo.manager.localized

/** 一个密封接口，用于描述UI的各种状态。 使用密封接口可以确保在 `when` 表达式中处理所有可能的状态，提高代码的健壮性。 */
sealed interface CState<out T> {
    /** 初始状态或空闲状态。 表示UI当前未进行任何加载操作。 */
    data object Idle : CState<Nothing>

    /**
     * 加载中状态。 当数据正在获取时，UI应显示此状态。
     * @param isRefreshing 如果为 true，表示是“下拉刷新”操作，通常此时旧数据依然可见。
     * @param throwable 如果在刷新过程中发生了错误，可以携带这个异常信息，用于非阻塞性提示（如 Toast）。
     */
    data class Loading(
        override val isRefreshing: Boolean = false,
        override val throwable: Throwable? = null,
    ) : CState<Nothing>,
        CLoading,
        CError

    /**
     * 成功状态。 表示数据已成功获取。这是最核心的状态，因为它也聚合了其他状态的能力。
     * @param T 泛型，表示成功获取的数据类型。
     * @param data 成功获取到的业务数据。
     * @param isEmpty 数据是否为空。例如，请求成功，但列表返回了0个项目。
     * @param isRefreshing 如果为 true，表示正在基于当前成功数据进行“下拉刷新”。
     * @param throwable 如果在刷新或后续操作中发生非阻塞性错误，通过此字段传递。
     */
    data class Success<T>(
        val data: T,
        override val isRefreshing: Boolean = false,
        override val throwable: Throwable? = null,
    ) : CState<T>,
        CEmpty,
        CLoading,
        CError {
        override fun isEmpty(): Boolean =
            if (data is CEmpty) {
                data.isEmpty()
            } else {
                data is Collection<*> && data.isEmpty()
            }
    }

    /**
     * 错误状态。 表示初次加载失败，没有任何可显示的数据。这是一个阻塞性的、终结性的错误状态。
     * @param throwable 具体的异常信息。
     */
    data class Error(
        override val throwable: Throwable,
    ) : CState<Nothing>,
        CError
}

/** 定义了“加载中”行为的接口。 任何可以表现为“加载中”的状态都应实现此接口。 */
interface CLoading {
    val isRefreshing: Boolean
        get() = false
}

/** 定义了“空数据”行为的接口。 成功状态可能需要判断是否为空。 */
interface CEmpty {
    fun isEmpty(): Boolean = false
}

/** 定义了“错误”行为的接口。 任何可能携带异常信息的状态都应实现此接口。 */
interface CError {
    val throwable: Throwable?
}

/** 扩展属性：判断一个错误是否是网络错误。 实际项目中可以根据需要扩展，例如判断`retrofit2.HttpException`等。 */
val CError.isNetworkError: Boolean
    // 这里我们简单地判断是否是 `IOException` 的子类，这通常涵盖了大部分网络问题。
    get() = throwable?.isNetworkException() == true

/** 是否需要请求数据。 如果状态为空闲或错误，则需要请求数据。 */
val CState<*>.shouldRequestData: Boolean
    get() = this is CState.Idle || this is CState.Error

/** 获取错误信息。 */
val CState<*>.errorOrNull: Throwable?
    get() = if (this is CError) throwable else null

/** 是否存在错误。 */
val CState<*>.hasError: Boolean
    get() = errorOrNull != null

/** 是否正在刷新。 */
val CState<*>.isRefreshing: Boolean
    get() = if (this is CLoading) isRefreshing else false

/** 是否正在刷新。 */
val CState<*>.isSuccess: Boolean
    get() = this is CState.Success

/** 获取数据。 */
val <T> CState<T>.dataOrNull: T?
    get() = if (this is CState.Success) data else null

/** 获取数据。 */
val <T> CState<T>.requireData: T
    get() = dataOrNull ?: throw IllegalStateException("数据为空".localized)

fun <T, R> CState<T>.stateWhen(
    onIdle: (() -> R)? = null,
    onSuccess: ((T) -> R)? = null,
    onError: ((Throwable?) -> R)? = null,
    onLoading: (() -> R)? = null,
    orElse: () -> R,
): R =
    when (this) {
        is CState.Error -> onError?.invoke(this.errorOrNull) ?: orElse()
        CState.Idle -> onIdle?.invoke() ?: orElse()
        is CState.Loading -> onLoading?.invoke() ?: orElse()
        is CState.Success -> onSuccess?.invoke(requireData) ?: orElse()
    }

fun <T> Result<T>.toCState(): CState<T> =
    if (this.isSuccess) {
        CState.Success(data = this.getOrNull()!!)
    } else {
        CState.Error(throwable = this.exceptionOrNull()!!)
    }

fun <T> MutableState<CState<T>>.cUpdate(function: (T) -> T) {
    value.dataOrNull?.apply {
        value = CState.Success(function(this))
    }
}
