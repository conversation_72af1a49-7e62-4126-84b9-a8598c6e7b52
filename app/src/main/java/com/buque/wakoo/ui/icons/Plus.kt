package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Plus: ImageVector
    get() {
        if (_plus != null) {
            return _plus!!
        }
        _plus =
            ImageVector
                .Builder(
                    name = "Plus",
                    defaultWidth = 16.dp,
                    defaultHeight = 16.dp,
                    viewportWidth = 16f,
                    viewportHeight = 16f,
                ).apply {
                    // 我们通过绘制一个闭合路径来创建加号的形状
                    path(
                        fill = SolidColor(Color.Black), // 颜色可以被 Icon 的 tint 覆盖
                        stroke = null,
                        strokeLineWidth = 0f,
                        strokeLineCap = StrokeCap.Butt,
                        strokeLineJoin = StrokeJoin.Miter,
                        strokeLineMiter = 4f,
                        pathFillType = PathFillType.NonZero,
                    ) {
                        // 水平矩形: y从7到9, x从2到14
                        // 垂直矩形: x从7到9, y从2到14
                        // 绘制外轮廓
                        moveTo(9f, 2f) // 移动到顶部矩形的右上角
                        verticalLineTo(7f) // 向下到中心
                        horizontalLineTo(14f) // 向右到边缘
                        verticalLineTo(9f) // 向下
                        horizontalLineTo(9f) // 向左回到中心
                        verticalLineTo(14f) // 向下到底部
                        horizontalLineTo(7f) // 向左
                        verticalLineTo(9f) // 向上回到中心
                        horizontalLineTo(2f) // 向左到边缘
                        verticalLineTo(7f) // 向上
                        horizontalLineTo(7f) // 向右回到中心
                        verticalLineTo(2f) // 向上回到顶部
                        close() // 闭合路径
                    }
                }.build()
        return _plus!!
    }

@Suppress("ktlint:standard:backing-property-naming")
// 使用 backing property 来延迟初始化，确保只在第一次访问时创建
private var _plus: ImageVector? = null
