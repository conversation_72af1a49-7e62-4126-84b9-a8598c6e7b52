package com.buque.wakoo.ui.dialog

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.DialogController
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.icons.Close
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.VipTag
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import kotlinx.coroutines.delay

@Composable
fun DialogScope.EnterPrivateRoomNotificationDialogContent(
    roomId: String,
    user: User,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        Spacer(
            modifier =
                Modifier
                    .padding(top = 24.dp)
                    .matchParentSize()
                    .background(WakooWhite, RoundedCornerShape(24.dp))
                    .paint(
                        painter = painterResource(R.drawable.bg_private_room_dialog),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.FillWidth,
                        alignment = Alignment.TopCenter,
                    ),
        )

        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            AvatarNetworkImage(
                user = user,
                border = BorderStroke(2.dp, WakooWhite),
                size = 80.dp,
            )

            SizeHeight(30.dp)

            Text(
                text = "%s进入了情侣小屋，快去看看吧！".localizedFormat(user.name),
                color = WakooText,
                fontSize = 15.sp,
                minLines = 2,
                textAlign = TextAlign.Center,
            )

            SizeHeight(30.dp)

            SolidButton(
                text = "立即进入".localized,
                onClick = {
                    dismiss()
                    LiveRoomManager.joinRoom(roomId = roomId, isPrivateRoom = true)
                },
                backgroundColor = Color(0xFFFE669E),
                textColor = WakooWhite,
                fontSize = 16.sp,
                height = 36.dp,
                minWidth = 160.dp,
            )

            SizeHeight(20.dp)
        }
    }
}

@Preview
@Composable
private fun PreviewEnterPrivateRoomNotificationDialogContent() {
    DialogController.preview.apply {
        Box(
            modifier =
                Modifier
                    .background(Color.Blue)
                    .padding(20.dp),
        ) {
            EnterPrivateRoomNotificationDialogContent(
                roomId = "1",
                user = BasicUser.sampleBoy,
            )
        }
    }
}

@Composable
fun DialogScope.PrivateRoomJoinTip(
    content: String,
    button: String,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .background(
                    brush =
                        Brush.verticalGradient(
                            0f to Color(0xFFFFEEF5),
                            0.5f to WakooWhite,
                        ),
                    shape = RoundedCornerShape(12.dp),
                ).padding(horizontal = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        SizeHeight(20.dp)

        Text(
            text = content,
            color = Color(0xFFFE669E),
            fontSize = 14.sp,
            minLines = 2,
            textAlign = TextAlign.Center,
        )

        SizeHeight(16.dp)

        Box {
            SolidButton(
                text = button,
                onClick = {
                    dismiss()
                    LocalAppNavController.useRoot?.push(Route.Member)
                },
                modifier = Modifier.padding(top = 10.dp),
                backgroundColor = Color(0xFFFE669E),
                textColor = WakooWhite,
                fontSize = 16.sp,
                height = 36.dp,
                minWidth = 160.dp,
            )
            VipTag(modifier = Modifier.align(Alignment.TopEnd))
        }

        SizeHeight(16.dp)
    }
}

@Preview
@Composable
private fun PreviewPrivateRoomJoinTip() {
    DialogController.preview.apply {
        Box(
            modifier =
                Modifier
                    .background(Color.Yellow)
                    .padding(20.dp),
        ) {
            PrivateRoomJoinTip(
                content = "您的免费时长已不足，\n可添加好友享有无限通话时长。",
                button = "加好友",
            )
        }
    }
}

@Composable
fun DialogScope.InviteToPrivateRoomDialogContent(
    roomId: String,
    user: User,
    hint: String,
    isFree: Boolean,
) {
    val context = LocalContext.current
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .background(WakooWhite, RoundedCornerShape(24.dp))
                .paint(
                    painter = painterResource(R.drawable.bg_private_room_dialog),
                    contentScale = ContentScale.FillWidth,
                    alignment = Alignment.TopCenter,
                ),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Box(
            modifier =
                Modifier
                    .padding(top = 15.dp)
                    .fillMaxWidth(),
        ) {
            Image(
                imageVector = WakooIcons.Close,
                contentDescription = "close",
                modifier =
                    Modifier
                        .statusBarsPadding()
                        .padding(end = 10.dp)
                        .padding(8.dp)
                        .clickable(onClick = {
                            dismiss()
                        })
                        .size(24.dp)
                        .align(Alignment.TopEnd),
                colorFilter = ColorFilter.tint(WakooGrayText),
            )

            Image(
                painter = painterResource(id = R.drawable.ic_welcome_new_user_title),
                contentDescription = null,
                modifier =
                    Modifier
                        .padding(top = 15.dp)
                        .align(Alignment.Center)
                        .height(24.dp),
                contentScale = ContentScale.FillHeight,
            )
        }

        SizeHeight(16.dp)

        AvatarNetworkImage(
            user = user,
            size = 80.dp,
        )

        Text(
            text = user.name,
            color = WakooText,
            fontSize = 18.sp,
        )

        SizeHeight(5.dp)

        val userDesc =
            remember(user, context) {
                buildString {
                    append(user.age)
                    append("岁".localized)

                    if (user.height > 0) {
                        append("丨")
                        append(user.formatHeight)
                    }
                }
            }

        SizeHeight(3.dp)

        Text(
            text = userDesc,
            color = WakooGrayText,
            fontSize = 14.sp,
        )

        var downCounter by remember {
            mutableIntStateOf(0)
        }

        var launched by remember {
            mutableStateOf(false)
        }

        LaunchedEffect(Unit) {
            downCounter = 5
            while (downCounter > 0) {
                delay(1000)
                downCounter--
            }
            if (!launched) {
                launched = true
                dismiss()
                LiveRoomManager.joinRoom(roomId = roomId, isPrivateRoom = true)
            }
        }

        Text(
            text = hint,
            modifier = Modifier.padding(top = 15.dp, start = 24.dp, end = 24.dp),
            fontSize = 14.sp,
            color = WakooGrayText,
            textAlign = TextAlign.Center,
        )

        Box(
            modifier = Modifier.padding(top = 20.dp, start = 20.dp, end = 20.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .padding(top = 10.dp)
                        .fillMaxWidth()
                        .height(44.dp)
                        .clip(CircleShape)
                        .background(Color(0xFFFE669E), CircleShape)
                        .clickable(enabled = downCounter > 0) {
                            launched = true
                            dismiss()
                            LiveRoomManager.joinRoom(roomId = roomId, isPrivateRoom = true)
                        },
                contentAlignment = Alignment.Center,
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Text(
                        text = "和TA聊聊".localized,
                        color = Color.White,
                        fontSize = 16.sp,
                        lineHeight = 16.sp,
                    )
                    Text(
                        text = "(${downCounter}s)",
                        color = Color.White,
                        fontSize = 16.sp,
                        lineHeight = 16.sp,
                    )
                }
            }

            if (isFree) {
                Box(
                    modifier =
                        Modifier
                            .align(Alignment.TopEnd)
                            .height(20.dp)
                            .background(Color(0xFFFEEA66), RoundedCornerShape(4.dp))
                            .padding(horizontal = 8.dp),
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = "免费".localized,
                        style = MaterialTheme.typography.labelLarge,
                        color = Color(0xFF7C461D),
                    )
                }
            }
        }

        SizeHeight(20.dp)
    }
}

@Preview
@Composable
private fun PreviewInviteToPrivateRoomDialogContent() {
    DialogController.preview.apply {
        Box(
            modifier =
                Modifier
                    .background(Color.Red)
                    .padding(20.dp),
        ) {
            InviteToPrivateRoomDialogContent(
                roomId = "1",
                user = BasicUser.sampleBoy,
                hint = "她正在找人语音连麦聊天...",
                isFree = true,
            )
        }
    }
}
