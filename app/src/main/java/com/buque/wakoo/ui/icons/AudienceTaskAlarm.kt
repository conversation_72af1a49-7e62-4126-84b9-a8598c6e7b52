package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.AudienceTaskAlarm: ImageVector
    get() {
        if (_AudienceTaskAlarm != null) {
            return _AudienceTaskAlarm!!
        }
        _AudienceTaskAlarm = ImageVector.Builder(
            name = "AudienceTaskAlarm",
            defaultWidth = 13.dp,
            defaultHeight = 13.dp,
            viewportWidth = 13f,
            viewportHeight = 13f
        ).apply {
            path(fill = SolidColor(Color(0xFF047767))) {
                moveTo(6.5f, 11.917f)
                curveTo(5.86f, 11.917f, 5.226f, 11.791f, 4.634f, 11.546f)
                curveTo(4.043f, 11.301f, 3.506f, 10.942f, 3.053f, 10.489f)
                curveTo(2.6f, 10.036f, 2.241f, 9.499f, 1.996f, 8.907f)
                curveTo(1.751f, 8.316f, 1.625f, 7.682f, 1.625f, 7.042f)
                curveTo(1.625f, 6.402f, 1.751f, 5.768f, 1.996f, 5.176f)
                curveTo(2.241f, 4.585f, 2.6f, 4.047f, 3.053f, 3.595f)
                curveTo(3.506f, 3.142f, 4.043f, 2.783f, 4.634f, 2.538f)
                curveTo(5.226f, 2.293f, 5.86f, 2.167f, 6.5f, 2.167f)
                curveTo(7.793f, 2.167f, 9.033f, 2.68f, 9.947f, 3.595f)
                curveTo(10.861f, 4.509f, 11.375f, 5.749f, 11.375f, 7.042f)
                curveTo(11.375f, 8.335f, 10.861f, 9.575f, 9.947f, 10.489f)
                curveTo(9.033f, 11.403f, 7.793f, 11.917f, 6.5f, 11.917f)
                close()
                moveTo(6.5f, 10.833f)
                curveTo(6.998f, 10.833f, 7.491f, 10.735f, 7.951f, 10.545f)
                curveTo(8.411f, 10.354f, 8.829f, 10.075f, 9.181f, 9.723f)
                curveTo(9.533f, 9.371f, 9.812f, 8.953f, 10.003f, 8.493f)
                curveTo(10.194f, 8.033f, 10.292f, 7.54f, 10.292f, 7.042f)
                curveTo(10.292f, 6.544f, 10.194f, 6.051f, 10.003f, 5.591f)
                curveTo(9.812f, 5.131f, 9.533f, 4.713f, 9.181f, 4.361f)
                curveTo(8.829f, 4.009f, 8.411f, 3.729f, 7.951f, 3.539f)
                curveTo(7.491f, 3.348f, 6.998f, 3.25f, 6.5f, 3.25f)
                curveTo(5.494f, 3.25f, 4.53f, 3.65f, 3.819f, 4.361f)
                curveTo(3.108f, 5.072f, 2.708f, 6.036f, 2.708f, 7.042f)
                curveTo(2.708f, 8.047f, 3.108f, 9.012f, 3.819f, 9.723f)
                curveTo(4.53f, 10.434f, 5.494f, 10.833f, 6.5f, 10.833f)
                close()
                moveTo(7.042f, 7.042f)
                horizontalLineTo(8.667f)
                verticalLineTo(8.125f)
                horizontalLineTo(5.958f)
                verticalLineTo(4.333f)
                horizontalLineTo(7.042f)
                verticalLineTo(7.042f)
                close()
                moveTo(0.946f, 3.403f)
                lineTo(2.861f, 1.488f)
                lineTo(3.628f, 2.254f)
                lineTo(1.712f, 4.169f)
                lineTo(0.946f, 3.403f)
                close()
                moveTo(10.138f, 1.488f)
                lineTo(12.054f, 3.403f)
                lineTo(11.288f, 4.169f)
                lineTo(9.372f, 2.254f)
                lineTo(10.139f, 1.488f)
                horizontalLineTo(10.138f)
                close()
            }
        }.build()

        return _AudienceTaskAlarm!!
    }

@Suppress("ObjectPropertyName")
private var _AudienceTaskAlarm: ImageVector? = null
