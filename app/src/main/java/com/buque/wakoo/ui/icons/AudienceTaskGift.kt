package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.AudienceTaskGift: ImageVector
    get() {
        if (_AudienceTaskGift != null) {
            return _AudienceTaskGift!!
        }
        _AudienceTaskGift = ImageVector.Builder(
            name = "AudienceTaskGift",
            defaultWidth = 13.dp,
            defaultHeight = 13.dp,
            viewportWidth = 13f,
            viewportHeight = 13f
        ).apply {
            path(fill = SolidColor(Color(0xFF047767))) {
                moveTo(7.854f, 1.083f)
                curveTo(8.174f, 1.083f, 8.489f, 1.164f, 8.769f, 1.318f)
                curveTo(9.05f, 1.473f, 9.286f, 1.695f, 9.457f, 1.966f)
                curveTo(9.628f, 2.236f, 9.728f, 2.546f, 9.747f, 2.865f)
                curveTo(9.767f, 3.184f, 9.705f, 3.503f, 9.568f, 3.792f)
                lineTo(11.375f, 3.792f)
                curveTo(11.519f, 3.792f, 11.657f, 3.849f, 11.758f, 3.95f)
                curveTo(11.86f, 4.052f, 11.917f, 4.19f, 11.917f, 4.333f)
                verticalLineTo(6.5f)
                curveTo(11.917f, 6.644f, 11.86f, 6.781f, 11.758f, 6.883f)
                curveTo(11.657f, 6.985f, 11.519f, 7.042f, 11.375f, 7.042f)
                horizontalLineTo(10.833f)
                verticalLineTo(11.375f)
                curveTo(10.833f, 11.519f, 10.776f, 11.656f, 10.675f, 11.758f)
                curveTo(10.573f, 11.86f, 10.436f, 11.917f, 10.292f, 11.917f)
                horizontalLineTo(2.708f)
                curveTo(2.565f, 11.917f, 2.427f, 11.86f, 2.325f, 11.758f)
                curveTo(2.224f, 11.656f, 2.167f, 11.519f, 2.167f, 11.375f)
                verticalLineTo(7.042f)
                horizontalLineTo(1.625f)
                curveTo(1.482f, 7.042f, 1.344f, 6.985f, 1.242f, 6.883f)
                curveTo(1.141f, 6.781f, 1.084f, 6.644f, 1.084f, 6.5f)
                verticalLineTo(4.333f)
                curveTo(1.084f, 4.19f, 1.141f, 4.052f, 1.242f, 3.95f)
                curveTo(1.344f, 3.849f, 1.482f, 3.792f, 1.625f, 3.792f)
                lineTo(3.433f, 3.792f)
                curveTo(3.24f, 3.387f, 3.197f, 2.926f, 3.312f, 2.492f)
                curveTo(3.427f, 2.058f, 3.692f, 1.679f, 4.06f, 1.422f)
                curveTo(4.428f, 1.166f, 4.876f, 1.048f, 5.323f, 1.09f)
                curveTo(5.77f, 1.132f, 6.187f, 1.332f, 6.501f, 1.653f)
                curveTo(6.677f, 1.472f, 6.887f, 1.329f, 7.12f, 1.231f)
                curveTo(7.352f, 1.133f, 7.602f, 1.083f, 7.854f, 1.083f)
                close()
                moveTo(9.75f, 7.042f)
                horizontalLineTo(3.25f)
                verticalLineTo(10.833f)
                horizontalLineTo(9.75f)
                verticalLineTo(7.042f)
                close()
                moveTo(10.833f, 4.875f)
                horizontalLineTo(2.167f)
                verticalLineTo(5.958f)
                horizontalLineTo(10.833f)
                verticalLineTo(4.875f)
                close()
                moveTo(5.146f, 2.167f)
                curveTo(4.937f, 2.167f, 4.737f, 2.247f, 4.586f, 2.391f)
                curveTo(4.435f, 2.535f, 4.345f, 2.732f, 4.335f, 2.94f)
                curveTo(4.325f, 3.149f, 4.395f, 3.353f, 4.532f, 3.511f)
                curveTo(4.668f, 3.669f, 4.86f, 3.768f, 5.068f, 3.788f)
                lineTo(5.146f, 3.792f)
                horizontalLineTo(5.958f)
                verticalLineTo(2.979f)
                curveTo(5.958f, 2.777f, 5.883f, 2.583f, 5.747f, 2.433f)
                curveTo(5.612f, 2.283f, 5.425f, 2.19f, 5.224f, 2.171f)
                lineTo(5.146f, 2.167f)
                close()
                moveTo(7.854f, 2.167f)
                lineTo(7.776f, 2.171f)
                curveTo(7.589f, 2.189f, 7.413f, 2.271f, 7.28f, 2.405f)
                curveTo(7.146f, 2.538f, 7.063f, 2.713f, 7.045f, 2.901f)
                lineTo(7.042f, 2.979f)
                verticalLineTo(3.792f)
                horizontalLineTo(7.854f)
                lineTo(7.932f, 3.788f)
                curveTo(8.133f, 3.768f, 8.32f, 3.675f, 8.456f, 3.525f)
                curveTo(8.591f, 3.376f, 8.666f, 3.181f, 8.666f, 2.979f)
                curveTo(8.666f, 2.777f, 8.591f, 2.583f, 8.456f, 2.433f)
                curveTo(8.32f, 2.284f, 8.133f, 2.19f, 7.932f, 2.171f)
                lineTo(7.854f, 2.167f)
                close()
            }
        }.build()

        return _AudienceTaskGift!!
    }

@Suppress("ObjectPropertyName")
private var _AudienceTaskGift: ImageVector? = null
