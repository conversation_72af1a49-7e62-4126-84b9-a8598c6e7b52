package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.Filter: ImageVector
    get() {
        if (_Filter != null) {
            return _Filter!!
        }
        _Filter = ImageVector.Builder(
            name = "Filter",
            defaultWidth = 24.dp,
            defaultHeight = 24.dp,
            viewportWidth = 24f,
            viewportHeight = 24f
        ).apply {
            group(
                clipPathData = PathData {
                    moveTo(0f, 0f)
                    horizontalLineToRelative(24f)
                    verticalLineToRelative(24f)
                    horizontalLineToRelative(-24f)
                    close()
                }
            ) {
                path(fill = SolidColor(Color(0xFF111111))) {
                    moveTo(20f, 3f)
                    verticalLineTo(5f)
                    horizontalLineTo(19f)
                    lineTo(14f, 12.5f)
                    verticalLineTo(21f)
                    horizontalLineTo(8f)
                    verticalLineTo(12.5f)
                    lineTo(3f, 5f)
                    horizontalLineTo(2f)
                    verticalLineTo(3f)
                    horizontalLineTo(20f)
                    close()
                    moveTo(5.404f, 5f)
                    lineTo(10f, 11.894f)
                    verticalLineTo(19f)
                    horizontalLineTo(12f)
                    verticalLineTo(11.894f)
                    lineTo(16.596f, 5f)
                    horizontalLineTo(5.404f)
                    close()
                }
                path(
                    stroke = SolidColor(Color(0xFF000000)),
                    strokeLineWidth = 2f
                ) {
                    moveTo(16.5f, 12f)
                    horizontalLineTo(22.5f)
                }
                path(
                    stroke = SolidColor(Color(0xFF000000)),
                    strokeLineWidth = 2f
                ) {
                    moveTo(16.5f, 16f)
                    horizontalLineTo(22.5f)
                }
                path(
                    stroke = SolidColor(Color(0xFF000000)),
                    strokeLineWidth = 2f
                ) {
                    moveTo(16.5f, 20f)
                    horizontalLineTo(22.5f)
                }
            }
        }.build()

        return _Filter!!
    }

@Suppress("ObjectPropertyName")
private var _Filter: ImageVector? = null
