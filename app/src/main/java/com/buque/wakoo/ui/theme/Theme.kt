package com.buque.wakoo.ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.unit.dp
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.bean.Environment
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.LoggedInHost
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.rememberRootNavController
import com.buque.wakoo.ui.dialog.loading.ProvideLoadingManager
import com.buque.wakoo.ui.widget.drag.LocalFloatingLayoutManagerState
import com.buque.wakoo.ui.widget.drag.rememberFloatingLayoutManagerState

// --- 浅色主题配色 (Light Theme) ---
private val LightColorScheme =
    lightColorScheme(
        primary = WakooGreen, // 主要交互颜色 (按钮、高亮)
        onPrimary = WakooBlack, // 在主要颜色之上的内容颜色 (如绿色按钮上的文字)
        secondary = WakooDarkGray, // 次要交互颜色
        onSecondary = WakooWhite, // 在次要颜色之上的内容颜色
        background = WakooWhite, // App 主要背景色
        onBackground = WakooBlack, // 背景之上的内容颜色 (主要文字)
        surface = WakooWhite, // 卡片、对话框等组件的表面颜色
        onSurface = WakooBlack, // 表面之上的内容颜色 (卡片内的文字)
        onSurfaceVariant = WakooGrayText, // 表面上变化的内容颜色 (次要文字、提示文字)
        error = WakooRed, // 错误颜色
        onError = WakooWhite, // 错误颜色之上的内容颜色
        outline = WakooLightGrayBg, // 轮廓颜色 (如输入框描边)
    )

// --- 全新生成的暗色主题配色 (Dark Theme) ---
private val DarkColorScheme =
    darkColorScheme(
        // 主色调 WakooGreen 调整为更柔和、适合暗色背景的版本
        primary = Color(0xFF45D982),
        // 在主色上的文字依然保持黑色，延续品牌特色
        onPrimary = WakooBlack,
        // 次要交互颜色
        secondary = Color(0xFF353535),
        onSecondary = WakooWhite,
        // 背景使用深灰色而不是纯黑，视觉更柔和
        background = Color(0xFF121212),
        onBackground = WakooWhite,
        // 卡片、输入框等表面颜色，比背景稍亮，以体现层次感
        surface = Color(0xFF1E1E1E),
        onSurface = WakooWhite,
        // 次要文字颜色，使用浅灰色
        onSurfaceVariant = Color(0xFFB0B0B0),
        // 错误颜色也需要调整以适应暗色背景
        error = Color(0xFFFFB4AB),
        onError = Color(0xFF690005),
        // 轮廓线颜色
        outline = Color(0xFF444444),
    )

private var previewInitialized = false

@Composable
fun WakooTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = !LocalInspectionMode.current,
    content: @Composable () -> Unit,
) {
    // 目前只支持浅色模式
//    val colorScheme =
//        when {
//            dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
//                val context = LocalContext.current
//                if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
//            }
//
//            darkTheme -> DarkColorScheme
//            else -> LightColorScheme
//        }
    val colorScheme = LightColorScheme

    if (LocalInspectionMode.current) {
        val context = LocalContext.current
        if (!previewInitialized) {
            WakooApplication.attach(context)
            previewInitialized = true
            EnvironmentManager.previewInitialize()
        }

        CompositionLocalProvider(
            LocalFloatingLayoutManagerState provides rememberFloatingLayoutManagerState(collisionSpacing = 10.dp),
        ) {
            MaterialTheme(
                colorScheme = colorScheme,
                typography = Typography,
                content = {
                    ProvideLoadingManager {
                        val controller =
                            rememberRootNavController(loginNavKey = Route.Login, isLoggedIn = true, LoggedInHost)
                        LocalAppNavController.ProvideController(controller, {
                            LocalSelfUserProvider.Provider(content = content)
                        })
                    }
                },
            )
        }
    } else {
        MaterialTheme(
            colorScheme = colorScheme,
            typography = Typography,
            content = content,
        )
    }
}
