package com.buque.wakoo.ui.screens.liveroom.panel

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.IconLabel
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LiveRoomRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.BottomPanelWidgetDialog
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.dialog.BottomPanelScaffold
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.SimpleDoubleActionDialog
import com.buque.wakoo.ui.icons.Background
import com.buque.wakoo.ui.icons.BlockEffect
import com.buque.wakoo.ui.icons.Collapse
import com.buque.wakoo.ui.icons.Exit
import com.buque.wakoo.ui.icons.Lock
import com.buque.wakoo.ui.icons.MicMode
import com.buque.wakoo.ui.icons.Report
import com.buque.wakoo.ui.icons.RoomMode
import com.buque.wakoo.ui.icons.RoomSettings
import com.buque.wakoo.ui.icons.Share
import com.buque.wakoo.ui.icons.Trash
import com.buque.wakoo.ui.icons.UnBlockEffect
import com.buque.wakoo.ui.icons.UnLock
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.RoomRole
import com.buque.wakoo.ui.screens.liveroom.SetRoomPasswordDialog
import com.buque.wakoo.ui.widget.SizeHeight
import kotlinx.serialization.Serializable

// 房间管理
private const val MENU_TYPE_ROOM_MANAGE = 1

// 房间模式
private const val MENU_TYPE_ROOM_MODE = 2

// 上麦模式
private const val MENU_TYPE_MIC_MODE = 3

// 清空心动值
private const val MENU_TYPE_CLEAR_SCORE = 4

// 收起房间
private const val MENU_TYPE_COLLAPSE_ROOM = 5

// 分享房间
private const val MENU_TYPE_SHARE_ROOM = 6

// 举报房间
private const val MENU_TYPE_REPORT_ROOM = 7

// 退出房间
private const val MENU_TYPE_EXIT_ROOM = 8

// 屏蔽特效
private const val MENU_TYPE_BLOCK_GIFT_EFFECT = 9

// 房间背景
private const val MENU_TYPE_BACKGROUND_ROOM = 10

// 房间锁定
private const val MENU_TYPE_LOCK_ROOM = 11

@Composable
fun DialogScope.LiveRoomSettingPanel(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    val rootNavController = LocalAppNavController.root
    val navController = LocalAppNavController.current
    BottomPanelScaffold(
        title = "房间设置".localized,
        useClose = true,
        modifier = modifier,
    ) {
        val selfRole by roomInfoState.rememberRoomRoleState(LocalSelfUserProvider.currentId)
        val menuItems by remember {
            derivedStateOf {
                buildList {
                    if (roomInfoState.basicInfo.roomMode != LiveRoomMode.Private) {
                        if (selfRole != RoomRole.Member) {
                            add(IconLabel(MENU_TYPE_ROOM_MANAGE, WakooIcons.RoomSettings, "房间管理".localized))
                            add(IconLabel(MENU_TYPE_ROOM_MODE, WakooIcons.RoomMode, "房间模式".localized))
                            add(IconLabel(MENU_TYPE_MIC_MODE, WakooIcons.MicMode, "上麦模式".localized))
                            if (roomInfoState.basicInfo.roomMode != LiveRoomMode.Normal) {
                                add(IconLabel(MENU_TYPE_CLEAR_SCORE, WakooIcons.Trash, "清空心动值".localized))
                            }
                        }
                    }

                    add(IconLabel(MENU_TYPE_COLLAPSE_ROOM, WakooIcons.Collapse, "收起房间".localized))
                    if (roomInfoState.basicInfo.roomMode != LiveRoomMode.Private && selfRole != RoomRole.Member) {
                        add(IconLabel(MENU_TYPE_BACKGROUND_ROOM, WakooIcons.Background, "房间背景".localized))
                        if (roomInfoState.basicInfo.locked) {
                            add(IconLabel(MENU_TYPE_LOCK_ROOM, WakooIcons.Lock, "取消锁房".localized))
                        } else {
                            add(IconLabel(MENU_TYPE_LOCK_ROOM, WakooIcons.UnLock, "锁定房间".localized))
                        }
                    }

                    if (roomInfoState.basicInfo.roomMode != LiveRoomMode.Private) {
                        add(IconLabel(MENU_TYPE_SHARE_ROOM, WakooIcons.Share, "分享房间".localized))
                        if (selfRole == RoomRole.Member) {
                            add(IconLabel(MENU_TYPE_REPORT_ROOM, WakooIcons.Report, "举报房间".localized))
                        }
                    }

                    add(IconLabel(MENU_TYPE_EXIT_ROOM, WakooIcons.Exit, "退出房间".localized))
                    if (LiveRoomManager.blockGiftEffectEnable) {
                        add(IconLabel(MENU_TYPE_BLOCK_GIFT_EFFECT, WakooIcons.UnBlockEffect, "打开礼物特效".localized))
                    } else {
                        add(IconLabel(MENU_TYPE_BLOCK_GIFT_EFFECT, WakooIcons.BlockEffect, "关闭礼物特效".localized))
                    }
                }
            }
        }

        FlowRow(
            modifier = Modifier.fillMaxWidth(),
            maxItemsInEachRow = 4,
            horizontalArrangement = Arrangement.SpaceAround,
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            menuItems.forEach {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier =
                        Modifier.noEffectClick {
                            when (it.id) {
                                MENU_TYPE_ROOM_MANAGE -> {
                                    dismiss()
                                    roomInfoState.sendEvent(
                                        RoomEvent.PanelDialog { roomInfoState ->
                                            LiveRoomManagePanel(roomInfoState) {
                                                roomInfoState.sendEvent(
                                                    RoomEvent.PanelDialog { roomInfoState ->
                                                        LiveRoomSettingPanel(roomInfoState)
                                                    },
                                                )
                                            }
                                        },
                                    )
                                }

                                MENU_TYPE_ROOM_MODE -> {
                                    dismiss()
                                    roomInfoState.sendEvent(
                                        RoomEvent.PanelDialog { roomInfoState ->
                                            LiveRoomModeSettingsPanel(roomInfoState) {
                                                roomInfoState.sendEvent(
                                                    RoomEvent.PanelDialog { roomInfoState ->
                                                        LiveRoomSettingPanel(roomInfoState)
                                                    },
                                                )
                                            }
                                        },
                                    )
                                }

                                MENU_TYPE_MIC_MODE -> {
                                    dismiss()
                                    roomInfoState.sendEvent(
                                        RoomEvent.PanelDialog { roomInfoState ->
                                            LiveMicModeSettingsPanel(roomInfoState) {
                                                roomInfoState.sendEvent(
                                                    RoomEvent.PanelDialog { roomInfoState ->
                                                        LiveRoomSettingPanel(roomInfoState)
                                                    },
                                                )
                                            }
                                        },
                                    )
                                }

                                MENU_TYPE_CLEAR_SCORE -> {
                                    roomInfoState.sendEvent(
                                        RoomEvent.CustomDialog {
                                            SimpleDoubleActionDialog(
                                                content = "确定要将麦位上所有心动值归0吗？".localized,
                                                cancelButtonConfig = DialogButtonStyles.Primary.copy(text = "确认".localized),
                                                confirmButtonConfig = DialogButtonStyles.Secondary.copy(text = "取消".localized),
                                                onCancel = {
                                                    roomInfoState.sendEvent(RoomEvent.ClearLoveValue)
                                                    dismiss()
                                                },
                                                onConfirm = {
                                                    dismiss()
                                                },
                                            )
                                        },
                                    )
                                }

                                MENU_TYPE_COLLAPSE_ROOM -> {
                                    dismiss()
                                    roomInfoState.sendEvent(RoomEvent.CollapseRoom)
                                }

                                MENU_TYPE_SHARE_ROOM -> {
                                    dismiss()
                                    rootNavController.push(Route.SelectUser(2, roomInfoState.id))
                                }

                                MENU_TYPE_REPORT_ROOM -> {
                                    dismiss()
                                    rootNavController.push(Route.Report(2, roomInfoState.id))
                                }

                                MENU_TYPE_EXIT_ROOM -> {
                                    dismiss()
                                    roomInfoState.sendEvent(RoomEvent.ExitRoom)
                                }

                                MENU_TYPE_BLOCK_GIFT_EFFECT -> {
                                    LiveRoomManager.toggleBlockGiftEffect()
                                }

                                MENU_TYPE_BACKGROUND_ROOM -> {
                                    navController.push(LiveRoomRoute.BackgroundSet)
                                }

                                MENU_TYPE_LOCK_ROOM -> {
                                    if (roomInfoState.basicInfo.locked) {
                                        roomInfoState.sendEvent(
                                            RoomEvent.CustomDialog {
                                                SimpleDoubleActionDialog(
                                                    content = "确定要要取消锁房吗？".localized,
                                                    cancelButtonConfig = DialogButtonStyles.Primary.copy(text = "确认".localized),
                                                    confirmButtonConfig = DialogButtonStyles.Secondary.copy(text = "取消".localized),
                                                    onCancel = {
                                                        roomInfoState.sendEvent(RoomEvent.SetRoomPassword(id, null))
                                                    },
                                                    onConfirm = {
                                                        dismiss()
                                                    },
                                                )
                                            },
                                        )
                                    } else {
                                        roomInfoState.sendEvent(RoomEvent.DestinationDialog(SetRoomPasswordDialog()))
                                    }
                                }
                            }
                        },
                ) {
                    Box(
                        modifier =
                            Modifier
                                .size(56.dp)
                                .background(Color.White, CircleShape),
                        contentAlignment = Alignment.Center,
                    ) {
                        Icon(
                            imageVector = it.icon,
                            contentDescription = null,
                            modifier = Modifier.size(24.dp),
                        )
                    }
                    SizeHeight(12.dp)
                    BasicText(
                        it.label,
                        modifier = Modifier.width(56.dp),
                        style =
                            MaterialTheme.typography.labelLarge.copy(
                                color = Color(0xFF111111),
                                textAlign = TextAlign.Center,
                            ),
                        maxLines = 1,
                        autoSize =
                            TextAutoSize.StepBased(
                                minFontSize = 9.sp,
                                maxFontSize = MaterialTheme.typography.labelLarge.fontSize,
                            ),
                        overflow = TextOverflow.Ellipsis,
                    )
                }
            }

            val rem = menuItems.size.rem(4)
            if (rem > 0) {
                repeat(4 - rem) {
                    Box(modifier = Modifier.size(56.dp))
                }
            }
        }
    }
}

@Serializable
class LiveRoomSettingPanelDialog : BottomPanelWidgetDialog<LiveRoomInfoState>() {
    @Composable
    override fun DialogScope.Content(roomInfoState: LiveRoomInfoState) {
        LiveRoomSettingPanel(roomInfoState)
    }
}
