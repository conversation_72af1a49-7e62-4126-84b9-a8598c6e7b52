package com.buque.wakoo.ui.widget.gift

import android.R.attr.contentDescription
import android.util.Log
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.lerp
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntRect
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupPositionProvider
import androidx.compose.ui.window.PopupProperties
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.bean.BlessEntity
import com.buque.wakoo.bean.GiftWall
import com.buque.wakoo.bean.GiftWallItemDetail
import com.buque.wakoo.bean.GiftWallSummaryBean
import com.buque.wakoo.bean.IGift
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.Close
import com.buque.wakoo.ui.icons.GiftWallItemBG
import com.buque.wakoo.ui.icons.IconAdd
import com.buque.wakoo.ui.icons.IconDrease
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.RecyclePoolUtils
import com.buque.wakoo.utils.eventBus.EventBusEffect
import com.buque.wakoo.viewmodel.GiftWallDetailViewModel
import com.buque.wakoo.viewmodel.GiftWallViewModel
import com.buque.wakoo.viewmodel.SendGiftFromWallEvent
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.roundToInt
import kotlin.random.Random

//region bean

sealed interface GiftWallItem {
    val type: Int

    val span: Int
}

class SpaceItem(
    val height: Int,
    override val span: Int,
) : GiftWallItem {
    override val type: Int
        get() = 0
}

data class TopRadiusItem(
    override val span: Int,
) : GiftWallItem {
    override val type: Int
        get() = 1
}

data class BottomRadiusItem(
    override val span: Int,
) : GiftWallItem {
    override val type: Int
        get() = 2
}

class SpanItem(
    override val span: Int,
    val isStart: Boolean = false,
    val isFirstLine: Boolean = false,
) : GiftWallItem {
    override val type: Int
        get() = 3
}

class CategoryTitleItem(
    val title: String,
    val lightCount: Int = 0,
    val totalCount: Int = 0,
    val seriesId: Int = -1,
    override val span: Int,
) : GiftWallItem {
    override val type: Int
        get() = 4

    fun updateLightCount(newCount: Int): CategoryTitleItem = CategoryTitleItem(title, lightCount + newCount, totalCount, seriesId, span)
}

data class GiftItem(
    val gift: GiftWall.GiftWrapper.Gift,
    val count: Int,
    val direction: Int = -1,
    val seriesId: Int = -1,
    val isFirstLine: Boolean = false,
) : GiftWallItem {
    fun updateLightCount(newCount: Int): GiftItem = GiftItem(gift, count + newCount, direction, seriesId)

    override val type: Int
        get() = 5

    override val span: Int
        get() = 2
}

data class CategoryGiftWall(
    val category: String,
    val items: List<GiftWallItem>,
    val lightCount: Int = 0,
    val totalCount: Int = 0,
)

data class NewCategoryGiftWall(
    val category: String,
    val items: List<SeriesItem>,
    val lightCount: Int = 0,
    val totalCount: Int = 0,
) {
    data class SeriesItem(
        val seriesName: String,
        val items: List<GiftItem>,
        val lightCount: Int = 0,
        val totalCount: Int = 0,
    )
}
//endregion

//region 个人中心和个人资料卡的礼物墙
@Composable
fun UserGiftWallTab(
    sceneId: String,
    sceneType: Int? = null,
    refreshFlag: Int = 0,
    onGiftClicked: (GiftItem) -> Unit = {},
) {
    val viewModel =
        viewModel<GiftWallViewModel>(initializer = {
            GiftWallViewModel(
                sceneType, // 如果是礼物面板点进来的, 就是4私聊, 否则全是6个人主页来的
                sceneId.toInt(),
                true,
            )
        })

    val giftWallSummary by viewModel.giftWallSummary.collectAsStateWithLifecycle()

    UserGiftWallTabScaffold(giftWallSummary) {
        val state by viewModel.getState(it, 4).collectAsStateWithLifecycle()
        when (state) {
            is GiftWallViewModel.State.LoadSucceedState -> {
                (state as? GiftWallViewModel.State.LoadSucceedState)?.let {
                    val item = it.list.find { it is CategoryTitleItem } as? CategoryTitleItem
                    val withBox = !(item?.title.isNullOrBlank())

                    UserGiftWallTabContent(it.list, onGiftClicked)
                }
            }

            GiftWallViewModel.State.LoadingState -> {
                Box(
                    modifier =
                        Modifier
                            .fillMaxSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    CircularProgressIndicator()
                }
            }

            GiftWallViewModel.State.LoadFailed -> {
                Box(
                    modifier =
                        Modifier
                            .fillMaxSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    Text("加载失败".localized)
                }
            }

            else -> {
            }
        }
    }
}

@Composable
fun UserGiftWallTabScaffold(
    summary: GiftWallSummaryBean,
    contentWidget: @Composable (GiftWallSummaryBean.Tab) -> Unit = {},
) {
    Box(
        modifier =
            Modifier
                .clip(RoundedCornerShape(16.dp))
                .background(color = Color(0xfff9fff9))
                .border(1.dp, color = Color(0xffb5ffdd), RoundedCornerShape(16.dp)),
    ) {
        Spacer(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(115.dp)
                    .background(
                        brush = Brush.verticalGradient(listOf(Color(0xffecffe6), Color(0xfff9fff9))),
                    ),
        )

        val pageState =
            rememberPagerState {
                summary.tabs.size
            }
        val selectedTabIndex = pageState.currentPage
        val scope = rememberCoroutineScope()

        Column {
            TabRow(
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier.padding(horizontal = 30.dp, vertical = 15.dp),
                divider = {},
                containerColor = Color.Transparent,
                indicator = { tabPositions ->
                    if (selectedTabIndex < tabPositions.size) {
                        Image(
                            painter = painterResource(R.drawable.ic_gift_wall_tab_bg),
                            contentDescription = null,
                            modifier = Modifier.tabIndicatorOffset(tabPositions[selectedTabIndex]),
                        )
                    }
                },
            ) {
                summary.tabs.forEachIndexed { index, tab ->
                    Tab(
                        selected = selectedTabIndex == index,
                        selectedContentColor = Color(0xff54ab6f),
                        unselectedContentColor = Color(0x8054ab6f),
                        onClick = {
                            scope.launch {
                                pageState.animateScrollToPage(index)
                            }
                        },
                        content = {
                            Text(
                                text = tab.name,
                                modifier = Modifier.padding(horizontal = 18.dp),
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = if (selectedTabIndex == index) FontWeight.SemiBold else FontWeight.Normal,
                            )
                        },
                    )
                }
            }

            HorizontalPager(pageState, modifier = Modifier.weight(1f), userScrollEnabled = false) {
                contentWidget(summary.tabs[it])
            }
        }
    }
}

//endregion

@Composable
fun UserGiftWallTabContent(
    list: List<GiftWallItem>,
    onGiftClicked: (GiftItem) -> Unit = {},
) {
    val realList by remember {
        derivedStateOf {
            list.filter {
                it is GiftItem || it is CategoryTitleItem || it is SpanItem
            }
        }
    }
    LazyVerticalGrid(
        GridCells.Fixed(8),
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(horizontal = 4.dp, vertical = 14.dp),
    ) {
        itemsIndexed(realList, span = { index, item ->
            GridItemSpan(item.span)
        }) { index, it ->
            when (it) {
                is CategoryTitleItem -> {
                    if (it.title.isNotBlank()) {
                        Row(
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 6.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center,
                        ) {
                            Image(painterResource(R.drawable.ic_gift_wall_start_left), contentDescription = "")
                            Text(
                                text =
                                    buildAnnotatedString {
                                        append(it.title)
                                        append("(")
                                        append(it.lightCount.toString())
                                        append("/")
                                        append(it.totalCount.toString())
                                        append(")")
                                    },
                                color = Color(0xff54ab6f),
                                fontSize = 11.sp,
                                textAlign = TextAlign.Center,
                                lineHeight = 15.sp,
                                fontWeight = FontWeight.Medium,
                            )
                            Image(painterResource(R.drawable.ic_gift_wall_start_right), contentDescription = "")
                        }
                    }
                }

                is GiftItem -> {
                    Column(
                        modifier =
                            Modifier
                                .noEffectClick {
                                    onGiftClicked(it)
                                }.fillMaxWidth()
                                .padding(horizontal = 3.dp, vertical = 5.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        val colorFilter =
                            if (it.count > 0) {
                                null
                            } else {
                                ColorFilter.colorMatrix(ColorMatrix().apply { setToSaturation(0f) })
                            }

                        Box {
                            Image(
                                WakooIcons.GiftWallItemBG,
                                contentDescription = null,
                                modifier =
                                    Modifier
                                        .fillMaxWidth()
                                        .aspectRatio(0.84375f),
                            )
                            NetworkImage(
                                it.gift.icon,
                                modifier =
                                    Modifier
                                        .align(Alignment.BottomCenter)
                                        .padding(bottom = 5.dp)
                                        .fillMaxWidth(0.8f)
                                        .aspectRatio(1f),
                                contentScale = ContentScale.Inside,
                                colorFilter = colorFilter,
                            )
                        }

                        Text(
                            text = it.gift.name,
                            modifier =
                                Modifier
                                    .padding(top = 5.dp)
                                    .height(18.dp),
                            color = Color(0xFF222222),
                            fontSize = 11.sp,
                            lineHeight = 22.sp,
                            fontWeight = FontWeight.SemiBold,
                            maxLines = 1,
                        )
                        Text(
                            text = "x${it.count}",
                            modifier =
                                Modifier
                                    .padding(top = 2.dp)
                                    .height(16.dp),
                            color = Color(0xFFC8CCD7),
                            fontSize = 12.sp,
                            lineHeight = 22.sp,
                            fontWeight = FontWeight.W900,
                            fontFamily = FontFamily.MI_SANS,
                            maxLines = 1,
                        )
                    }
                }

                is SpanItem -> {
                    SpanItemWidget(modifier = Modifier.fillMaxWidth())
                }

                else -> {
                }
            }
        }
    }
}

/**
 * 礼物详情弹窗
 * 在打开后需要调用接口获取该礼物的其他信息
 *
 * @param gift 礼物信息
 * @param targetUserId purpose = 2时需要的是对方id ,其他时候这个id都是个人主页的用户id
 * @param purpose 目的, 1是赠送 2是索要 3是施舍
 * @param sceneType 详情见[com.qyqy.ucoo.user.gift.GiftApi.getWallSummaryInfo] sceneType参数
 * @param sceneId 详情见[com.qyqy.ucoo.user.gift.GiftApi.getWallSummaryInfo] sceneId参数
 * @param begId 乞讨id, purpose = 3 就需要用到这个id了
 * @param onClose 关闭
 * @param onConfirm 赠送/索要
 */
@Composable
fun GiftItemDetailDialogContent(
    gift: IGift,
    targetUserId: Int,
    purpose: Int,
    sceneType: Int,
    sceneId: Int,
    begId: Int? = null,
    defaultCount: Int = 1,
    onClose: () -> Unit = {},
    onConfirm: (bean: GiftWallItemDetail, count: Int) -> Boolean = { _, _ -> false },
) {
    val viewModel = viewModel(GiftWallDetailViewModel::class)
    val isLoading by viewModel.isLoadingState.collectAsState()
    var giftDetail by remember(gift.id) {
        mutableStateOf<GiftWallItemDetail?>(null)
    }
    val giftNotNull = giftDetail
    val loadingMgr = LocalLoadingManager.current
    val scope = rememberCoroutineScope()
    var showBlessWordLayout by remember {
        mutableStateOf(false)
    }
    val giftCount = remember { mutableStateOf(defaultCount) }

    LaunchedEffect(gift.id) {
        giftCount.value = 1
    }

    EventBusEffect<SendGiftFromWallEvent> {
        if (targetUserId == it.userId) {
            viewModel.getGiftDetail(targetUserId, gift.id, purpose, sceneType, sceneId, begId) {
                giftDetail = it
            }
        }
    }
    LaunchedEffect(gift.id) {
        viewModel.getGiftDetail(targetUserId, gift.id, purpose, sceneType, sceneId, begId) {
            giftDetail = it
        }
    }

    GiftItemDetailContent(giftDetail ?: gift, isLoading, purpose, giftCount, onClose, onConfirm = { bean, count ->
        // 如果上层处理完毕了就不需要处理了
        if (onConfirm(bean, count)) {
            return@GiftItemDetailContent
        }

        when (purpose) {
            1 -> {
                when (bean.type) {
                    8 -> { // 祝福语礼物
                        if (giftNotNull?.bless != null) {
                            showBlessWordLayout = true
                        }
                    }

                    else -> {
                        loadingMgr.show(scope) {
                            viewModel.sendWallGift(
                                userId = targetUserId,
                                bean,
                                count,
                                sceneType,
                                sceneId,
                            )
                        }
                    }
                }
            }

            2 -> {
                // 开始乞讨
                loadingMgr.show(scope) {
                    viewModel.makeBegGiftRequest(userId = targetUserId, bean, count).onSuccess {
                        onClose()
                    }
                }
            }

            3 -> {
                // 开始打赏
                when (bean.type) {
                    8 -> { // 祝福语礼物
                        if (giftNotNull?.bless != null) {
                            showBlessWordLayout = true
                        }
                    }

                    else -> {
                        loadingMgr.show(scope) {
                            viewModel.giveBegGiftRequest(begId).onSuccess {
                                onClose()
                            }
                        }
                    }
                }
            }

            else -> {
                LogUtils.w("GiftWallScreen", "未知的操作purpose")
            }
        }
    })
    if (showBlessWordLayout && giftNotNull != null && giftNotNull.bless != null) {
        Dialog({
            showBlessWordLayout = false
        }) {
            BlessingWordEditLayout(
                title = giftNotNull.bless.title,
                placeholder = giftNotNull.bless.placeholder,
                hint = giftNotNull.bless.hint,
                onButtonClick = { blessWord ->
                    if (purpose == 1) {
                        loadingMgr.show(scope) {
                            viewModel.sendWallGift(
                                userId = targetUserId,
                                giftNotNull,
                                giftCount.value,
                                sceneType,
                                sceneId,
                            )
                            showBlessWordLayout = false
                        }
                    } else if (purpose == 3) {
                        loadingMgr.show(scope) {
                            viewModel.giveBegGiftRequest(begId, blessWord).onSuccess {
                                onClose()
                            }
                        }
                    }
                },
            )
        }
    }
}

@Composable
private fun GiftItemDetailContent(
    gift: IGift,
    isLoading: Boolean,
    purpose: Int,
    giftCount: MutableState<Int>,
    onClose: () -> Unit = {},
    onConfirm: (bean: GiftWallItemDetail, count: Int) -> Unit = { _, _ -> },
) {
    val detail = gift as? GiftWallItemDetail

    Box(
        modifier =
            Modifier
                .width(311.dp)
                .background(Color.White, shape = RoundedCornerShape(8.dp)),
    ) {
        Column(
            modifier =
                Modifier
                    .padding(top = 40.dp)
                    .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            NetworkImage(gift.icon, modifier = Modifier.size(100.dp))
            SizeHeight(8.dp)
            Text(
                buildAnnotatedString {
                    append(gift.name)
                    detail?.begCount?.let {
                        withStyle(SpanStyle(fontSize = 14.sp)) {
                            if (it != 0) {
                                append(" x$it")
                            }
                        }
                    }
                },
                fontSize = 16.sp,
                lineHeight = 20.sp,
                color = WakooText,
            )
            SizeHeight(8.dp)
            Row(verticalAlignment = Alignment.CenterVertically) {
                Image(
                    painterResource(
                        if (gift.priceType == 1) {
                            R.drawable.ic_green_diamond_straight
                        } else {
                            R.drawable.ic_silver_currency
                        },
                    ),
                    contentDescription = null,
                    modifier =
                        Modifier
                            .padding(end = 4.dp)
                            .size(14.dp),
                )
                Text((gift.price * (detail?.begCount ?: 1)).toString(), color = Color(0xff999999), fontSize = 14.sp, lineHeight = 16.sp)
            }
            SizeHeight(18.dp)
            Box(modifier = Modifier, contentAlignment = Alignment.Center) {
                if (isLoading) {
                    CircularProgressIndicator()
                } else if (purpose == 1) {
                    (gift as? GiftWallItemDetail)?.starCnt?.let {
                        Text(
                            "当前点亮总数%s个".localizedFormat(it),
                            fontSize = 13.sp,
                            lineHeight = 19.sp,
                            color = Color(0xFF666666),
                            fontWeight = FontWeight.Medium,
                        )
                    }
                } else if (purpose == 2) {
                    CounterWidget(giftCount, modifier = Modifier.fillMaxWidth())
                }
            }
            SizeHeight(18.dp)
            if (isLoading) {
                Spacer(modifier = Modifier.height(40.dp))
            } else if (detail != null) {
                GradientButton(
                    detail.actionBtnTxt,
                    enabled = detail.canAction,
                    gradientColors =
                        if (detail.canAction) {
                            listOf(
                                Color(0xFFA3FF2C),
                                Color(0xFF31FFA1),
                            )
                        } else {
                            listOf(
                                Color(0xFFE9EAEF),
                                Color(0xFFE9EAEF),
                            )
                        },
                    textColor = if (detail.canAction) WakooText else Color(0xff999999),
                    modifier =
                        Modifier
                            .fillMaxWidth(0.7f)
                            .height(40.dp),
                    onClick = {
                        if (detail.canAction) {
                            onConfirm(detail, giftCount.value)
                        } else {
                            // 不能点击的话就toast具体错误
                            if (detail.cantActionHint.isNotBlank()) {
                                showToast(detail.cantActionHint)
                            }
                        }
                    },
                )
            }
            SizeHeight(4.dp)
            detail?.hint?.let {
                if (it.isNotBlank()) {
                    Text(it, fontSize = 11.sp, lineHeight = 10.sp, color = Color(0xff999999), modifier = Modifier.padding(bottom = 12.dp))
                }
            }
            SizeHeight(15.dp)
        }
        Icon(
            WakooIcons.Close,
            contentDescription = null,
            modifier =
                Modifier
                    .padding(8.dp)
                    .size(24.dp)
                    .padding(4.5.dp)
                    .align(Alignment.TopEnd)
                    .noEffectClick(onClick = onClose),
            tint = Color(0xFFC9CDD4),
        )
    }
}

//region 各类组件
@Composable
private fun SpanItemWidget(modifier: Modifier = Modifier) {
    Column(
        modifier = modifier.padding(horizontal = 3.dp, vertical = 5.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.size(74.dp))
        Text(
            text = "礼物".localized,
            modifier =
                Modifier
                    .padding(top = 5.dp)
                    .height(18.dp),
            color = Color.Transparent,
            fontSize = 12.sp,
            maxLines = 1,
        )
        Text(
            text = "x0",
            modifier =
                Modifier
                    .padding(top = 2.dp)
                    .height(16.dp),
            color = Color.Transparent,
            fontSize = 10.sp,
            maxLines = 1,
        )
    }
}

/**
 * 计数组件
 * @param modifier 修饰符，用于自定义组件的外观和行为
 * @param countState 外部传入的计数state，用于管理计数值
 */
@Composable
private fun CounterWidget(
    countState: MutableState<Int>,
    modifier: Modifier = Modifier,
) {
    // 使用remember保存buttonRef状态，用于记录按钮的位置
    val buttonRef = remember { mutableStateOf<Rect?>(null) }
    // 创建一个水平排列的Row组件
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // 显示减少计数的图标按钮
        Image(
            WakooIcons.IconDrease,
            modifier =
                Modifier
                    .size(20.dp)
                    .background(color = Color(0xFFE9EAEF), shape = CircleShape)
                    .noEffectClick {
                        // 当计数大于1时，减少计数
                        if (countState.value > 1) {
                            countState.value -= 1
                        }
                    },
            contentDescription = null,
        )
        // 创建一个包含计数文本和下拉箭头的Box组件
        Box(
            modifier =
                Modifier
                    .padding(horizontal = 16.dp)
                    .width(98.dp)
                    .height(32.dp)
                    .background(
                        color = Color(0xFFE9EAEF),
                        shape = RoundedCornerShape(4.dp),
                    ).onGloballyPositioned { coordinates ->
                        // 记录按钮的位置
                        buttonRef.value = coordinates.boundsInRoot()
                    },
//                    .click(noEffect = true) {
//                        // 点击时显示下拉菜单
//                        makeVisible = true
//                    }
        ) {
            // 显示当前计数值
            Text(
                "${countState.value}",
                modifier =
                    Modifier
                        .align(Alignment.Center),
                fontSize = 12.sp,
                lineHeight = 10.sp,
                color = WakooText,
            )
//            // 显示下拉箭头图标
//            Image(
//                WakooIcons.ArrowRight,
//                modifier =
//                    Modifier
//                        .padding(end = 20.dp)
//                        .size(10.dp, 7.dp)
//                        .align(Alignment.CenterEnd),
//                contentDescription = null,
//            )
        }
        // 显示增加计数的图标按钮
        Image(
            WakooIcons.IconAdd,
            modifier =
                Modifier
                    .size(20.dp)
                    .background(color = Color(0xFFE9EAEF), shape = CircleShape)
                    .noEffectClick {
                        // 增加计数
                        countState.value += 1
                    },
            contentDescription = null,
        )
    }
}
//endregion

@Preview(showBackground = true)
@Composable
fun PreviewGiftWallPage() {
    val giftBean =
        GiftWall.GiftWrapper(
            gift = GiftWall.GiftWrapper.Gift(t = 0, id = 311, name = "测试礼物"),
            count = Random.nextInt(0, 2),
        )
    val list =
        mutableListOf<GiftWall.SeriesGift>()
            .apply {
                for (j in 0..2) {
                    add(
                        GiftWall.SeriesGift(
                            1,
                            seriesName = "测试礼物$j",
                            gifts =
                                buildList {
                                    val listSize = Random.nextInt(3, 10)
                                    for (i in 0 until listSize) {
                                        add(GiftWall.GiftWrapper(giftBean.gift, Random.nextInt(0, 2)))
                                    }
                                },
                        ),
                    )
                }
            }
    val items = GiftWallViewModel.convertGiftWall(list, 5)
    UserGiftWallTabScaffold(
        GiftWallSummaryBean(
            tabs =
                listOf(
                    GiftWallSummaryBean.Tab(t = 0, name = "盲盒礼物"),
                    GiftWallSummaryBean.Tab(t = 1, name = "普通礼物"),
                    GiftWallSummaryBean.Tab(t = 1, name = "普通礼物"),
                    GiftWallSummaryBean.Tab(t = 1, name = "普通礼物"),
                    GiftWallSummaryBean.Tab(t = 1, name = "普通礼物"),
                ),
        ),
    ) {
        UserGiftWallTabContent(items) {
        }
    }
}

//region 工具类 , 目前只有这里用到了,所以只放在这里

fun getColorFromGradient(
    colors: List<Color>,
    stops: List<Float>? =
        colors.mapIndexed { index, color ->
            (1f / colors.size) * index
        },
    position: Float, // 0f - 1f
): Color {
    val validPosition = position.coerceIn(0f, 1f)
    val computedStops =
        stops?.takeIf { it.size == colors.size }
            ?: List(colors.size) { i -> if (i.toFloat() / (colors.size - 1) < 1f) i.toFloat() / (colors.size - 1) else 1f }

    // 边界检查
    if (validPosition <= computedStops.first()) return colors.first()
    if (validPosition >= computedStops.last()) return colors.last()

    // 查找插值区间
    val index = computedStops.indexOfFirst { it >= validPosition } - 1
    val startStop = computedStops[index]
    val endStop = computedStops[index + 1]
    val intervalFraction = (validPosition - startStop) / (endStop - startStop)

    return lerp(colors[index], colors[index + 1], intervalFraction)
}

class ComposePath {
    companion object {
        fun obtain(): Path {
            val thiz = Path::class.java.toString()
            if (!RecyclePoolUtils.hasgister(thiz)) {
                RecyclePoolUtils.register(
                    thiz,
                    object : RecyclePoolUtils.InstanceCallback<Path> {
                        override fun newInstance(): Path = Path()

                        override fun reset(data: Path) {
                            data.reset()
                        }
                    },
                )
            }
            return RecyclePoolUtils.obtain()
        }

        inline fun doRun(func: (Path) -> Unit) {
            val obtain = obtain()
            func(obtain)
            RecyclePoolUtils.recycle(obtain)
        }
    }
}

/**
 * @param borderWidth
 * @param brush
 * @param radius
 * @param mode 0 不是任何一遍 1左边 2上边 3右边 4下边 5左右 6上下
 */
private fun Modifier.border(
    borderWidth: Dp = 1.dp,
    brush: Brush,
    radius: Dp = 0.dp,
    mode: Int = 0,
) = this.drawBehind {
    if (borderWidth == 0.dp || mode <= 0) {
        return@drawBehind
    }

    val strokeWidth = borderWidth.toPx()
    val width = size.width
    val height = size.height
    ComposePath.doRun {
        it.apply {
            when (mode) {
                2 -> {
                    moveTo(0f, radius.toPx())
                    quadraticTo(
                        0f,
                        0f,
                        radius.toPx(),
                        0f,
                    )
                    // 上边线 + topEndRadius
                    lineTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width,
                        0f,
                        width,
                        radius.toPx(),
                    )
                }

                4 -> {
                    moveTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )
                    lineTo(width - radius.toPx(), height)
                    quadraticTo(
                        width,
                        height,
                        width,
                        height - radius.toPx(),
                    )
                }

                1 -> {
                    moveTo(radius.toPx(), 0f)
                    quadraticTo(
                        0f,
                        0f,
                        0f,
                        radius.toPx(),
                    )
                    lineTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )
                }

                3 -> {
                    moveTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width,
                        0f,
                        width,
                        radius.toPx(),
                    )
                    lineTo(width, height - radius.toPx())
                    quadraticTo(
                        width,
                        height,
                        width - radius.toPx(),
                        height,
                    )
                }

                5 -> {
                    moveTo(radius.toPx(), 0f)
                    quadraticTo(
                        0f,
                        0f,
                        0f,
                        radius.toPx(),
                    )
                    lineTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )

                    moveTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width,
                        0f,
                        width,
                        radius.toPx(),
                    )
                    lineTo(width, height - radius.toPx())
                    quadraticTo(
                        width,
                        height,
                        width - radius.toPx(),
                        height,
                    )
                }

                6 -> {
                    moveTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )
                    lineTo(width - radius.toPx(), height)
                    quadraticTo(
                        width,
                        height,
                        width,
                        height - radius.toPx(),
                    )
                }

                else -> {
                }
            }
        }
        drawPath(
            path = it,
            brush = brush,
            style = Stroke(strokeWidth),
        )
    }
}

/**
 * @param borderWidth
 * @param brush
 * @param radius
 * @param mode 0 不是任何一遍 1左边 2上边 3右边 4下边 5左右 6上下
 */
private fun Modifier.border(
    borderWidth: Dp = 1.dp,
    color: Color,
    radius: Dp = 0.dp,
    mode: Int = 0,
) = this.drawBehind {
    if (borderWidth == 0.dp || mode <= 0) {
        return@drawBehind
    }

    val strokeWidth = borderWidth.toPx()
    val width = size.width
    val height = size.height

    ComposePath.doRun {
        it.apply {
            when (mode) {
                2 -> {
                    moveTo(0f, radius.toPx())
                    quadraticTo(
                        0f,
                        0f,
                        radius.toPx(),
                        0f,
                    )
                    // 上边线 + topEndRadius
                    lineTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width,
                        0f,
                        width,
                        radius.toPx(),
                    )
                }

                4 -> {
                    moveTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )
                    lineTo(width - radius.toPx(), height)
                    quadraticTo(
                        width,
                        height,
                        width,
                        height - radius.toPx(),
                    )
                }

                1 -> {
                    moveTo(radius.toPx(), 0f)
                    quadraticTo(
                        0f,
                        0f,
                        0f,
                        radius.toPx(),
                    )
                    lineTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )
                }

                3 -> {
                    moveTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width,
                        0f,
                        width,
                        radius.toPx(),
                    )
                    lineTo(width, height - radius.toPx())
                    quadraticTo(
                        width,
                        height,
                        width - radius.toPx(),
                        height,
                    )
                }

                5 -> {
                    moveTo(radius.toPx(), 0f)
                    quadraticTo(
                        0f,
                        0f,
                        0f,
                        radius.toPx(),
                    )
                    lineTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )

                    moveTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width,
                        0f,
                        width,
                        radius.toPx(),
                    )
                    lineTo(width, height - radius.toPx())
                    quadraticTo(
                        width,
                        height,
                        width - radius.toPx(),
                        height,
                    )
                }

                6 -> {
                    moveTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )
                    lineTo(width - radius.toPx(), height)
                    quadraticTo(
                        width,
                        height,
                        width,
                        height - radius.toPx(),
                    )
                }

                else -> {
                }
            }
        }

        drawPath(
            path = it,
            color = color,
            style = Stroke(strokeWidth),
        )
    }
}

//endregion
