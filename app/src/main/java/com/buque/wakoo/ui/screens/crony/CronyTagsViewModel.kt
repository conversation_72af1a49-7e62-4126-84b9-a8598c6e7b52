package com.buque.wakoo.ui.screens.crony

import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.toCState
import com.buque.wakoo.viewmodel.CSViewModel

class CronyTagsViewModel (private val region:Int): CSViewModel<List<CronyTag>>() {

    override suspend fun loadState(): CState<List<CronyTag>> {
        return GlobalRepository.cronyRepo.getTags(region).toCState()
    }
}