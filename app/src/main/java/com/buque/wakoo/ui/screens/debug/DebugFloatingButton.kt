package com.buque.wakoo.ui.screens.debug

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.ui.widget.AutoSizeText
import com.buque.wakoo.ui.widget.drag.DraggableFloatingItem
import com.buque.wakoo.ui.widget.drag.rememberDraggableFloatingState
import kotlinx.coroutines.delay

/**
 * 调试浮动按钮
 * 仅在 Debug 模式下显示，提供快速访问调试功能的入口
 */
@Composable
fun DebugFloatingButton() {
    // 只在 Debug 模式下显示
    if (EnvironmentManager.isProdRelease) {
        return
    }

    var showDebugMenu by remember { mutableStateOf(false) }

    val floatingState =
        rememberDraggableFloatingState(
            initialAlignment = Alignment.TopEnd,
            initialOffsetDp = DpOffset(x = 0.dp, y = 90.dp),
            initialIsVisible = false,
            initialIsStickyToEdge = true,
            allowDragOutOfBounds = true,
            stickyEdgeMarginDp = (-10).dp,
        )

    LaunchedEffect(floatingState) {
        delay(300)
        floatingState.isVisible = true
    }

    BoxWithConstraints(
        modifier = Modifier.fillMaxSize(),
    ) {
        // *** 这是最核心的修复：将 parentSize 从管理器同步到子 State ***
        LaunchedEffect(constraints) {
            floatingState.setBounds(
                parent =
                    Rect(
                        Offset.Zero,
                        Size(constraints.maxWidth.toFloat(), constraints.maxHeight.toFloat()),
                    ),
            )
        }

        DraggableFloatingItem(state = floatingState) {
            Box(
                modifier =
                    Modifier
                        .noEffectClick {
                            showDebugMenu = true
                        }.padding(horizontal = 10.dp)
                        .size(12.dp, 60.dp)
                        .background(MaterialTheme.colorScheme.primary.copy(0.5f)),
                contentAlignment = Alignment.Center,
            ) {
                AutoSizeText(
                    "调试工具",
                    color = MaterialTheme.colorScheme.onPrimary,
                    minTextSize = 8.sp,
                    maxTextSize = 12.sp,
                    fontSize = 12.sp,
                    lineHeight = 12.sp,
                    maxLines = 4,
                    textAlign = TextAlign.Center,
                )
            }
        }
    }

    // 调试菜单对话框
    if (showDebugMenu) {
        DebugMenu(
            onDismiss = {
                showDebugMenu = false
            },
        )
    }
}
