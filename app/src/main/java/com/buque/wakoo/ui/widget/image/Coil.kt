package com.buque.wakoo.ui.widget.image

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.isSpecified
import coil3.compose.AsyncImage
import coil3.compose.SubcomposeAsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import coil3.request.transformations
import com.buque.wakoo.R
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.RootNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.icons.ImageError
import com.buque.wakoo.ui.icons.ImagePlaceholder
import com.buque.wakoo.ui.icons.WakooIcons

/**
 * 为Compose加载网络图片
 */
@Composable
fun NetworkImage(
    data: Any?,
    modifier: Modifier = Modifier,
    contentScale: ContentScale = ContentScale.Crop,
    alignment: Alignment = Alignment.Center,
    placeholder: Painter? =
        rememberConstrainedCenteredVectorPainter(
            imageVector = WakooIcons.ImagePlaceholder,
            backgroundColor = Color(0xFFE9EAEF),
        ),
    error: Painter? =
        rememberConstrainedCenteredVectorPainter(
            imageVector = WakooIcons.ImageError,
            backgroundColor = Color(0xFFE9EAEF),
        ),
    preview: Painter? = placeholder,
    applyBuilder: ImageRequest.Builder.() -> Unit = { },
    crossfade: Boolean = true,
    memoryCacheKey: String? = null,
    colorFilter: ColorFilter? = null,
) {
    if (LocalInspectionMode.current) {
        Image(
            painter =
                when (data) {
                    is Int -> painterResource(data)
                    is Painter -> data
                    else -> preview ?: painterResource(id = R.drawable.ic_app_logo)
                },
            contentDescription = null,
            modifier = modifier,
            contentScale = contentScale,
            alignment = alignment,
        )
        return
    }
    val context = LocalContext.current

    AsyncImage(
        model =
            ImageRequest
                .Builder(context)
                .data(data)
                .memoryCacheKey(memoryCacheKey)
                .crossfade(crossfade)
                .apply {
                    this.applyBuilder()
                }.build(),
        contentDescription = null,
        contentScale = contentScale,
        alignment = alignment,
        modifier = modifier,
        placeholder = placeholder,
        error = error,
        colorFilter = colorFilter,
    )
}

/**
 * 带模糊效果的网络图片组件
 *
 * @param data 图片数据源
 * @param modifier 修饰符
 * @param blurRadius 模糊半径 (1-250)
 * @param sampling 采样率，用于提高性能 (1表示原始尺寸，2表示缩小一半)
 * @param contentScale 内容缩放方式
 * @param placeholder 占位符
 * @param error 错误时显示的图片
 * @param preview 预览图片
 * @param crossfade 是否启用淡入效果
 */
@Composable
fun BlurNetworkImage(
    data: Any?,
    modifier: Modifier = Modifier,
    blurRadius: Int = 10,
    sampling: Int = 1,
    contentScale: ContentScale = ContentScale.Crop,
    alignment: Alignment = Alignment.Center,
    placeholder: Painter? =
        rememberConstrainedCenteredVectorPainter(
            imageVector = WakooIcons.ImagePlaceholder,
            backgroundColor = Color(0xFFE9EAEF),
        ),
    error: Painter? =
        rememberConstrainedCenteredVectorPainter(
            imageVector = WakooIcons.ImageError,
            backgroundColor = Color(0xFFE9EAEF),
        ),
    preview: Painter? = placeholder,
    crossfade: Boolean = true,
) {
    NetworkImage(
        data = data,
        modifier = modifier,
        contentScale = contentScale,
        alignment = alignment,
        placeholder = placeholder,
        error = error,
        preview = preview,
        crossfade = crossfade,
        applyBuilder = {
            transformations(BlurTransformation(radius = blurRadius, sampling = sampling))
        },
    )
}

@Composable
fun SquareNetworkImage(
    data: Any?,
    modifier: Modifier = Modifier,
    size: Dp = 48.dp,
    shape: Shape = RectangleShape,
    contentScale: ContentScale = ContentScale.Crop,
    alignment: Alignment = Alignment.Center,
    placeholder: Painter? =
        rememberConstrainedCenteredVectorPainter(
            imageVector = WakooIcons.ImagePlaceholder,
            backgroundColor = Color(0xFFE9EAEF),
        ),
    error: Painter? =
        rememberConstrainedCenteredVectorPainter(
            imageVector = WakooIcons.ImageError,
            backgroundColor = Color(0xFFE9EAEF),
        ),
    preview: Painter? = placeholder,
    crossfade: Boolean = true,
) {
    NetworkImage(
        data = data,
        modifier =
            Modifier
                .size(size)
                .clip(shape)
                .then(modifier),
        contentScale = contentScale,
        alignment = alignment,
        placeholder = placeholder,
        error = error,
        preview = preview ?: painterResource(id = R.drawable.ic_app_logo),
        crossfade = crossfade,
    )
}

/**
 * 为Compose加载圆形头像图片
 */
@Composable
fun AvatarNetworkImage(
    user: User,
    modifier: Modifier = Modifier,
    size: Dp = 48.dp,
    shape: Shape = CircleShape,
    border: BorderStroke? = null,
    contentScale: ContentScale = ContentScale.Crop,
    alignment: Alignment = Alignment.Center,
    placeholder: Painter? =
        rememberConstrainedCenteredVectorPainter(
            imageVector = WakooIcons.ImagePlaceholder,
            backgroundColor = Color(0xFFE9EAEF),
        ),
    error: Painter? =
        rememberConstrainedCenteredVectorPainter(
            imageVector = WakooIcons.ImageError,
            backgroundColor = Color(0xFFE9EAEF),
        ),
    applyBuilder: ImageRequest.Builder.() -> Unit = { },
    preview: Painter? = placeholder,
    crossfade: Boolean = true,
    enabled: Boolean = true,
    onClick: (RootNavController) -> Unit = {
        it.push(Route.UserProfile(user.toBasic()))
    },
) {
    if (LocalInspectionMode.current) {
        Image(
            painter = preview ?: painterResource(id = R.drawable.ic_app_logo),
            contentDescription = null,
            modifier =
                modifier
                    .size(size)
                    .run {
                        if (border != null) {
                            border(border, shape)
                        } else {
                            this
                        }
                    }.clip(shape),
            contentScale = contentScale,
            alignment = alignment,
        )
        return
    }
    val rootNavController = LocalAppNavController.root
    NetworkImage(
        data = user.avatar,
        modifier =
            modifier
                .run {
                    val receiver =
                        if (border != null) {
                            border(border, shape)
                        } else {
                            this
                        }

                    if (size.isSpecified) {
                        receiver.size(size)
                    } else {
                        receiver
                    }
                }.clip(shape)
                .run {
                    if (enabled) {
                        clickable {
                            onClick(rootNavController)
                        }
                    } else {
                        this
                    }
                },
        contentScale = contentScale,
        alignment = alignment,
        placeholder = placeholder,
        error = error,
        preview = preview ?: painterResource(id = R.drawable.ic_app_logo),
        crossfade = crossfade,
        applyBuilder = applyBuilder,
    )
}

/**
 * 创建带加载状态的图片
 */
@Composable
fun LoadingNetworkImage(
    url: Any?,
    modifier: Modifier = Modifier,
    contentScale: ContentScale = ContentScale.Crop,
    alignment: Alignment = Alignment.Center,
    loading: @Composable () -> Unit,
    error: @Composable () -> Unit,
) {
    val context = LocalContext.current

    SubcomposeAsyncImage(
        model =
            ImageRequest
                .Builder(context)
                .data(url)
                .crossfade(true)
                .build(),
        contentDescription = null,
        contentScale = contentScale,
        alignment = alignment,
        modifier = modifier,
        loading = {
            loading()
        },
        error = {
            error()
        },
    )
}
