package com.buque.wakoo.ui.screens.crony

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.graphics.toColorInt
import com.buque.wakoo.R
import com.buque.wakoo.app.OnClick
import com.buque.wakoo.app.OnContent
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.theme.WakooUI
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.utils.buildAString
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

private val colorPurple = Color(0xFF9E5CE1)

@Composable
fun CronyEmptyCard(
    modifier: Modifier,
    onClick: OnClick = {},
) {
    Column(
        modifier
            .aspectRatio(98 / 130f)
            .paint(painter = painterResource(R.drawable.ff_cell_default), contentScale = ContentScale.FillBounds),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.weight(25f))
        Image(
            painter = painterResource(R.drawable.icon_crony_add),
            modifier =
                Modifier
                    .size(62.dp)
                    .noEffectClick(onClick = onClick),
            contentDescription = null,
            contentScale = ContentScale.FillBounds,
        )
        Spacer(modifier = Modifier.weight(10f))
        Text("邀请亲友".localized, fontSize = 12.sp, color = colorPurple)
        Spacer(modifier = Modifier.weight(20f))
    }
}

@Composable
fun CronyItemCard(
    item: CronyItem,
    showIntimacy: Boolean,
    modifier: Modifier = Modifier,
) {
    val tag = item.tag
    Box(
        modifier.aspectRatio(98 / 130f),
    ) {
        if (tag.labelBgImg.isNotEmpty()) {
            NetworkImage(tag.labelBgImg, modifier = Modifier.fillMaxSize(), contentScale = ContentScale.FillBounds)
        } else {
            Image(
                painterResource(R.drawable.ff_cell_default),
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                contentDescription = null,
            )
        }
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceBetween,
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .aspectRatio(98 / 24f),
//                    .then(if (tag.labelBgGradientColor.isNotEmpty()) Modifier else Modifier.background(tag.labelBgColor.toComposeColor())),
                contentAlignment = Alignment.Center,
            ) {
                Text(tag.name, fontSize = 13.sp, color = tag.smallFontColor.toComposeColor())
            }
            AvatarNetworkImage(
                item.user.basicUser,
                size = Dp.Unspecified,
                modifier =
                    Modifier
                        .fillMaxWidth(48 / 98f)
                        .aspectRatio(1f),
                border = BorderStroke(1.dp, WakooWhite),
            )
            Text(
                item.user.nickname,
                color = tag.nicknameFontColor.toComposeColor(),
                fontSize = 12.sp,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
            if (showIntimacy) {
                Text(
                    item.info.intimacyValue,
                    color = tag.nicknameFontColor.toComposeColor(),
                    fontSize = 12.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
            }
            SizeHeight(6.dp)
        }
    }
}

@Composable
fun LoadingCronyCard(modifier: Modifier = Modifier) {
    Box(
        modifier
            .aspectRatio(98 / 130f)
            .paint(painter = painterResource(R.drawable.ff_cell_default), contentScale = ContentScale.FillBounds),
    )
}

@Preview
@Composable
private fun PreviewCronyEmptyCard() {
    CronyEmptyCard(modifier = Modifier.width(98.dp))
}

@Composable
fun CronyMainCard(
    modifier: Modifier = Modifier,
    showBottomButtons: Boolean = true,
    unlockEnable: Boolean = true,
    content: OnContent = {},
    onSetting: OnClick = {},
    onUnlock: OnClick = {},
    onQuestionClick: OnClick = {},
) {
    val shape = RoundedCornerShape(24.dp)
    val self = LocalSelfUserProvider.current
    Box(modifier = modifier) {
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(top = 38.5.dp)
                    .background(Brush.verticalGradient(listOf(Color(0xFFEBC8FF), Color(0xFFF8EAFF))), shape)
                    .background(Brush.radialGradient(listOf(Color.White, Color.Transparent)))
                    .border(1.5.dp, Color(0xFFFCF8E4), shape)
                    .padding(horizontal = 14.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .aspectRatio(343 / 36.5f)
                        .padding(horizontal = 14.dp),
            ) {
                Image(
                    painter = painterResource(R.drawable.label_ff),
                    contentDescription = null,
                    contentScale = ContentScale.FillHeight,
                    modifier =
                        Modifier
                            .height(20.dp)
                            .align(Alignment.Center),
                )

                Image(
                    painter = painterResource(R.drawable.ff_question),
                    modifier =
                        Modifier
                            .align(Alignment.CenterEnd)
                            .click(onClick = onQuestionClick),
                    contentDescription = "qa",
                )
            }
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .weight(1f),
            ) {
                content()
            }
            if (showBottomButtons) {
                Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.spacedBy(12.dp)) {
                    if (unlockEnable && self.isCN) {
                        Box(
                            modifier =
                                Modifier
                                    .weight(1f)
                                    .height(36.dp)
                                    .clip(WakooUI.Shapes.chip)
                                    .clickable(onClick = onUnlock)
                                    .background(Brush.horizontalGradient(listOf(Color(0xFFF0A8FF), Color(0xFF945EFF))), WakooUI.Shapes.chip)
                                    .border(1.dp, Color(0xA6FFFFFF), WakooUI.Shapes.chip),
                            contentAlignment = Alignment.Center,
                        ) {
                            BasicText(
                                "解锁更多席位".localized,
                                style = TextStyle(color = Color.White),
                                autoSize = TextAutoSize.StepBased(minFontSize = 8.sp, maxFontSize = 14.sp),
                            )
                        }
                    }

                    Box(
                        modifier =
                            Modifier
                                .weight(1f)
                                .height(36.dp)
                                .clip(WakooUI.Shapes.chip)
                                .clickable(onClick = onSetting)
                                .background(Brush.horizontalGradient(listOf(Color(0xFFF3B2FF), Color(0xFFD6A5FD))), WakooUI.Shapes.chip)
                                .border(1.dp, Color(0xA6FFFFFF), WakooUI.Shapes.chip),
                        contentAlignment = Alignment.Center,
                    ) {
                        BasicText(
                            "亲友关系设置".localized,
                            style = TextStyle(color = Color(0xFF874AC7)),
                            autoSize = TextAutoSize.StepBased(minFontSize = 8.sp, maxFontSize = 14.sp),
                        )
                    }
                }
            }
            SizeHeight(22.dp)
        }
        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .aspectRatio(343 / 73f)
                    .paint(painterResource(R.drawable.decor_ff_cell_top), contentScale = ContentScale.FillBounds),
        )
    }
}

@Preview
@Composable
private fun PreviewCronyMainCard() {
    CronyMainCard(
        modifier =
            Modifier
                .fillMaxWidth()
                .height(450.dp),
        content = {
            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                horizontalArrangement = Arrangement.spacedBy(10.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp),
            ) {
                items(6) {
                    CronyEmptyCard(modifier = Modifier.fillMaxWidth())
                }
            }
        },
    )
}

@Serializable
data class CronyTag(
    @SerialName("card_bg_dark_color")
    val cardBgDarkColor: String = "",
    @SerialName("card_bg_light_color")
    val cardBgLightColor: String = "",
    @SerialName("id")
    val id: Int = 0,
    @SerialName("label_bg_color")
    val labelBgColor: String = "",
    @SerialName("label_bg_gradient_color")
    val labelBgGradientColor: List<String> = emptyList(),
    @SerialName("label_bg_img")
    val labelBgImg: String = "",
    @SerialName("name")
    val name: String = "",
    @SerialName("nickname_font_color")
    val nicknameFontColor: String = "",
    @SerialName("normal_font_color")
    val normalFontColor: String = "",
    @SerialName("small_font_color")
    val smallFontColor: String = "",
) {
    @Composable
    fun Content() {
        Box(
            modifier =
                Modifier
                    .widthIn(min = 41.dp)
                    .heightIn(min = 18.dp)
                    .then(
                        if (labelBgGradientColor.isNotEmpty()) {
                            Modifier.background(
                                Brush.horizontalGradient(
                                    colors =
                                        labelBgGradientColor
                                            .map { c ->
                                                runCatching { Color(c.toColorInt()) }.getOrNull() ?: Color.White
                                            }.toList(),
                                ),
                                RoundedCornerShape(4.dp),
                            )
                        } else {
                            Modifier.background(
                                runCatching {
                                    Color(labelBgColor.toColorInt())
                                }.getOrNull() ?: Color.White,
                                RoundedCornerShape(4.dp),
                            )
                        },
                    ),
            contentAlignment = Alignment.Center,
        ) {
            Text(name, fontSize = 12.sp, color = smallFontColor.toComposeColor())
        }
    }
}

fun String.toComposeColor(errorColor: Color = Color.White) = runCatching { Color(this.toColorInt()) }.getOrNull() ?: errorColor

@Composable
fun SelectRelationshipLabelDialog(
    labelList: List<CronyTag>,
    onConfirm: (CronyTag) -> Unit = {},
) {
    var selectedIndex by remember(labelList) {
        mutableIntStateOf(-1)
    }
    Column(
        modifier =
            Modifier
                .width(290.dp)
                .background(Color(0xFFFFFFFF), WakooUI.Shapes.small)
                .padding(vertical = 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        BasicText(
            modifier =
                Modifier
                    .height(24.dp)
                    .padding(horizontal = 10.dp),
            text = "请选择亲友关系标签".localized,
            style =
                TextStyle(
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF1D2129),
                    textAlign = TextAlign.Center,
                ),
            maxLines = 2,
            autoSize = TextAutoSize.StepBased(minFontSize = 10.sp, maxFontSize = 17.sp),
        )
        LazyVerticalGrid(
            columns = GridCells.Fixed(3),
            modifier =
                Modifier
                    .padding(top = 10.dp)
                    .fillMaxWidth()
                    .sizeIn(maxHeight = 280.dp)
                    .selectableGroup(),
            contentPadding = PaddingValues(horizontal = 16.dp, vertical = 10.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            itemsIndexed(labelList, key = { _, item ->
                item.id
            }) { index, item ->
                val selected = selectedIndex == index
                Surface(
                    selected = selected,
                    onClick = {
                        selectedIndex = index
                    },
                    modifier = Modifier.height(36.dp),
                    shape = WakooUI.Shapes.extraSmall,
                    color = if (selected) Color.Black else Color(0xFFE9EAEF),
                    contentColor = if (selected) WakooGreen else WakooGrayText,
                ) {
                    Box(
                        modifier =
                            Modifier
                                .fillMaxSize()
                                .padding(5.dp),
                        contentAlignment = Alignment.Center,
                    ) {
                        BasicText(
                            text = item.name,
                            style = TextStyle(color = if (selected) WakooGreen else Color(0xFFB6B6B6)),
                            autoSize = TextAutoSize.StepBased(minFontSize = 8.sp, maxFontSize = 15.sp),
                        )
                    }
                }
            }
        }
        GradientButton(
            "下一步".localized,
            onClick = {
                onConfirm(labelList[selectedIndex])
            },
            modifier =
                Modifier
                    .padding(top = 22.dp)
                    .size(238.dp, 36.dp),
            enabled = selectedIndex > -1,
        )
    }
}

@Preview(widthDp = 375)
@Composable
private fun PreviewSelectRelationshipLabelDialog() {
    SelectRelationshipLabelDialog(
        listOf(
            CronyTag(name = "你大爷"),
            CronyTag(name = "你二爷"),
            CronyTag(name = "你三爷"),
            CronyTag(name = "你四爷"),
            CronyTag(name = "你五爷"),
            CronyTag(name = "你六爷"),
            CronyTag(name = "你七爷"),
        ),
    )
}

@Composable
fun UnLockCronyPositionDialogContent(
    pricePerLabel: Int,
    onConfirm: (Int) -> Unit = {},
) {
    var unlockCount by remember {
        mutableIntStateOf(1)
    }
    Column(
        modifier =
            Modifier
                .width(290.dp)
                .background(Color.White, WakooUI.Shapes.small)
                .padding(vertical = 20.dp, horizontal = 10.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            modifier = Modifier.height(24.dp),
            text = "解锁亲友团席位".localized,
            style =
                TextStyle(
                    fontSize = 17.sp,
                    fontWeight = FontWeight.Medium,
                    color = WakooText,
                ),
            maxLines = 2,
        )

        Row(
            modifier = Modifier.padding(vertical = 40.dp),
            horizontalArrangement = Arrangement.spacedBy(2.dp),
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_filled_count_minus),
                contentDescription = null,
                modifier =
                    Modifier
                        .size(28.dp, 28.dp)
                        .clickable {
                            if (unlockCount > 1) {
                                unlockCount--
                            }
                        },
            )
            Box(
                modifier =
                    Modifier
                        .size(60.dp, 28.dp)
                        .border(1.dp, Color(0xFFE9EAEF), WakooUI.Shapes.extraSmall),
                contentAlignment = Alignment.Center,
            ) {
                Text(
                    text = unlockCount.toString(),
                    color = WakooText,
                    fontSize = 15.sp,
                    textAlign = TextAlign.Center,
                )
            }

            Image(
                painter = painterResource(id = R.drawable.ic_filled_count_add),
                contentDescription = null,
                modifier =
                    Modifier
                        .size(28.dp, 28.dp)
                        .clickable {
                            unlockCount++
                        },
            )
        }

        BasicText(
            text = "%d钻石可解锁1个亲友团席位".localizedFormat(pricePerLabel),
            maxLines = 2,
            style = TextStyle(color = Color(0xFF666666)),
            autoSize = TextAutoSize.StepBased(minFontSize = 10.sp, maxFontSize = 14.sp),
        )

        BasicText(
            modifier = Modifier.padding(top = 16.dp, bottom = 20.dp),
            text = "本次总花费：%d钻石".localizedFormat(pricePerLabel.times(unlockCount)),
            style = TextStyle(color = Color(0xFFFFB320)),
            maxLines = 2,
            autoSize = TextAutoSize.StepBased(minFontSize = 10.sp, maxFontSize = 14.sp),
        )

        GradientButton(
            "确认解锁".localized,
            onClick = {
                onConfirm(unlockCount)
            },
            modifier = Modifier.size(144.dp, 44.dp),
        )
    }
}

@Preview
@Composable
private fun PreviewUnlockCronyPosition() {
    UnLockCronyPositionDialogContent(20)
}

@Composable
fun CronyInviteCard(
    userLeft: User,
    userRight: User,
    message: AnnotatedString,
    tag: CronyTag,
    button: OnContent = {},
) {
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(horizontal = 70.dp),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(top = 29.dp)
                    .background(Brush.verticalGradient(listOf(Color(0xFFEBC8FF), Color(0xFFF8EAFF))), RoundedCornerShape(23.dp))
                    .background(Brush.radialGradient(listOf(Color.White, Color.Transparent)))
                    .border(1.5.dp, Color(0xFFFCF8E4), RoundedCornerShape(23.dp))
                    .padding(top = 70.dp)
                    .padding(horizontal = 13.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                message,
                fontSize = 16.sp,
                lineHeight = 32.sp,
                modifier =
                    Modifier.background(
                        Brush.horizontalGradient(
                            listOf(
                                Color.Transparent,
                                Color.White,
                                Color.Transparent,
                            ),
                        ),
                    ),
            )
            SizeHeight(20.dp)
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text("关系标签:".localized, color = Color(0xFF742A96))
                SizeWidth(2.dp)
                tag.Content()
            }
            button()
            SizeHeight(20.dp)
        }

        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .aspectRatio(343 / 73f)
                    .paint(painterResource(R.drawable.decor_ff_cell_top), contentScale = ContentScale.FillBounds),
        )
        val avatarMod = Modifier.border(1.dp, Color.White, CircleShape)
        Box(
            modifier =
                Modifier
                    .padding(top = 8.dp)
                    .size(120.dp, 64.dp)
                    .align(Alignment.TopCenter),
        ) {
            AvatarNetworkImage(userLeft, size = 64.dp, modifier = avatarMod)
            AvatarNetworkImage(userRight, size = 64.dp, modifier = avatarMod.align(Alignment.CenterEnd))
        }
    }
}

@Composable
fun CronyMessageCard(
    accepted: Boolean,
    cronyMessage: CronyMessage,
) {
    val self = LocalSelfUserProvider.current
    val isInviteMe = self.id == cronyMessage.invitee.id
    val aString =
        remember(cronyMessage) {
            val isMyPost = cronyMessage.inviter.id == self.id
            val str = if (isMyPost) cronyMessage.invitee.nickname else cronyMessage.inviter.nickname
            val full =
                if (isMyPost) {
                    "邀请%s加入我的亲友团".localizedFormat(cronyMessage.invitee.nickname)
                } else {
                    "邀请你加入%s的亲友团".localizedFormat(str)
                }
            buildAString(full to Color(0xFF742A96), str to Color(0xFFFC50B0))
        }
    val l = LocalLoadingManager.current

    val alpha = if (accepted) 0.3f else 1f
    Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
        CronyInviteCard(cronyMessage.inviter.basicUser, cronyMessage.invitee.basicUser, aString, cronyMessage.tag) {
            if (isInviteMe) {
                SizeHeight(20.dp)
                Text(
                    if (accepted) "已接受".localized else "接受邀请".localized,
                    color = Color.White.copy(alpha = if (accepted) 0.5f else 1f),
                    fontSize = 14.sp,
                    lineHeight = 36.sp,
                    modifier =
                        Modifier
                            .widthIn(min = 190.dp)
                            .clip(WakooUI.Shapes.chip)
                            .click(enabled = !accepted, onClick = {
//                            if (System.currentTimeMillis() > cronyMessage.expireTimeStamp * 1000) {
//                                showToast("邀请已过期".localized)
//                                return@click
//                            }
                                l.show(null) {
                                    GlobalRepository.cronyRepo.accept(cronyMessage.id)
                                }
                            })
                            .background(
                                Brush.horizontalGradient(
                                    listOf(Color(0xFFF0A8FF).copy(alpha = alpha), Color(0xFF945EFF).copy(alpha = alpha)),
                                ),
                                WakooUI.Shapes.chip,
                            ).border(1.dp, Color.White.copy(alpha = alpha), WakooUI.Shapes.chip),
                    textAlign = TextAlign.Center,
                )
            }
        }
    }
}
