package com.buque.wakoo.ui.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.bean.user.IUserDecorations
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.ui.widget.richtext.RichTextScope

//region 财富和魅力等级组件(日区专用)

/**
 * 经验值等级组件(财富等级和魅力等级, 日区特有)
 */
@Composable
fun ExpLevelWidget(
    wealthLevel: Int = 0,
    charmLevel: Int = 0,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        if (wealthLevel > 0) {
            LevelBadge(level = wealthLevel, wealthLevels, modifier = Modifier.size(40.dp, 16.dp))
        }
        if (wealthLevel > 0 && charmLevel > 0) {
            Spacer(modifier = Modifier.width(4.dp))
        }
        if (charmLevel > 0) {
            LevelBadge(level = charmLevel, charmLevels, modifier = Modifier.size(40.dp, 16.dp))
        }
    }
}

/**
 * 等级组件
 */
@Composable
fun ExpLevelWidget(
    user: IUserDecorations,
    modifier: Modifier = Modifier,
) {
    if (LocalSelfUserProvider.isJP) {
        ExpLevelWidget(charmLevel = user.charmLevel, wealthLevel = user.wealthLevel, modifier = modifier)
    } else {
        UserLevelWidget(level = user.level, modifier = modifier)
    }
}

fun RichTextScope.appendUserLevel(
    isCN: Boolean,
    user: IUserDecorations,
) {
    if (isCN) {
        InlineSizedContent(42.dp, 18.dp) {
            UserLevelWidget(level = user.level)
        }
    } else {
        InlineSizedContent(40.dp, 16.dp) {
            ExpLevelWidget(charmLevel = user.charmLevel, wealthLevel = user.wealthLevel)
        }
    }
}

@Composable
private fun LevelBadge(
    level: Int,
    levels: IntArray,
    modifier: Modifier = Modifier,
) {
    val index =
        remember(level) {
            level
                .minus(1)
                .coerceIn(0, 100)
                .div(10)
                .coerceIn(0, 9)
        }

    Row(
        modifier =
            modifier.paint(
                painter = painterResource(id = levels[index]),
                contentScale = ContentScale.FillBounds,
            ),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Spacer(modifier = Modifier.weight(8f))
        AutoSizeText(
            text = level.coerceAtLeast(1).toString(),
            modifier = Modifier.weight(7f),
            color = Color.White,
            maxTextSize = 10.sp,
            minTextSize = 5.sp,
            maxLines = 1,
            fontWeight = FontWeight.SemiBold,
            textAlign = TextAlign.Center,
        )
        Spacer(modifier = Modifier.weight(2.5f))
    }
}

private val wealthLevels =
    intArrayOf(
        R.drawable.ic_wealth_level_10,
        R.drawable.ic_wealth_level_20,
        R.drawable.ic_wealth_level_30,
        R.drawable.ic_wealth_level_40,
        R.drawable.ic_wealth_level_50,
        R.drawable.ic_wealth_level_60,
        R.drawable.ic_wealth_level_70,
        R.drawable.ic_wealth_level_80,
        R.drawable.ic_wealth_level_90,
        R.drawable.ic_wealth_level_100,
    )

private val charmLevels =
    intArrayOf(
        R.drawable.ic_charm_level_10,
        R.drawable.ic_charm_level_20,
        R.drawable.ic_charm_level_30,
        R.drawable.ic_charm_level_40,
        R.drawable.ic_charm_level_50,
        R.drawable.ic_charm_level_60,
        R.drawable.ic_charm_level_70,
        R.drawable.ic_charm_level_80,
        R.drawable.ic_charm_level_90,
        R.drawable.ic_charm_level_100,
    )

@Preview
@Composable
private fun PreviewLevelWidget() {
    Column {
        for (i in 0..9) {
            ExpLevelWidget(i * 10 + 1, i * 10 + 1)
        }
        ExpLevelWidget(100, 100)
    }
}

//endregion

//region 普通等级组件
private val normalLevels =
    intArrayOf(
        R.drawable.ic_level_10,
        R.drawable.ic_level_20,
        R.drawable.ic_level_30,
        R.drawable.ic_level_40,
        R.drawable.ic_level_50,
        R.drawable.ic_level_60,
        R.drawable.ic_level_70,
        R.drawable.ic_level_80,
        R.drawable.ic_level_90,
        R.drawable.ic_level_100,
    )

@Composable
fun UserLevelWidget(
    level: Int,
    modifier: Modifier = Modifier,
) {
    if (level <= 0) {
        Image(
            painter = painterResource(id = R.drawable.label_new),
            contentDescription = "level",
            modifier = modifier.size(42.dp, 18.dp),
        )
    } else {
        LevelBadge(
            level = level,
            levels = normalLevels,
            modifier = modifier.size(42.dp, 18.dp),
        )
    }
}

@Preview
@Composable
private fun PreviewNormalLevelWidget() {
    Column {
        UserLevelWidget(0)
        for (i in 0..9) {
            UserLevelWidget(i * 10 + 1)
        }
        UserLevelWidget(100)
    }
}

//endregion
