package com.buque.wakoo.ui.widget.wheelPicker

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import com.buque.wakoo.navigation.dialog.DialogScope
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.minus
import kotlinx.datetime.todayIn
import kotlin.time.Clock
import kotlin.time.ExperimentalTime

@OptIn(ExperimentalTime::class)
@Composable
fun DialogScope.AppDateWheelPickerPanel(
    title: String,
    initDateString: String,
    modifier: Modifier = Modifier,
    yearRange: IntRange =
        remember {
            val currentYear = Clock.System.todayIn(TimeZone.currentSystemDefault()).year
            (1950..2100.coerceAtLeast(currentYear))
        },
    onConfirm: (Boolean, LocalDate) -> Unit,
) {
    val initSelectedDate =
        remember {
            initDateString.takeIf { it.isNotEmpty() }?.let {
                LocalDate.parse(it)
            } ?: Clock.System
                .todayIn(TimeZone.currentSystemDefault())
                .minus(18, DateTimeUnit.YEAR)
        }

    var selectedDate by remember { mutableStateOf(initSelectedDate) }
    WheelPickerPanelScaffold(
        title = title,
        onDismissRequest = {
            dismiss()
        },
        onConfirm = {
            dismiss()
            onConfirm(initSelectedDate != selectedDate, selectedDate)
        },
        modifier = modifier,
    ) {
        DateWheelPicker(
            selectedDate = selectedDate,
            onDateChanged = { selectedDate = it },
            modifier = modifier,
            yearRange = yearRange,
            enabledDateRange =
                remember(yearRange) {
                    val startDate = LocalDate(yearRange.first, 1, 1)
                    val endDate = Clock.System.todayIn(TimeZone.currentSystemDefault()).minus(18, DateTimeUnit.YEAR)
                    startDate..endDate
                },
            isMonthInfinite = true,
            isDayInfinite = true,
        )
    }
}
