package com.buque.wakoo.ui.widget.state

import androidx.annotation.FloatRange
import androidx.compose.foundation.gestures.ScrollableState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.pulltorefresh.PullToRefreshState
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.ext.isNetworkException
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.widget.AppPullToRefreshBox
import com.buque.wakoo.ui.widget.pagination.LoadResult
import com.buque.wakoo.ui.widget.pagination.PaginateState
import com.buque.wakoo.ui.widget.pagination.VerticalPagerPaginateState
import com.buque.wakoo.ui.widget.pagination.rememberPaginateState
import com.buque.wakoo.ui.widget.pagination.rememberVerticalPagerPaginateState
import com.buque.wakoo.viewmodel.BasicListPaginateViewModel
import com.buque.wakoo.viewmodel.ListPaginateViewModel
import com.buque.wakoo.viewmodel.TabListPaginateViewModel
import kotlinx.coroutines.launch

/**
 * 一个通用的状态布局组件，根据传入的 `CState` 渲染不同的UI。
 *
 * @param T 成功状态下数据的类型。
 * @param state 当前的UI状态，类型为 `CState<T>`。
 * @param modifier Modifier应用于此布局的根容器。
 * @param onRetry 当显示错误或空视图时，点击重试/重新加载按钮的回调。
 * @param loadingContent 自定义的加载中视图。默认为一个居中的`CircularProgressIndicator`。
 * @param errorContent 自定义的错误视图。默认为一个包含错误信息和重试按钮的视图。
 * @param emptyContent 自定义的空数据视图。默认为一个包含提示信息和重新加载按钮的视图。
 * @param successContent **【核心】** 成功状态下要显示的视图。这是一个“插槽”，
 * 你需要提供一个 Composable Lambda，它接收成功获取的数据 `T` 并渲染对应的UI。
 */
@Composable
fun <T> CStateLayout(
    state: CState<T>,
    modifier: Modifier = Modifier,
    useScrollableLayout: Boolean = true,
    emptyText: String = "暂无数据".localized,
    emptyId: Int = R.drawable.ic_empty_for_all,
    emptyButton: String? = null,
    emptyCheckProvider: (CState.Success<T>) -> Boolean = {
        it.isEmpty()
    },
    onRetry: () -> Unit = {},
    onEmptyClick: () -> Unit = {},
    loadingContent: @Composable BoxScope.() -> Unit = {
        StateComponent.Loading()
    },
    errorContent: @Composable BoxScope.(throwable: Throwable) -> Unit = { DefaultErrorView(it, onRetry) },
    emptyContent: @Composable BoxScope.() -> Unit = {
        StateComponent.Empty(
            text = emptyText,
            emptyId = emptyId,
            buttonText = emptyButton,
            onClick = onEmptyClick,
        )
    },
    wrapperBox: @Composable (contentWidget: @Composable () -> Unit) -> Unit = {
        it()
    },
    successContent: @Composable (data: T) -> Unit,
) {
    // 根据state的具体类型，决定渲染哪个视图
    when (state) {
        is CState.Success -> {
            wrapperBox({
                if (emptyCheckProvider(state)) {
                    // 数据为空，显示空视图
                    Box(
                        modifier =
                            Modifier
                                .fillMaxSize()
                                .run {
                                    if (useScrollableLayout) {
                                        verticalScroll(rememberScrollState())
                                    } else {
                                        this
                                    }
                                },
                        contentAlignment = Alignment.Center,
                    ) {
                        emptyContent()
                    }
                } else {
                    // 数据加载成功且不为空，调用插槽，渲染成功视图
                    successContent(state.data)
                }
            })
        }

        is CState.Error -> {
            // 显示错误视图
            Box(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .run {
                            if (useScrollableLayout) {
                                verticalScroll(rememberScrollState())
                            } else {
                                this
                            }
                        },
                contentAlignment = Alignment.Center,
            ) {
                errorContent(state.throwable)
            }
        }

        is CState.Loading -> {
            // 只有在非“下拉刷新”模式下才显示全屏加载视图
            // 如果是下拉刷新，successContent会保持可见，由外部的PullRefresh组件显示刷新指示器
            if (!state.isRefreshing) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    loadingContent()
                }
            } else if (state.throwable != null) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxSize()
                            .run {
                                if (useScrollableLayout) {
                                    verticalScroll(rememberScrollState())
                                } else {
                                    this
                                }
                            },
                    contentAlignment = Alignment.Center,
                ) {
                    errorContent(state.throwable)
                }
            }
        }

        is CState.Idle -> {
            // 空闲状态，通常什么都不显示，等待进入加载状态
        }
    }
}

/**
 * 默认的错误视图
 */
@Composable
fun DefaultErrorView(
    throwable: Throwable,
    onRetry: () -> Unit,
) {
    val isNetworkError = throwable.isNetworkException()
    val title = if (isNetworkError) "网络错误".localized else "加载失败".localized
    if (isNetworkError) {
        StateComponent.NetworkError(text = title, onClick = onRetry)
    } else {
        StateComponent.Error(text = title, onClick = onRetry)
    }
}

@Composable
inline fun <
    ReqKey,
    PageKey : Any,
    Data,
    reified PaginateViewModel : ListPaginateViewModel<
        ReqKey,
        PageKey,
        Data,
        *,
    >,
> CStateListPaginateLayout(
    reqKey: ReqKey,
    modifier: Modifier = Modifier,
    viewModel: PaginateViewModel = viewModel<PaginateViewModel>(),
    refreshState: PullToRefreshState = rememberPullToRefreshState(),
    listState: ScrollableState = rememberLazyListState(),
    useScrollableLayout: Boolean = true,
    emptyText: String = "暂无数据".localized,
    emptyId: Int = R.drawable.ic_empty_for_all,
    emptyButton: String? = null,
    autoRefresh: Boolean = false,
    noinline wrapperBox: @Composable (contentWidget: @Composable () -> Unit) -> Unit = {
        it()
    },
    noinline emptyCheckProvider: (CState.Success<List<Data>>) -> Boolean = {
        it.isEmpty()
    },
    noinline onEmptyClick: (PaginateViewModel) -> Unit = {},
    crossinline content: @Composable (PaginateState<PageKey>, data: List<Data>) -> Unit,
) {
    val scope = rememberCoroutineScope()
    val cState = viewModel.getListState()

    val paginateState = rememberPaginateState<PageKey>(nextEnabled = false)

    val onRefresh: () -> Unit = {
        scope.launch {
            paginateState.setNextEnabled(false)
            val result = viewModel.refreshList(reqKey, true)
            if (result.success && result.nextPageKey != null) {
                paginateState.resetNext(result.nextPageKey)
                paginateState.setNextEnabled(true)
            }
        }
    }

    AppPullToRefreshBox(
        isRefreshing = cState.isRefreshing,
        onRefresh = onRefresh,
        modifier = modifier,
        state = refreshState,
    ) {
        LaunchedEffect(reqKey) {
            if (cState.shouldRequestData) {
                paginateState.setNextEnabled(false)
                val result = viewModel.refreshList(reqKey, false)
                if (result.success && result.nextPageKey != null) {
                    paginateState.resetNext(result.nextPageKey)
                    paginateState.setNextEnabled(true)
                }
            } else if (autoRefresh) {
                onRefresh()
            }
        }

        CStateLayout(
            cState,
            useScrollableLayout = useScrollableLayout,
            emptyText = emptyText,
            emptyId = emptyId,
            emptyButton = emptyButton,
            emptyCheckProvider = emptyCheckProvider,
            wrapperBox = wrapperBox,
            onRetry = {
                scope.launch {
                    paginateState.setNextEnabled(false)
                    val result = viewModel.refreshList(reqKey, false)
                    if (result.success && result.nextPageKey != null) {
                        paginateState.resetNext(result.nextPageKey)
                        paginateState.setNextEnabled(true)
                    }
                }
            },
            onEmptyClick = {
                onEmptyClick(viewModel)
            },
        ) { list ->
            when (listState) {
                is LazyListState -> {
                    paginateState.ConnectToState(listState) {
                        if (it.next) {
                            viewModel.loadMoreTabDataList(reqKey, it.key)
                        } else {
                            LoadResult.Page(null)
                        }
                    }
                }

                is LazyGridState -> {
                    paginateState.ConnectToGridState(listState) {
                        if (it.next) {
                            viewModel.loadMoreTabDataList(reqKey, it.key)
                        } else {
                            LoadResult.Page(null)
                        }
                    }
                }

                else -> {
                    throw IllegalArgumentException("only support LazyListState and LazyGridState")
                }
            }

            content(paginateState, list)
        }
    }
}

@Composable
inline fun <
    PageKey : Any,
    Data,
    reified PaginateViewModel : BasicListPaginateViewModel<
        PageKey,
        Data,
    >,
> StateListPaginateLayout(
    modifier: Modifier = Modifier,
    viewModel: PaginateViewModel = viewModel<PaginateViewModel>(),
    refreshState: PullToRefreshState = rememberPullToRefreshState(),
    listState: LazyListState = rememberLazyListState(),
    useScrollableLayout: Boolean = true,
    emptyText: String = "暂无数据".localized,
    emptyId: Int = R.drawable.ic_empty_for_all,
    emptyButton: String? = null,
    autoRefresh: Boolean = false,
    refreshEnable: Boolean = true,
    paginateState: PaginateState<PageKey> = rememberPaginateState(nextEnabled = false),
    noinline wrapperBox: @Composable (contentWidget: @Composable () -> Unit) -> Unit = {
        it()
    },
    noinline emptyCheckProvider: (CState.Success<List<Data>>) -> Boolean = {
        it.isEmpty()
    },
    noinline onEmptyClick: (PaginateViewModel) -> Unit = {},
    noinline emptyContent: @Composable BoxScope.() -> Unit = {
        StateComponent.Empty(
            text = emptyText,
            emptyId = emptyId,
            buttonText = emptyButton,
            onClick = {
                onEmptyClick(viewModel)
            },
        )
    },
    crossinline content: @Composable (PaginateState<PageKey>, data: List<Data>) -> Unit,
) {
    val scope = rememberCoroutineScope()
    val cState = viewModel.getListState()
    val onRefresh: () -> Unit = {
        scope.launch {
            paginateState.setNextEnabled(false)
            val result = viewModel.refreshList()
            if (result.success && result.nextPageKey != null) {
                paginateState.resetNext(result.nextPageKey)
                paginateState.setNextEnabled(true)
            }
        }
    }

    AppPullToRefreshBox(
        isRefreshing = cState.isRefreshing,
        onRefresh = onRefresh,
        modifier = modifier,
        enabled = refreshEnable,
        state = refreshState,
    ) {
        LaunchedEffect(Unit) {
            if (cState.shouldRequestData) {
                paginateState.setNextEnabled(false)
                val result = viewModel.refreshList(false)
                if (result.success && result.nextPageKey != null) {
                    paginateState.resetNext(result.nextPageKey)
                    paginateState.setNextEnabled(true)
                }
            } else if (autoRefresh) {
                onRefresh()
            }
        }

        CStateLayout(
            cState,
            useScrollableLayout = useScrollableLayout,
            emptyText = emptyText,
            emptyId = emptyId,
            emptyButton = emptyButton,
            emptyContent = emptyContent,
            wrapperBox = wrapperBox,
            onRetry = {
                scope.launch {
                    paginateState.setNextEnabled(false)
                    val result = viewModel.refreshList(false)
                    if (result.success && result.nextPageKey != null) {
                        paginateState.resetNext(result.nextPageKey)
                        paginateState.setNextEnabled(true)
                    }
                }
            },
            onEmptyClick = {
                onEmptyClick(viewModel)
            },
        ) { list ->
            paginateState.ConnectToState(listState) {
                if (it.next) {
                    viewModel.loadNexPageData()
                } else {
                    LoadResult.Page(null)
                }
            }

            content(paginateState, list)
        }
    }
}

@Composable
inline fun <
    ReqKey,
    PageKey : Any,
    TabKey,
    Data,
    reified PaginateViewModel : TabListPaginateViewModel<
        ReqKey,
        PageKey,
        TabKey,
        Data,
        *,
    >,
> CStateListPaginateLayout(
    reqKey: ReqKey,
    tabKey: TabKey,
    modifier: Modifier = Modifier,
    viewModel: PaginateViewModel = viewModel<PaginateViewModel>(),
    refreshState: PullToRefreshState = rememberPullToRefreshState(),
    listState: ScrollableState = rememberLazyListState(),
    useScrollableLayout: Boolean = true,
    emptyText: String = "暂无数据".localized,
    emptyId: Int = R.drawable.ic_empty_for_all,
    emptyButton: String? = null,
    autoRefresh: Boolean = false,
    noinline wrapperBox: @Composable (contentWidget: @Composable () -> Unit) -> Unit = {
        it()
    },
    noinline onEmptyClick: (PaginateViewModel) -> Unit = {},
    crossinline content: @Composable (PaginateState<PageKey>, data: List<Data>) -> Unit,
) {
    val scope = rememberCoroutineScope()
    val cState = viewModel.getListState(tabKey)

    val paginateState = rememberPaginateState<PageKey>(nextEnabled = false)

    val onRefresh: () -> Unit = {
        scope.launch {
            paginateState.setNextEnabled(false)
            val result = viewModel.refreshTabList(reqKey, tabKey, true)
            if (result.success && result.nextPageKey != null) {
                paginateState.resetNext(result.nextPageKey)
                paginateState.setNextEnabled(true)
            }
        }
    }

    AppPullToRefreshBox(
        isRefreshing = cState.isRefreshing,
        onRefresh = onRefresh,
        modifier = modifier,
        state = refreshState,
    ) {
        LaunchedEffect(reqKey, tabKey) {
            if (cState.shouldRequestData) {
                paginateState.setNextEnabled(false)
                val result = viewModel.refreshTabList(reqKey, tabKey, false)
                if (result.success && result.nextPageKey != null) {
                    paginateState.resetNext(result.nextPageKey)
                    paginateState.setNextEnabled(true)
                }
            } else if (autoRefresh) {
                onRefresh()
            }
        }

        CStateLayout(
            cState,
            useScrollableLayout = useScrollableLayout,
            emptyText = emptyText,
            emptyId = emptyId,
            emptyButton = emptyButton,
            wrapperBox = wrapperBox,
            onRetry = {
                scope.launch {
                    paginateState.setNextEnabled(false)
                    val result = viewModel.refreshTabList(reqKey, tabKey, false)
                    if (result.success && result.nextPageKey != null) {
                        paginateState.resetNext(result.nextPageKey)
                        paginateState.setNextEnabled(true)
                    }
                }
            },
            onEmptyClick = {
                onEmptyClick(viewModel)
            },
        ) { list ->
            when (listState) {
                is LazyListState -> {
                    paginateState.ConnectToState(listState) {
                        if (it.next) {
                            viewModel.loadMoreTabDataList(reqKey, tabKey, it.key)
                        } else {
                            LoadResult.Page(null)
                        }
                    }
                }

                is LazyGridState -> {
                    paginateState.ConnectToGridState(listState) {
                        if (it.next) {
                            viewModel.loadMoreTabDataList(reqKey, tabKey, it.key)
                        } else {
                            LoadResult.Page(null)
                        }
                    }
                }

                else -> {
                    throw IllegalArgumentException("only support LazyListState and LazyGridState")
                }
            }

            content(paginateState, list)
        }
    }
}

@Composable
inline fun <
    ReqKey,
    PageKey : Any,
    TabKey,
    Data,
    reified PaginateViewModel : TabListPaginateViewModel<
        ReqKey,
        PageKey,
        TabKey,
        Data,
        *,
    >,
> CStateVerticalPagerPaginateLayout(
    reqKey: ReqKey,
    tabKey: TabKey,
    modifier: Modifier = Modifier,
    refreshEnable: Boolean = true,
    viewModel: PaginateViewModel = viewModel<PaginateViewModel>(),
    refreshState: PullToRefreshState = rememberPullToRefreshState(),
    initialPage: Int = 0,
    @FloatRange(from = -0.5, to = 0.5) initialPageOffsetFraction: Float = 0f,
    useScrollableLayout: Boolean = true,
    emptyText: String = "暂无数据".localized,
    emptyId: Int = R.drawable.ic_empty_for_all,
    emptyButton: String? = null,
    noinline wrapperBox: @Composable (contentWidget: @Composable () -> Unit) -> Unit = {
        it()
    },
    noinline onEmptyClick: (VerticalPagerPaginateState<PageKey>, PaginateViewModel) -> Unit = { _, _ -> },
    crossinline content: @Composable (VerticalPagerPaginateState<PageKey>, PagerState, data: List<Data>) -> Unit,
) {
    val scope = rememberCoroutineScope()
    val cState = viewModel.getListState(tabKey)

    val paginateState = rememberVerticalPagerPaginateState<PageKey>(enabled = false)

    AppPullToRefreshBox(
        isRefreshing = cState.isRefreshing,
        onRefresh = {
            scope.launch {
                paginateState.setEnabled(false)
                val result = viewModel.refreshTabList(reqKey, tabKey, true)
                if (result.success && result.nextPageKey != null) {
                    paginateState.reset(result.nextPageKey, true)
                }
            }
        },
        modifier = modifier,
        state = refreshState,
        enabled = refreshEnable,
    ) {
        LaunchedEffect(reqKey, tabKey) {
            if (cState.shouldRequestData) {
                paginateState.setEnabled(false)
                val result = viewModel.refreshTabList(reqKey, tabKey, false)
                if (result.success && result.nextPageKey != null) {
                    paginateState.reset(result.nextPageKey, true)
                }
            }
        }

        CStateLayout(
            cState,
            useScrollableLayout = useScrollableLayout,
            emptyText = emptyText,
            emptyId = emptyId,
            emptyButton = emptyButton,
            wrapperBox = wrapperBox,
            onRetry = {
                scope.launch {
                    paginateState.setEnabled(false)
                    val result = viewModel.refreshTabList(reqKey, tabKey, false)
                    if (result.success && result.nextPageKey != null) {
                        paginateState.reset(result.nextPageKey, true)
                    }
                }
            },
            onEmptyClick = {
                onEmptyClick(paginateState, viewModel)
            },
        ) { list ->

            val pagerState = rememberPagerState(initialPage, initialPageOffsetFraction) { list.size }

            paginateState.ConnectToState(pagerState) { key, currentPageCount ->
                viewModel.loadMoreTabDataList(reqKey, tabKey, key)
            }

            content(paginateState, pagerState, list)
        }
    }
}
