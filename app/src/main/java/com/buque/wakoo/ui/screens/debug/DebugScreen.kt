package com.buque.wakoo.ui.screens.debug

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.buque.wakoo.app.Const
import com.buque.wakoo.app.currentUserKV
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.core.webview.AppLinkNavigator
import com.buque.wakoo.ext.parseValue
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.navigation.GalleryScreenKey
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.RootNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.repository.UserRepository
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.ExpandableText
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.media.previewer.MediaViewerAlbum
import com.buque.wakoo.ui.widget.media.previewer.MediaViewerItem
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.eventBus.tryToLink
import kotlinx.coroutines.launch

@Composable
fun DebugScreen(controller: RootNavController) {
    val ctrl = LocalAppNavController.current
    val dc = rememberDialogController()
    val lm = LocalLoadingManager.current
    val scope = rememberCoroutineScope()
    val u by AccountManager.userStateFlow.collectAsState()
    val scrollState = rememberScrollState()

    val repo = remember { UserRepository() }
    val selfUserInfo = LocalSelfUserProvider.current
    val isCN = selfUserInfo.isCN

    TitleScreenScaffold("Debug") {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .verticalScroll(scrollState)
                    .padding(it),
        ) {
            ExpandableText(u?.toString() ?: "", modifier = Modifier.padding(16.dp))
            if (u != null) {
                Text("isJp:${u!!.isJP},isHQU:${u?.isHQU}")
            }
            FlowRow(horizontalArrangement = Arrangement.spacedBy(4.dp)) {
                Button(onClick = {
                    "wakoo://page/main?tab=discover".tryToLink()
                }) {
                    Text("Back to Discover")
                }
                Button(onClick = {
                    controller.push(Route.Web("${EnvironmentManager.current.apiUrl}h5/hello"))
                }) {
                    Text("H5 测试")
                }
                Button(onClick = {
                    controller.push(Route.Web("${Const.LOCAL_WEB_URL}h5/hello"))
                }) {
                    Text("本地H5 测试")
                }
                Button(onClick = {
                    scope.launch {
                        repo
                            .getNextAction()
                            .onSuccess { obj ->
                                obj.parseValue<String>("toast")?.let {
                                    showToast(it)
                                }
                                obj.parseValue<String>("next_action")?.tryToLink()
                            }
                    }
                }) {
                    Text("PUQC测试")
                }

                Button(onClick = {
                    LogUtils.d(AccountManager.getAccessToken().orEmpty())
                }) {
                    Text("log token")
                }
                Button(onClick = {
                    AppLinkNavigator.go("wakoo://page/dressup_mall", controller, dc)
                }) {
                    Text("装扮商城")
                }

                SolidButton("群组", onClick = {
                    controller.push(Route.ChatGroup("564"))
                })

                var id by remember { mutableStateOf("") }
                Button(onClick = {
                    dc.easyPost {
                        Column(modifier = Modifier.width(270.dp)) {
                            AppTextField(id, onValueChange = { newUid ->
                                id = newUid
                            })
                            SolidButton("打开群组", onClick = {
                                dismiss()
                                controller.push(Route.ChatGroup(id))
                            })
                            SolidButton("打开个人主页", onClick = {
                                dismiss()
                                controller.push(Route.UserProfile(BasicUser.sampleBoy.copy(id = id)))
                            })
                        }
                    }
                }) {
                    Text("打开用户主页")
                }
                Button(onClick = {
                    AppLinkNavigator.go(
                        "wakoo://page/web_frame?info=%7B%22target_url%22%3A%20%22https%3A//api.test.wakooclub.com/h5/auth/japan/widw/pre_apply%22%2C%20%22gravity%22%3A%20%22bottom%22%2C%20%22width%22%3A%20-1%2C%20%22height%22%3A%20422%2C%20%22radius%22%3A%20%7B%22left_top%22%3A%2012%2C%20%22right_top%22%3A%2012%2C%20%22right_bottom%22%3A%2012%2C%20%22left_bottom%22%3A%2012%7D%2C%20%22cancelable%22%3A%20true%7D",
                        ctrl,
                        dc,
                    )
                }) {
                    Text("打开提现")
                }
                Button(onClick = {
                    AppLinkNavigator.go("wakoo://page/report?type=1&target_id=123", ctrl, dc)
                }) {
                    Text("打开Report")
                }

                Button(onClick = {
                    currentUserKV.putBoolean("first_enter_message_page", true)
                }) {
                    Text("打开通知")
                }
            }

            Button(onClick = {
                val videoUrls =
                    listOf(
                        // 西瓜视频 Demo
                        "https://sf1-cdn-tos.huoshanstatic.com/obj/media-fe/xgplayer_doc_video/mp4/xgplayer-demo-360p.mp4",
                        // 大灰熊
                        "https://www.w3schools.com/html/movie.mp4",
                        // 冰川 (Sintel trailer)
                        "https://media.w3.org/2010/05/sintel/trailer.mp4",
                        // 新闻视频1 (齐鲁网)
                        "https://stream7.iqilu.com/10339/upload_transcode/202002/09/20200209105011F0zPoYzHry.mp4",
                        // 新闻视频2 (齐鲁网)
                        "https://stream7.iqilu.com/10339/upload_transcode/202002/09/20200209104902N3v5Vpxuvb.mp4",
                    )

                val album =
                    MediaViewerAlbum(
                        items =
                            buildList {
                                addAll(
                                    listOf(
                                        // Photo by Mahyar Motebassem (https://unsplash.com/photos/f0d83M-PkNw).
                                        MediaViewerItem.Image(
                                            fullSizedUrl = "https://unsplash.com/photos/f0d83M-PkNw/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODMzODU2fA&force=true",
                                            placeholderImageUrl = "https://unsplash.com/photos/f0d83M-PkNw/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODMzODU2fA&force=true&w=300",
                                            aspectRatio = 300f / 375f,
                                        ),
                                        // Photo by Jack White (https://unsplash.com/photos/jDCIBr88RGU/).
                                        MediaViewerItem.Image(
                                            fullSizedUrl = "https://unsplash.com/photos/L_SjEwDtJEI/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODM0MjAxfA&force=true",
                                            placeholderImageUrl = "https://unsplash.com/photos/L_SjEwDtJEI/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODM0MjAxfA&force=true&w=300",
                                            aspectRatio = 300f / 450f,
                                        ),
                                        // Photo by Romain Guy (https://www.flickr.com/photos/romainguy/).
                                        MediaViewerItem.Image(
                                            fullSizedUrl = "https://live.staticflickr.com/4734/39442725251_be4b6395a2_o_d.jpg",
                                            placeholderImageUrl = "https://live.staticflickr.com/4734/39442725251_ed2353237e_c_d.jpg",
                                            aspectRatio = 533f / 800f,
                                        ),
                                        MediaViewerItem.Image(
                                            fullSizedUrl = "https://live.staticflickr.com/4687/39511378181_e815b89822_o_d.jpg",
                                            placeholderImageUrl = "https://live.staticflickr.com/4687/39511378181_ab0c158858_c_d.jpg",
                                            aspectRatio = 449 / 800f,
                                        ),
                                        MediaViewerItem.Video(
                                            fullSizedUrl = "https://sf1-cdn-tos.huoshanstatic.com/obj/media-fe/xgplayer_doc_video/mp4/xgplayer-demo-360p.mp4",
                                            placeholderImageUrl = "https://sf1-cdn-tos.huoshanstatic.com/obj/media-fe/xgplayer_doc_video/mp4/xgplayer-demo-360p.mp4",
                                            aspectRatio = 640 / 360f,
                                        ),
                                        MediaViewerItem.Video(
                                            fullSizedUrl = "https://www.w3schools.com/html/movie.mp4",
                                            placeholderImageUrl = "https://www.w3schools.com/html/movie.mp4",
                                            aspectRatio = 320 / 240f,
                                        ),
                                        MediaViewerItem.Video(
                                            fullSizedUrl = "https://media.w3.org/2010/05/sintel/trailer.mp4",
                                            placeholderImageUrl = "https://media.w3.org/2010/05/sintel/trailer.mp4",
                                            aspectRatio = 854 / 480f,
                                        ),
                                        MediaViewerItem.Video(
                                            fullSizedUrl = "https://stream7.iqilu.com/10339/upload_transcode/202002/09/20200209105011F0zPoYzHry.mp4",
                                            placeholderImageUrl = "https://stream7.iqilu.com/10339/upload_transcode/202002/09/20200209105011F0zPoYzHry.mp4",
                                            aspectRatio = 960 / 540f,
                                        ),
                                        MediaViewerItem.Video(
                                            fullSizedUrl = "https://stream7.iqilu.com/10339/upload_transcode/202002/09/20200209104902N3v5Vpxuvb.mp4",
                                            placeholderImageUrl = "https://stream7.iqilu.com/10339/upload_transcode/202002/09/20200209104902N3v5Vpxuvb.mp4",
                                            aspectRatio = 960 / 540f,
                                        ),
                                    ),
                                )
                            },
                    )

                controller.push(GalleryScreenKey(album))
            }) {
                Text("打开图库")
            }
        }
    }
}
