package com.buque.wakoo.ui.screens.chatgroup.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.chatgroup.ChatGroupMember
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.screens.chatgroup.chat.ChatGroupAdmin
import com.buque.wakoo.ui.screens.chatgroup.chat.ChatGroupOwner
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.state.StateListPaginateLayout
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupOnlineMemberListViewModel
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupSettingsViewModel
import com.buque.wakoo.viewmodel.chatgroup.OnlineKey

/**
 * 群组成员列表，用群信息第一页数据再加成员接口实现
 */
@Composable
fun ChatGroupMemberListScreen(viewModel: ChatGroupOnlineMemberListViewModel) {
    val vm = viewModel<ChatGroupSettingsViewModel>(initializer = {
        ChatGroupSettingsViewModel(viewModel.groupId)
    })
    LaunchedEffect(vm) {
        vm.refreshState()
    }
    val lm = LocalLoadingManager.current
    TitleScreenScaffold("成员列表".localized) { pd ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(pd)
        ) {
            ChatGroupMemberList(viewModel.groupId, viewModel)

            val state by vm.state
            val st = state.dataOrNull ?: return@Box
            val joinState = st.joinState
            Box(
                modifier =
                    Modifier
                        .align(Alignment.BottomCenter)
                        .fillMaxWidth()
                        .height(120.dp)
                        .background(
                            Brush.verticalGradient(
                                0f to Color(0x00FFFFFF),
                                0.5f to Color(0xCCFFFFFF),
                                1f to Color(0xFFFFFFFF),
                            ),
                        )
                        .padding(top = 22.dp),
            ) {
                if (!joinState.isMember) {
                    GradientButton(
                        text =
                            "申请中".localized.takeIf { joinState.isApplying } ?: "申请加入".localized,
                        onClick = {
                            lm.show(null) {
                                vm.applyJoin()
                            }
                        },
                        enabled = joinState.isNone,
                        modifier =
                            Modifier
                                .padding(horizontal = 32.dp)
                                .fillMaxWidth(),
                    )
                }
            }
        }
    }
}

@Composable
private fun ChatGroupMemberList(
    groupId: String,
    viewModel: ChatGroupOnlineMemberListViewModel =
        viewModel<ChatGroupOnlineMemberListViewModel>(initializer = {
            ChatGroupOnlineMemberListViewModel(groupId)
        }),
) {
    val nav = LocalAppNavController.root
    StateListPaginateLayout<OnlineKey, ChatGroupMember, ChatGroupOnlineMemberListViewModel>(viewModel = viewModel) { _, list ->
        LazyColumn(
            modifier =
                Modifier
                    .padding(horizontal = 12.dp)
                    .fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(top = 10.dp, bottom = 150.dp),
        ) {
            items(list) { item ->
                Row(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(vertical = 10.dp)
                            .widthIn(max = 64.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(modifier = Modifier.size(48.dp)) {
                        AvatarNetworkImage(
                            modifier =
                                Modifier.noEffectClick(onClick = {
                                    nav.push(Route.UserProfile(item.user))
                                }),
                            user = item.user,
                            size = 48.dp,
                        )
                        if (item.isOnline) {
                            Box(
                                modifier =
                                    Modifier
                                        .size(14.dp)
                                        .align(Alignment.BottomEnd)
                                        .padding(2.dp)
                                        .background(Color.White, CircleShape)
                                        .padding(2.dp)
                                        .background(
                                            WakooGreen,
                                            CircleShape,
                                        ),
                            )
                        }
                    }
                    SizeWidth(8.dp)
                    Column {
                        Text(
                            text = item.user.name,
                            color = Color(0xFF111111),
                            style = MaterialTheme.typography.labelMedium,
                            textAlign = TextAlign.Center,
                            maxLines = 1,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            overflow = TextOverflow.Ellipsis,
                        )
                        SizeHeight(8.dp)
                        Row(verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.spacedBy(4.dp)) {
                            if (item.role == ChatGroupMember.ROLE_OWNER) {
                                ChatGroupOwner()
                            }
                            if (item.role == ChatGroupMember.ROLE_ADMIN) {
                                ChatGroupAdmin()
                            }
                            GenderAgeTag(item.user)
//                            UserLevelWidget(item.)
                        }
                    }
                }
            }
            item {
                SizeHeight(120.dp)
            }
        }
    }
}