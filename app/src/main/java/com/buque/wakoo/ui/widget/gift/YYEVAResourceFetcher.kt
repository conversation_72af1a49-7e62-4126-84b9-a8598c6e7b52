package com.buque.wakoo.ui.widget.gift

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import coil3.ImageLoader
import coil3.request.ImageRequest
import coil3.request.transformations
import coil3.toBitmap
import coil3.transform.CircleCropTransformation
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.user.IUserDecorations
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.ext.createBitmapFromComposable
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.widget.ExpLevelWidget
import com.buque.wakoo.ui.widget.UserLevelWidget
import com.buque.wakoo.utils.LogUtils
import com.yy.yyeva.inter.IEvaFetchResource
import com.yy.yyeva.mix.EvaResource
import com.yy.yyeva.mix.EvaSrc
import kotlinx.coroutines.runBlocking

class YYEVAResourceFetcher(
    val params: Map<String, Any?> = mapOf(),
    val context: Context? = null,
) : IEvaFetchResource {
    private val bmpSet = hashSetOf<Bitmap?>()

    companion object {
        val imageLoader = ImageLoader(WakooApplication.instance)
    }

    /**
     *  在此释放bitmap, 但是coil或其他工具 代管理的bitmap不能释放, 这样会导致下次在进入时图片不显示
     */
    override fun releaseSrc(resources: List<EvaResource>) {
        synchronized(bmpSet) {
            bmpSet.forEach {
                it?.let {
                    if (!it.isRecycled) {
                        it.recycle()
                    }
                }
            }
            bmpSet.clear()
        }
    }

    override fun setImage(
        resource: EvaResource,
        result: (Bitmap?, EvaSrc.FitType?) -> Unit,
    ) {
        val tag = resource.tag.trim()
        val user = params.get("user") as User
        try {
            when (tag) {
                "avatar" -> { // 头像
                    if (context != null) {
                        // 使用Coil3同步加载图片
                        val bitmap = loadAvatarBitmapSync(user.avatar, context)
                        if (bitmap != null) {
                            result(bitmap, null)
                        } else {
                            result(null, null)
                        }
                    } else {
                        result(null, null)
                    }
                }

                "level" -> { // 等级
                    if (context != null && user is IUserDecorations) {
                        createBitmapFromComposable(
                            context,
                            content = {
                                if (SelfUser?.isJP == true) {
                                    ExpLevelWidget(user.wealthLevel)
                                } else {
                                    UserLevelWidget(user.level)
                                }
                            },
                            callback = { bmp ->
                                // 在此保存bmp, 在资源释放时进行回收
                                synchronized(bmpSet) {
                                    bmpSet.add(bmp)
                                }
                                result(bmp, null)
                            },
                        )
                    } else {
                        result(null, null)
                    }
                }

                else -> {
                    result(null, null)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            result(null, null)
        }
    }

    /**
     * 使用Coil3同步加载图片并转换成bitmap
     * 支持从内存、磁盘、网络加载
     *
     * @param url 图片URL
     * @param context 上下文
     * @return Bitmap对象，加载失败返回null
     */
    private fun loadAvatarBitmapSync(
        url: String,
        context: Context,
    ): Bitmap? =
        try {
            runBlocking {
                val request =
                    ImageRequest
                        .Builder(context)
                        .data(url)
                        .transformations(CircleCropTransformation())
                        .build()

                val result = imageLoader.execute(request)
                when (result) {
                    is coil3.request.SuccessResult -> {
                        LogUtils.w("loadImageSync: 加载成功 - $url")
                        result.image.toBitmap()
                    }

                    is coil3.request.ErrorResult -> {
                        LogUtils.w("loadImageSync: 加载失败 - $url, error: ${result.throwable}")
                        null
                    }
                }
            }
        } catch (e: Exception) {
            LogUtils.w("loadImageSync: 异常 - $url, error: ${e.message}")
            null
        }

    override fun setText(
        resource: EvaResource,
        result: (EvaResource) -> Unit,
    ) {
        val tag = resource.tag.trim()
        try {
            when (tag) {
                "nickname" -> {
                    val user = params.get("user") as User
                    resource.text = user.name
                    resource.textColor = Color.parseColor("#fffffe")
                    resource.textAlign = "left"
                    resource.fontSize = 22
                    result(resource)
                }

                "customText1" -> {
                    resource.text = "进入聊天室".localized
                    resource.textColor = Color.parseColor("#fffffe")
                    resource.textAlign = "left"
                    resource.fontSize = 22
                    result(resource)
                }

                else -> {
                    result(resource)
                }
            }
        } catch (e: Exception) {
            result(resource)
        }
    }
}
