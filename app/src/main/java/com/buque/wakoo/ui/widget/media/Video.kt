package com.buque.wakoo.ui.widget.media

import androidx.annotation.OptIn
import androidx.compose.foundation.AndroidEmbeddedExternalSurface
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.layer.drawLayer
import androidx.compose.ui.graphics.rememberGraphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.ui.compose.modifiers.resizeWithContentScale
import androidx.media3.ui.compose.state.rememberPresentationState
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.video.videoFrameIndex
import com.buque.wakoo.ui.widget.media.manager.PlayMediaItem

@Composable
fun VideoMediaView(
    state: PlayMediaItem.Video,
    modifier: Modifier,
) {
    VideoPlayer(
        state = state,
        modifier = modifier,
    )
}

@OptIn(UnstableApi::class)
@Composable
private fun VideoPlayer(
    state: PlayMediaItem.Video,
    modifier: Modifier = Modifier,
) {
    val graphicsLayer = rememberGraphicsLayer()
    val alignment = state.alignment
    val contentScale = state.contentScale

    Box(modifier = modifier) {
        // Note its important the embedded Surface is removed from the composition when it is scrolled
        // off screen
        if (state.canShowVideo) {
            PlayingVideo(
                player = state.realPlayer!!,
                contentScale = contentScale,
                modifier =
                    Modifier
                        .fillMaxSize()
                        .drawWithContent {
                            graphicsLayer.record {
                                <EMAIL>()
                            }
                            drawLayer(graphicsLayer)
                        },
            )
        }
        if (state.canShowStill) {
            VideoStill(
                lastBitmap = state.videoStill,
                url = state.source,
                modifier = Modifier.fillMaxSize(),
                alignment = alignment,
                contentScale = contentScale,
            )
        }

        // Capture a still frame from the video to use as a stand in when buffering playback
        if (!state.playWhenReadyFlag && state.renderedFirstFrame) {
            LaunchedEffect(Unit) {
                if (!state.playWhenReadyFlag &&
                    state.renderedFirstFrame &&
                    graphicsLayer.size.height != 0 &&
                    graphicsLayer.size.width != 0
                ) {
                    state.videoStill = graphicsLayer.toImageBitmap()
                }
            }
        }

    }
    DisposableEffect(graphicsLayer) {
        state.isActive = true
        onDispose {
            state.renderedFirstFrame = false
            state.isActive = false
        }
    }
}

@Composable
private fun VideoStill(
    lastBitmap: ImageBitmap?,
    url: String?,
    modifier: Modifier,
    alignment: Alignment,
    contentScale: ContentScale,
) {
    when (lastBitmap) {
        null -> {
            val context = LocalContext.current
            AsyncImage(
                modifier = modifier,
                model = ImageRequest.Builder(context)
                    .data(url)
                    .videoFrameIndex(0) // 尝试提取视频的第0毫秒的帧
                    .build(),
                contentDescription = null,
                alignment = alignment,
                contentScale = contentScale,
            )
        }

        else ->
            Image(
                modifier = modifier,
                bitmap = lastBitmap,
                contentDescription = null,
                alignment = alignment,
                contentScale = contentScale,
            )
    }
}

@OptIn(UnstableApi::class)
@Composable
private fun PlayingVideo(
    modifier: Modifier,
    player: Player,
    contentScale: ContentScale,
) {
    val presentationState = rememberPresentationState(player)
    val scaledModifier = modifier.resizeWithContentScale(contentScale, presentationState.videoSizeDp)
    val alpha by remember {
        derivedStateOf {
            if (presentationState.videoSizeDp == null) 0f else 1f
        }
    }
    AndroidEmbeddedExternalSurface(modifier = scaledModifier.alpha(alpha)) {
        onSurface { createdSurface, _, _ ->
            createdSurface.onDestroyed {
                player.clearVideoSurface()
            }
            player.setVideoSurface(createdSurface)
        }
    }
}

