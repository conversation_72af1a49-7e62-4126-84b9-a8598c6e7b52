package com.buque.wakoo.ui.screens.chatgroup.tasks.models


import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * ```
 * [2.40.0](https://d03r3qddlre.feishu.cn/docx/PCT4diU3Gov1ynxrtrOc93PfnRg?from=from_copylink)
 * ```
 */
interface GroupTaskApi {

    companion object {
        fun getInstance() = ApiClient.createuserApiService<GroupTaskApi>()
    }

    /**
     *获取部落活跃任务信息
     */
    @GET("api/xya/group/v1/group/active_mission/infos")
    suspend fun getActiveTask(): ApiResponse<ActiveTaskInfo>


    @POST("api/xya/group/v1/group/active_mission/check_in")
    suspend fun checkIn(): ApiResponse<JsonObject>


    /**
     * 去完成跳转链接
     * ```
     * {
     *     "jump_link": ""
     * }
     * ```
     */
    @GET("api/xya/group/v1/group/active_mission/to_finish")
    suspend fun finishTask(@Query("mission_id") taskId: Int): ApiResponse<JsonObject>

    /**
     * 领取部落活跃任务奖励
     */
    @POST("api/xya/group/v1/group/active_mission/collect_reward")
    suspend fun collectReward(@Body map: Map<String, String>): ApiResponse<TribeActiveInfo>


    /**
     * 部落宝箱信息
     */
    @GET("api/xya/group/v1/group/bin/info")
    suspend fun tribeBoxInfo(): ApiResponse<TribeBoxInfo>

    /**
     * 部落 CP 任务
     */
    @GET("api/xya/group/v1/group/cp_mission/infos")
    suspend fun tribeCPTaskInfo(): ApiResponse<TribeCPTaskInfo>

    /**
     * 部落 CP 任务 可完成的用户/CP
     */
    @GET("api/xya/group/v1/group/cp_mission/users")
    suspend fun getUserList(
        @Query("mission_type") taskType: Int,
        @Query("last_id") lastId: Int
    ): ApiResponse<UserResult>


    @POST("api/xya/group/v1/group/bin/collect_reward")
    suspend fun collectBoxReward(): ApiResponse<JsonObject>
}