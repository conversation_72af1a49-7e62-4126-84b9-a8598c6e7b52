package com.buque.wakoo.ui.screens.crony

import com.buque.wakoo.app.AppJson
import com.buque.wakoo.ext.getOrNull
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.executeApiCallExpectingData
import kotlinx.serialization.json.decodeFromJsonElement

class CronyRepo {
    private val api = ApiClient.createuserApiService<CronyApi>()


    suspend fun getTags(region:Int) = executeApiCallExpectingData {
        api.getTags(region)
    }.map { obj ->
        obj.getOrNull("tags")?.let { AppJson.decodeFromJsonElement<List<CronyTag>>(it) }.orEmpty()
    }

    suspend fun invite(uid: String, tagId: Int) = executeApiCallExpectingData {
        api.inviteUser(
            map = mapOf(
                "relative_uid" to uid,
                "relative_tag_id" to tagId.toString()
            )
        )
    }

    suspend fun accept(id: Int) = executeApiCallExpectingData {
        api.acceptInvite(mapOf("invited_id" to id.toString()))
    }

    suspend fun buyRelationshipSeat(count: Int) = executeApiCallExpectingData {
        api.buyRelationshipSeat(mapOf("count" to count))
    }


    suspend fun getSettings(userId: String) = executeApiCallExpectingData {
        api.getSettings(userId)
    }

    suspend fun getRelations(userId: String, lastId: Int = 0) = executeApiCallExpectingData {
        api.getRelationshipLabelList(userId, lastId)
    }.map { obj ->
        obj.getOrNull("relatives")?.let { el -> AppJson.decodeFromJsonElement<List<CronyItem>>(el) }.orEmpty()
    }

    suspend fun breakUp(id:Int) = executeApiCallExpectingData {
        api.breakUp(mapOf("relationship_id" to id))
    }
}