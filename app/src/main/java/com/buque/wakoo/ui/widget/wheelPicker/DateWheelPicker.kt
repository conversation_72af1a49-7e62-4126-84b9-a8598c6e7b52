package com.buque.wakoo.ui.widget.wheelPicker

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.buque.wakoo.manager.localizedFormat
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.daysUntil
import kotlinx.datetime.minus
import kotlinx.datetime.number
import kotlinx.datetime.plus
import kotlinx.datetime.todayIn
import kotlin.time.Clock
import kotlin.time.ExperimentalTime

/**
 * 日期滚轮选择器组件
 * 支持年、月、日三级联动选择
 *
 * @param selectedDate 当前选中的日期
 * @param onDateChanged 日期改变回调
 * @param modifier 修饰符
 * @param yearRange 年份范围
 * @param enabledDateRange 可用日期范围
 * @param isMonthInfinite 月份是否启用无限循环
 * @param isDayInfinite 日期是否启用无限循环（开启后每月都显示31天，不存在的日期标记为不可用）
 * @param itemHeight 每个项的高度
 * @param visibleItemsCount 可见项数量
 * @param itemTextStyle 项文本样式
 * @param selectedTextColor 选中项文本颜色
 * @param unselectedTextColor 未选中项文本颜色
 * @param unEnabledTextColor 不可用项文本颜色
 * @param dividerConfig 分割线配置
 * @param effect3DConfig 3D效果配置
 */
@OptIn(ExperimentalTime::class)
@Composable
fun DateWheelPicker(
    modifier: Modifier = Modifier,
    selectedDate: LocalDate = Clock.System.todayIn(TimeZone.currentSystemDefault()),
    onDateChanged: (LocalDate) -> Unit = {},
    onDateChangeWithSanitization: (LocalDate) -> LocalDate? = { null },
    yearRange: IntRange = (1950..2100),
    enabledDateRange: ClosedRange<LocalDate> = remember { WheelPickerUtils.createFullDateRange(yearRange) },
    isMonthInfinite: Boolean = false,
    isDayInfinite: Boolean = false,
    space: Dp = 0.dp,
    itemHeight: Dp = WheelPickerDefaults.DefaultItemHeight,
    visibleItemsCount: Int = WheelPickerDefaults.DefaultVisibleItemsCount,
    itemTextStyle: TextStyle = WheelPickerDefaults.defaultTextStyle(),
    selectedTextColor: Color = WheelPickerDefaults.defaultSelectedTextColor(),
    unselectedTextColor: Color = WheelPickerDefaults.defaultUnselectedTextColor(),
    unEnabledTextColor: Color = WheelPickerDefaults.defaultUnEnabledTextColor(),
    dividerConfig: WheelPickerDefaults.DividerConfig = WheelPickerDefaults.defaultDividerConfig(),
    effect3DConfig: WheelPickerDefaults.Effect3DConfig = WheelPickerDefaults.default3DEffectConfig(),
) {
    // 年份数据
    val years = remember(yearRange) { yearRange.toList() }

    // 月份数据
    val months = remember { (1..12).toList() }

    // 当前选中的年月，用于计算天数
    var currentYear by remember(selectedDate) { mutableIntStateOf(selectedDate.year) }
    var currentMonth by remember(selectedDate) { mutableIntStateOf(selectedDate.month.number) }

    // 根据年月计算天数
    val days =
        remember(currentYear, currentMonth, isDayInfinite) {
            if (isDayInfinite) {
                // 无限循环模式：固定显示31天
                (1..31).toList()
            } else {
                // 正常模式：根据实际月份天数
                val daysInMonth =
                    LocalDate(currentYear, currentMonth, 1).daysUntil(
                        LocalDate(currentYear, currentMonth, 1).plus(1, DateTimeUnit.MONTH),
                    )
                (1..daysInMonth).toList()
            }
        }

    // 创建状态
    val yearState =
        rememberWheelPickerState(
            initialIndex = years.indexOf(selectedDate.year).coerceAtLeast(0),
            itemCount = years.size,
            isItemEnabled = { index ->
                val year = years.getOrNull(index) ?: return@rememberWheelPickerState false
                // 检查年份是否在可用范围内
                val yearStart = LocalDate(year, 1, 1)
                val yearEnd = LocalDate(year, 12, 31)
                yearStart <= enabledDateRange.endInclusive && yearEnd >= enabledDateRange.start
            },
        )

    val monthState =
        rememberWheelPickerState(
            initialIndex = selectedDate.monthNumber - 1,
            itemCount = months.size,
            isInfinite = isMonthInfinite,
            isItemEnabled = { index ->
                val month = months.getOrNull(index) ?: return@rememberWheelPickerState false
                val year = years.getOrNull(yearState.snappedIndex) ?: currentYear
                // 检查年月是否在可用范围内
                val monthStart = LocalDate(year, month, 1)
                val monthEnd = LocalDate(year, month, 1).plus(1, DateTimeUnit.MONTH).minus(1, DateTimeUnit.DAY)
                monthStart <= enabledDateRange.endInclusive && monthEnd >= enabledDateRange.start
            },
        )

    val dayState =
        rememberWheelPickerState(
            initialIndex = selectedDate.day - 1,
            itemCount = days.size,
            isInfinite = isDayInfinite,
            isItemEnabled = { index ->
                val day = days.getOrNull(index) ?: return@rememberWheelPickerState false
                val year = years.getOrNull(yearState.snappedIndex) ?: currentYear
                val month = months.getOrNull(monthState.snappedIndex) ?: currentMonth

                // 检查日期是否存在（针对无限循环模式）
                val isValidDate =
                    if (isDayInfinite) {
                        try {
                            LocalDate(year, month, day)
                            true
                        } catch (e: Exception) {
                            false
                        }
                    } else {
                        true // 正常模式下天数已经是正确的
                    }

                // 检查日期是否在可用范围内
                val isInRange =
                    if (isValidDate) {
                        WheelPickerUtils.isDateInRange(year, month, day, enabledDateRange)
                    } else {
                        false
                    }

                isValidDate && isInRange
            },
        )

    // 监听年份变化 - 只在滚动停止后触发
    LaunchedEffect(yearState.snappedIndex) {
        val newYear = years.getOrNull(yearState.snappedIndex) ?: currentYear
        if (newYear != currentYear) {
            currentYear = newYear

            // 检查当前选中的月日是否还在可用范围内
            val currentSelectedMonth = months.getOrNull(monthState.snappedIndex) ?: currentMonth
            val currentSelectedDay = days.getOrNull(dayState.snappedIndex) ?: 1

            val testDate =
                try {
                    LocalDate(newYear, currentSelectedMonth, currentSelectedDay)
                } catch (e: Exception) {
                    null
                }

            val finalDate =
                if (testDate == null || !WheelPickerUtils.isDateInRange(testDate, enabledDateRange)) {
                    // 当前日期不可用，需要调整到可用日期
                    val fallbackDate =
                        findNearestValidDate(newYear, currentSelectedMonth, currentSelectedDay, enabledDateRange)

                    // 更新月份和日期
                    val targetMonthIndex = fallbackDate.month.number - 1
                    val targetDayIndex = fallbackDate.day - 1

                    if (targetMonthIndex != monthState.snappedIndex) {
                        monthState.animateScrollToItem(targetMonthIndex)
                    }
                    if (targetDayIndex != dayState.snappedIndex) {
                        dayState.animateScrollToItem(targetDayIndex)
                    }

                    currentMonth = fallbackDate.month.number

                    fallbackDate
                } else {
                    testDate
                }
            val adjustLocalDate = onDateChangeWithSanitization(finalDate)

            if (adjustLocalDate != null) {
                val targetYearIndex = adjustLocalDate.year - 1
                val targetMonthIndex = adjustLocalDate.month.number - 1
                val targetDayIndex = adjustLocalDate.day - 1

                if (targetYearIndex != yearState.snappedIndex) {
                    monthState.animateScrollToItem(targetYearIndex)
                }
                if (targetMonthIndex != monthState.snappedIndex) {
                    monthState.animateScrollToItem(targetMonthIndex)
                }
                if (targetDayIndex != dayState.snappedIndex) {
                    dayState.animateScrollToItem(targetDayIndex)
                }

                currentYear = adjustLocalDate.year
                currentMonth = adjustLocalDate.month.number
            }

            onDateChanged(adjustLocalDate ?: finalDate)
        }
    }

    // 监听月份变化 - 只在滚动停止后触发
    LaunchedEffect(monthState.snappedIndex) {
        val newMonth = months.getOrNull(monthState.snappedIndex) ?: currentMonth
        if (newMonth != currentMonth) {
            currentMonth = newMonth

            // 检查当前选中的日期是否还在可用范围内
            val currentSelectedDay = days.getOrNull(dayState.snappedIndex) ?: 1

            val testDate =
                try {
                    LocalDate(currentYear, newMonth, currentSelectedDay)
                } catch (e: Exception) {
                    null
                }

            if (testDate == null || !WheelPickerUtils.isDateInRange(testDate, enabledDateRange)) {
                // 当前日期不可用，需要调整到可用日期
                val fallbackDate = findNearestValidDate(currentYear, newMonth, currentSelectedDay, enabledDateRange)

                // 更新日期
                val targetDayIndex = fallbackDate.dayOfMonth - 1
                if (targetDayIndex != dayState.snappedIndex) {
                    dayState.animateScrollToItem(targetDayIndex)
                }

                onDateChanged(fallbackDate)
            } else {
                // 当前日期可用，正常触发回调
                onDateChanged(testDate)
            }
        }
    }

    // 监听日期变化 - 只在滚动停止后触发
    LaunchedEffect(dayState.snappedIndex) {
        val newDay = dayState.snappedIndex + 1
        val testDate =
            try {
                LocalDate(currentYear, currentMonth, newDay)
            } catch (e: Exception) {
                null
            }

        if (testDate != null && WheelPickerUtils.isDateInRange(testDate, enabledDateRange)) {
            onDateChanged(testDate)
        }
    }

    // 当天数变化时，调整日期选择器
    LaunchedEffect(days.size) {
        dayState.updateData(days.size, false)
        // 如果当前选中的日期超出了新月份的天数范围，调整到最后一天
        if (dayState.snappedIndex >= days.size) {
            dayState.animateScrollToItem(days.size - 1)
        }
    }

    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(space),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // 年份选择器
        WheelPicker(
            items = years,
            state = yearState,
            modifier = Modifier.weight(1.2f),
            visibleItemsCount = visibleItemsCount,
            itemHeight = itemHeight,
            itemTextStyle = itemTextStyle,
            selectedTextColor = selectedTextColor,
            unselectedTextColor = unselectedTextColor,
            unEnabledTextColor = unEnabledTextColor,
            dividerConfig = dividerConfig,
            effect3DConfig = effect3DConfig,
            itemContent = { year, isSelected, isEnabled ->
                Text(
                    text = "%s年".localizedFormat(year),
                    modifier =
                        Modifier
                            .padding(end = 10.dp)
                            .align(Alignment.CenterEnd),
                    style = itemTextStyle,
                    color =
                        when {
                            !isEnabled -> unEnabledTextColor
                            isSelected -> selectedTextColor
                            else -> unselectedTextColor
                        },
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                )
            },
        )

        // 月份选择器
        WheelPicker(
            items = months,
            state = monthState,
            modifier = Modifier.weight(1f),
            visibleItemsCount = visibleItemsCount,
            itemHeight = itemHeight,
            itemTextStyle = itemTextStyle,
            selectedTextColor = selectedTextColor,
            unselectedTextColor = unselectedTextColor,
            unEnabledTextColor = unEnabledTextColor,
            dividerConfig = dividerConfig,
            effect3DConfig = effect3DConfig,
            isInfinite = isMonthInfinite,
            itemContent = { month, isSelected, isEnabled ->
                Text(
                    text = "%s月".localizedFormat(month),
                    style = itemTextStyle,
                    color =
                        when {
                            !isEnabled -> unEnabledTextColor
                            isSelected -> selectedTextColor
                            else -> unselectedTextColor
                        },
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                )
            },
        )

        // 日期选择器
        WheelPicker(
            items = days,
            state = dayState,
            modifier = Modifier.weight(1.2f),
            visibleItemsCount = visibleItemsCount,
            itemHeight = itemHeight,
            itemTextStyle = itemTextStyle,
            selectedTextColor = selectedTextColor,
            unselectedTextColor = unselectedTextColor,
            unEnabledTextColor = unEnabledTextColor,
            dividerConfig = dividerConfig,
            effect3DConfig = effect3DConfig,
            isInfinite = isDayInfinite,
            itemContent = { day, isSelected, isEnabled ->
                Text(
                    text = "%s日".localizedFormat(day),
                    modifier =
                        Modifier
                            .padding(start = 10.dp)
                            .align(Alignment.CenterStart),
                    style = itemTextStyle,
                    color =
                        when {
                            !isEnabled -> unEnabledTextColor
                            isSelected -> selectedTextColor
                            else -> unselectedTextColor
                        },
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                )
            },
        )
    }
}

/**
 * 创建安全的日期，避免无效日期（如2月30日）
 */
private fun createSafeDate(
    year: Int,
    month: Int,
    day: Int,
): LocalDate {
    val daysInMonth =
        LocalDate(year, month, 1).daysUntil(
            LocalDate(year, month, 1).plus(1, DateTimeUnit.MONTH),
        )
    val safeDay = day.coerceIn(1, daysInMonth)
    return LocalDate(year, month, safeDay)
}

/**
 * 查找最近的有效日期（在可用范围内）
 */
private fun findNearestValidDate(
    year: Int,
    month: Int,
    day: Int,
    enabledDateRange: ClosedRange<LocalDate>,
): LocalDate {
    // 首先尝试创建安全日期
    val safeDate = createSafeDate(year, month, day)

    // 如果在可用范围内，直接返回
    if (WheelPickerUtils.isDateInRange(safeDate, enabledDateRange)) {
        return safeDate
    }

    // 如果超出范围，返回范围边界
    return when {
        safeDate < enabledDateRange.start -> enabledDateRange.start
        safeDate > enabledDateRange.endInclusive -> enabledDateRange.endInclusive
        else -> safeDate
    }
}

/**
 * 时间滚轮选择器组件
 * 支持时、分、秒三级选择
 *
 * @param selectedHour 当前选中的小时
 * @param selectedMinute 当前选中的分钟
 * @param selectedSecond 当前选中的秒
 * @param onTimeChanged 时间改变回调
 * @param modifier 修饰符
 * @param is24Hour 是否使用24小时制
 * @param showSeconds 是否显示秒
 * @param itemHeight 每个项的高度
 * @param visibleItemsCount 可见项数量
 * @param itemTextStyle 项文本样式
 * @param dividerConfig 分割线配置
 * @param effect3DConfig 3D效果配置
 */
@Composable
fun TimeWheelPicker(
    modifier: Modifier = Modifier,
    selectedHour: Int = 0,
    selectedMinute: Int = 0,
    selectedSecond: Int = 0,
    onTimeChanged: (hour: Int, minute: Int, second: Int) -> Unit = { _, _, _ -> },
    is24Hour: Boolean = true,
    showSeconds: Boolean = true,
    itemHeight: Dp = WheelPickerDefaults.DefaultItemHeight,
    visibleItemsCount: Int = WheelPickerDefaults.DefaultVisibleItemsCount,
    itemTextStyle: TextStyle = WheelPickerDefaults.defaultTextStyle(),
    dividerConfig: WheelPickerDefaults.DividerConfig = WheelPickerDefaults.defaultDividerConfig(),
    effect3DConfig: WheelPickerDefaults.Effect3DConfig = WheelPickerDefaults.default3DEffectConfig(),
) {
    // 小时数据
    val hours =
        remember(is24Hour) {
            if (is24Hour) (0..23).toList() else (1..12).toList()
        }

    // 分钟和秒数据
    val minutes = remember { (0..59).toList() }
    val seconds = remember { (0..59).toList() }

    // 创建状态
    val hourState =
        rememberWheelPickerState(
            initialIndex = if (is24Hour) selectedHour else (selectedHour - 1).coerceIn(0, 11),
            itemCount = hours.size,
            isInfinite = true,
        )

    val minuteState =
        rememberWheelPickerState(
            initialIndex = selectedMinute,
            itemCount = minutes.size,
            isInfinite = true,
        )

    val secondState =
        rememberWheelPickerState(
            initialIndex = selectedSecond,
            itemCount = seconds.size,
            isInfinite = true,
        )

    // 监听时间变化 - 只在滚动停止后触发
    LaunchedEffect(hourState.snappedIndex, minuteState.snappedIndex, secondState.snappedIndex) {
        val hour = hours.getOrNull(hourState.snappedIndex) ?: 0
        val minute = minutes.getOrNull(minuteState.snappedIndex) ?: 0
        val second = seconds.getOrNull(secondState.snappedIndex) ?: 0
        onTimeChanged(hour, minute, second)
    }

    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // 小时选择器
        WheelPicker(
            items = hours,
            state = hourState,
            modifier = Modifier.weight(1f),
            visibleItemsCount = visibleItemsCount,
            itemHeight = itemHeight,
            itemTextStyle = itemTextStyle,
            dividerConfig = dividerConfig,
            effect3DConfig = effect3DConfig,
            isInfinite = true,
            itemContent = { hour, isSelected, isEnabled ->
                Text(
                    text = String.format("%02d时", hour),
                    style = itemTextStyle,
                    color =
                        when {
                            !isEnabled -> WheelPickerDefaults.defaultUnselectedTextColor().copy(alpha = 0.3f)
                            isSelected -> WheelPickerDefaults.defaultSelectedTextColor()
                            else -> WheelPickerDefaults.defaultUnselectedTextColor()
                        },
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                )
            },
        )

        // 分钟选择器
        WheelPicker(
            items = minutes,
            state = minuteState,
            modifier = Modifier.weight(1f),
            visibleItemsCount = visibleItemsCount,
            itemHeight = itemHeight,
            itemTextStyle = itemTextStyle,
            dividerConfig = dividerConfig,
            effect3DConfig = effect3DConfig,
            isInfinite = true,
            itemContent = { minute, isSelected, isEnabled ->
                Text(
                    text = String.format("%02d分", minute),
                    style = itemTextStyle,
                    color =
                        when {
                            !isEnabled -> WheelPickerDefaults.defaultUnselectedTextColor().copy(alpha = 0.3f)
                            isSelected -> WheelPickerDefaults.defaultSelectedTextColor()
                            else -> WheelPickerDefaults.defaultUnselectedTextColor()
                        },
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                )
            },
        )

        // 秒选择器（可选）
        if (showSeconds) {
            WheelPicker(
                items = seconds,
                state = secondState,
                modifier = Modifier.weight(1f),
                visibleItemsCount = visibleItemsCount,
                itemHeight = itemHeight,
                itemTextStyle = itemTextStyle,
                dividerConfig = dividerConfig,
                effect3DConfig = effect3DConfig,
                isInfinite = true,
                itemContent = { second, isSelected, isEnabled ->
                    Text(
                        text = String.format("%02d秒", second),
                        style = itemTextStyle,
                        color =
                            when {
                                !isEnabled -> WheelPickerDefaults.defaultUnselectedTextColor().copy(alpha = 0.3f)
                                isSelected -> WheelPickerDefaults.defaultSelectedTextColor()
                                else -> WheelPickerDefaults.defaultUnselectedTextColor()
                            },
                        textAlign = TextAlign.Center,
                        maxLines = 1,
                    )
                },
            )
        }
    }
}
