package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.UnLock: ImageVector
    get() {
        if (Unlock != null) {
            return Unlock!!
        }
        Unlock =
            ImageVector
                .Builder(
                    name = "Unlock",
                    defaultWidth = 24.dp,
                    defaultHeight = 24.dp,
                    viewportWidth = 24f,
                    viewportHeight = 24f,
                ).apply {
                    path(fill = SolidColor(Color(0xFF111111))) {
                        moveTo(7f, 10f)
                        horizontalLineTo(20f)
                        curveTo(20.552f, 10f, 21f, 10.448f, 21f, 11f)
                        verticalLineTo(21f)
                        curveTo(21f, 21.552f, 20.552f, 22f, 20f, 22f)
                        horizontalLineTo(4f)
                        curveTo(3.448f, 22f, 3f, 21.552f, 3f, 21f)
                        verticalLineTo(11f)
                        curveTo(3f, 10.448f, 3.448f, 10f, 4f, 10f)
                        horizontalLineTo(5f)
                        verticalLineTo(9f)
                        curveTo(5f, 5.134f, 8.134f, 2f, 12f, 2f)
                        curveTo(14.741f, 2f, 17.113f, 3.575f, 18.262f, 5.869f)
                        lineTo(16.473f, 6.763f)
                        curveTo(15.652f, 5.125f, 13.958f, 4f, 12f, 4f)
                        curveTo(9.239f, 4f, 7f, 6.239f, 7f, 9f)
                        verticalLineTo(10f)
                        close()
                        moveTo(5f, 12f)
                        verticalLineTo(20f)
                        horizontalLineTo(19f)
                        verticalLineTo(12f)
                        horizontalLineTo(5f)
                        close()
                        moveTo(10f, 15f)
                        horizontalLineTo(14f)
                        verticalLineTo(17f)
                        horizontalLineTo(10f)
                        verticalLineTo(15f)
                        close()
                    }
                }.build()

        return Unlock!!
    }

@Suppress("ObjectPropertyName")
private var Unlock: ImageVector? = null
