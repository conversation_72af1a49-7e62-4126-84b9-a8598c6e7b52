package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.IcMissionProgress: ImageVector
    get() {
        if (_IcMissionProgress != null) {
            return _IcMissionProgress!!
        }
        _IcMissionProgress = ImageVector.Builder(
            name = "IcMissionProgress",
            defaultWidth = 20.dp,
            defaultHeight = 20.dp,
            viewportWidth = 20f,
            viewportHeight = 20f
        ).apply {
            path(fill = SolidColor(Color(0xFF111111))) {
                moveTo(5.834f, 2.5f)
                verticalLineTo(0.833f)
                horizontalLineTo(7.5f)
                verticalLineTo(2.5f)
                horizontalLineTo(12.5f)
                verticalLineTo(0.833f)
                horizontalLineTo(14.167f)
                verticalLineTo(2.5f)
                horizontalLineTo(17.5f)
                curveTo(17.961f, 2.5f, 18.334f, 2.873f, 18.334f, 3.333f)
                verticalLineTo(7.5f)
                horizontalLineTo(16.667f)
                verticalLineTo(4.167f)
                horizontalLineTo(14.167f)
                verticalLineTo(5.833f)
                horizontalLineTo(12.5f)
                verticalLineTo(4.167f)
                horizontalLineTo(7.5f)
                verticalLineTo(5.833f)
                horizontalLineTo(5.834f)
                verticalLineTo(4.167f)
                horizontalLineTo(3.334f)
                verticalLineTo(15.833f)
                horizontalLineTo(8.334f)
                verticalLineTo(17.5f)
                horizontalLineTo(2.5f)
                curveTo(2.04f, 17.5f, 1.667f, 17.127f, 1.667f, 16.667f)
                verticalLineTo(3.333f)
                curveTo(1.667f, 2.873f, 2.04f, 2.5f, 2.5f, 2.5f)
                horizontalLineTo(5.834f)
                close()
                moveTo(14.167f, 10f)
                curveTo(12.326f, 10f, 10.834f, 11.493f, 10.834f, 13.333f)
                curveTo(10.834f, 15.174f, 12.326f, 16.667f, 14.167f, 16.667f)
                curveTo(16.008f, 16.667f, 17.5f, 15.174f, 17.5f, 13.333f)
                curveTo(17.5f, 11.493f, 16.008f, 10f, 14.167f, 10f)
                close()
                moveTo(9.167f, 13.333f)
                curveTo(9.167f, 10.572f, 11.406f, 8.333f, 14.167f, 8.333f)
                curveTo(16.928f, 8.333f, 19.167f, 10.572f, 19.167f, 13.333f)
                curveTo(19.167f, 16.095f, 16.928f, 18.333f, 14.167f, 18.333f)
                curveTo(11.406f, 18.333f, 9.167f, 16.095f, 9.167f, 13.333f)
                close()
                moveTo(13.334f, 10.833f)
                verticalLineTo(13.679f)
                lineTo(15.244f, 15.589f)
                lineTo(16.423f, 14.411f)
                lineTo(15f, 12.988f)
                verticalLineTo(10.833f)
                horizontalLineTo(13.334f)
                close()
            }
        }.build()

        return _IcMissionProgress!!
    }

@Suppress("ObjectPropertyName")
private var _IcMissionProgress: ImageVector? = null
