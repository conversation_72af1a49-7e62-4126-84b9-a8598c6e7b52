package com.buque.wakoo.ui.screens.chatgroup.member

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.chatgroup.ChatGroupMember
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.icons.ArrDown
import com.buque.wakoo.ui.icons.ArrUp
import com.buque.wakoo.ui.icons.ArrowNone
import com.buque.wakoo.ui.screens.chatgroup.chat.ChatGroupAdmin
import com.buque.wakoo.ui.screens.chatgroup.chat.ChatGroupOwner
import com.buque.wakoo.ui.screens.chatgroup.member.OnlineMemberApi.Companion.ORDER_BY_TOTAL_ACTIVE_POINT_ASC
import com.buque.wakoo.ui.screens.chatgroup.member.OnlineMemberApi.Companion.ORDER_BY_TOTAL_ACTIVE_POINT_DESC
import com.buque.wakoo.ui.screens.chatgroup.member.OnlineMemberApi.Companion.ORDER_BY_WEEK_ACTIVE_POINT_ASC
import com.buque.wakoo.ui.screens.chatgroup.member.OnlineMemberApi.Companion.ORDER_BY_WEEK_ACTIVE_POINT_DESC
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.ExpLevelWidget
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.pagination.PaginateState
import com.buque.wakoo.ui.widget.state.StateListPaginateLayout

private val weightNum = 0.8f

@Composable
fun GroupMemberList(
    modifier: Modifier = Modifier,
    vm: GroupOnlineMemberVM = viewModel<GroupOnlineMemberVM>(),
) {
    val count by vm.memberCountState
    val sortType by vm.stateSortType
    LaunchedEffect(sortType) {
        if (sortType != OnlineMemberApi.NONE) {
            vm.refreshList(true)
        }
    }
    Column(
        modifier =
            Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
    ) {
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(44.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text("群组成员数量 %s".localizedFormat(count), modifier = Modifier.weight(2f), fontSize = 12.sp, color = WakooGrayText)

            TextSort(
                "周活跃度",
                modifier =
                    Modifier
                        .click(onClick = {
                            vm.setSortType(
                                if (sortType == ORDER_BY_WEEK_ACTIVE_POINT_DESC) {
                                    ORDER_BY_WEEK_ACTIVE_POINT_ASC
                                } else {
                                    ORDER_BY_WEEK_ACTIVE_POINT_DESC
                                },
                            )
                        })
                        .weight(weightNum),
                isArrowUp =
                    when (sortType) {
                        ORDER_BY_WEEK_ACTIVE_POINT_ASC -> true
                        ORDER_BY_WEEK_ACTIVE_POINT_DESC -> false
                        else -> null
                    },
            )
            TextSort(
                "总活跃度",
                modifier =
                    Modifier
                        .click(onClick = {
                            vm.setSortType(
                                if (sortType == ORDER_BY_TOTAL_ACTIVE_POINT_DESC) {
                                    ORDER_BY_TOTAL_ACTIVE_POINT_ASC
                                } else {
                                    ORDER_BY_TOTAL_ACTIVE_POINT_DESC
                                },
                            )
                        })
                        .weight(weightNum),
                isArrowUp =
                    when (sortType) {
                        ORDER_BY_TOTAL_ACTIVE_POINT_ASC -> true
                        ORDER_BY_TOTAL_ACTIVE_POINT_DESC -> false
                        else -> null
                    },
            )
        }

        StateListPaginateLayout<Int, OnlineMember, GroupOnlineMemberVM>(
            refreshEnable = false,
            viewModel = vm,
        ) { state: PaginateState<Int>, data: List<OnlineMember> ->
            LazyColumn(modifier = Modifier.fillMaxSize()) {
                items(data) { item ->
                    GroupMemberItem(item)
                }
            }
        }
    }
}

private val colorRed = Color(0xFFFF3570)

@Composable
fun TextSort(
    text: String,
    modifier: Modifier = Modifier,
    isArrowUp: Boolean? = null,
) {
    Row(modifier = modifier, horizontalArrangement = Arrangement.Center, verticalAlignment = Alignment.CenterVertically) {
        BasicText(
            text,
            autoSize = TextAutoSize.StepBased(minFontSize = 6.sp, maxFontSize = 12.sp),
            style = TextStyle(color = if (isArrowUp != null) colorRed else WakooGrayText),
        )
        SizeWidth(2.dp)
        Image(
            imageVector =
                if (isArrowUp == true) {
                    ArrUp
                } else if (isArrowUp == false) {
                    ArrDown
                } else {
                    ArrowNone
                },
            contentDescription = null,
            modifier = Modifier.size(12.dp),
        )
        SizeWidth(2.dp)
    }
}

@Preview
@Composable
private fun TextSortPreview() {
    TextSort("周活跃度")
}

@Composable
fun GroupMemberItem(
    item: OnlineMember,
    modifier: Modifier = Modifier,
) {
    val nav = LocalAppNavController.current
    Row(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(vertical = 10.dp)
                .widthIn(max = 64.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Row(modifier = Modifier.weight(2f), verticalAlignment = Alignment.CenterVertically) {
            Box(modifier = Modifier.size(48.dp)) {
                AvatarNetworkImage(
                    modifier =
                        Modifier.noEffectClick(onClick = {
                            nav.push(Route.UserProfile(item.basicUser))
                        }),
                    user = item.basicUser,
                    size = 48.dp,
                )
                if (item.isOnline) {
                    Box(
                        modifier =
                            Modifier
                                .size(14.dp)
                                .align(Alignment.BottomEnd)
                                .padding(2.dp)
                                .background(Color.White, CircleShape)
                                .padding(2.dp)
                                .background(
                                    WakooGreen,
                                    CircleShape,
                                ),
                    )
                }
            }
            SizeWidth(8.dp)
            Column {
                Text(
                    text = item.basicUser.name,
                    color = Color(0xFF111111),
                    style = MaterialTheme.typography.labelMedium,
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    overflow = TextOverflow.Ellipsis,
                )
                SizeHeight(8.dp)
                Row(verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.spacedBy(4.dp)) {
                    if (item.role == ChatGroupMember.ROLE_OWNER) {
                        ChatGroupOwner()
                    }
                    if (item.role == ChatGroupMember.ROLE_ADMIN) {
                        ChatGroupAdmin()
                    }
                    GenderAgeTag(item.basicUser)
                    ExpLevelWidget(item.userDecor)
                }
            }
        }
        Text(item.weekValue, fontSize = 12.sp, color = WakooText, modifier = Modifier.weight(weightNum), textAlign = TextAlign.Center)
        Text(item.totalValue, fontSize = 12.sp, color = WakooText, modifier = Modifier.weight(weightNum), textAlign = TextAlign.Center)
    }
}
