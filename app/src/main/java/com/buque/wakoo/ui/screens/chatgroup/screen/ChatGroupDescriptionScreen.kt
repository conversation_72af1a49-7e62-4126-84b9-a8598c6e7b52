package com.buque.wakoo.ui.screens.chatgroup.screen

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.emptyAction
import com.buque.wakoo.bean.chatgroup.ChatGroupBean
import com.buque.wakoo.bean.chatgroup.ChatGroupMember
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.toastWhenError
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.ChatGroupRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.dialog.LongTextDialog
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.ArrowLeft
import com.buque.wakoo.ui.icons.More
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.chatgroup.ChatGroupNavCtrlKey
import com.buque.wakoo.ui.screens.chatgroup.chat.ChatGroupAdmin
import com.buque.wakoo.ui.screens.chatgroup.chat.ChatGroupOwner
import com.buque.wakoo.ui.screens.chatgroup.member.GroupMemberList
import com.buque.wakoo.ui.screens.chatgroup.tasks.ui.GroupTaskContainer
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooUI
import com.buque.wakoo.ui.widget.ExpandableText
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.coordinatorlayout.CustomCollapsibleHeader
import com.buque.wakoo.ui.widget.coordinatorlayout.rememberCustomCollapsibleHeaderState
import com.buque.wakoo.ui.widget.coordinatorlayout.rememberCustomCollapsibleScrollBehavior
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.image.SquareNetworkImage
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.ui.widget.state.StateListPaginateLayout
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupOnlineMemberListViewModel
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupSettingsViewModel
import com.buque.wakoo.viewmodel.chatgroup.OnlineKey
import kotlinx.coroutines.launch

@Composable
fun ChatGroupDetailScreen(
    groupId: String,
    viewModel: ChatGroupSettingsViewModel =
        viewModel<ChatGroupSettingsViewModel>(initializer = {
            ChatGroupSettingsViewModel(groupId)
        }),
) {
    val lm = LocalLoadingManager.current
    val state by viewModel.state
    LaunchedEffect(viewModel) {
        viewModel.refreshState()
    }
    val nav = LocalAppNavController.current
    val rootNav = LocalAppNavController.root
    CStateLayout(state) {
        ChatGroupDescriptionScreen(it, onApply = {
            lm.show(null) {
                viewModel
                    .applyJoin()
                    .toastWhenError()
            }
        }, onRefresh = { viewModel.refreshState() }, onOpen = {
            if (nav.key == ChatGroupNavCtrlKey) {
                nav.pop()
            } else {
                nav.push(Route.ChatGroup(viewModel.groupId))
            }
        }, onInvite = {
            rootNav.push(Route.SelectUser(1, groupId, Route.SelectUser.SELECT_FOR_INVITE_GROUP, "邀请加入群组".localized, "邀请加入".localized))
        })
    }
}

@Composable
private fun DetailTabContent(onRefresh: OnAction = {}) {
    Column(modifier = Modifier.fillMaxSize()) {
        val pagerState = rememberPagerState(initialPage = 0) {
            2
        }
        val tabs = listOf(
            "任务".localized,
            "成员".localized
        )
        val scope = rememberCoroutineScope()
        val selectedTabIndex = pagerState.currentPage
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp), horizontalArrangement = Arrangement.spacedBy(40.dp)
        ) {
            tabs.forEachIndexed { index, title ->
                Box(
                    modifier = Modifier
                        .wrapContentWidth()
                        .click(onClick = {
                            scope.launch {
                                pagerState.scrollToPage(index)
                            }
                        })
                        .height(28.dp)
                ) {
                    val isSelected = index == selectedTabIndex
                    Text(
                        text = title,
                        color = if (isSelected) WakooText else WakooGrayText,
                        textAlign = TextAlign.Center,
                        fontSize = 14.sp,
                        modifier = Modifier
                            .click {
                                scope.launch {
                                    pagerState.scrollToPage(index)
                                }
                            }
                            .align(Alignment.Center)
                    )
                    Box(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .size(12.dp, 3.dp)
                            .background(if (isSelected) WakooText else Color.Transparent, WakooUI.Shapes.chip)
                    )
                }
            }
        }
        SizeHeight(12.dp)
        HorizontalPager(pagerState) { page ->
            when (page) {
                0 -> GroupTaskContainer(modifier = Modifier, onRefreshGroupInfo = onRefresh)
                1 -> GroupMemberList()
            }
        }
    }
}

@Composable
fun ChatGroupDescriptionScreen(
    chatGroupBean: ChatGroupBean,
    onRefresh: OnAction = {},
    onApply: () -> Unit = emptyAction,
    onInvite: OnAction = emptyAction,
    onOpen: OnAction = emptyAction,
) {
    val self = LocalSelfUserProvider.current
    val showDialog =
        rememberSaveable {
            mutableStateOf(false)
        }
    val nav = LocalAppNavController.current
    val headerState = rememberCustomCollapsibleHeaderState()
    val scrollBehavior =
        rememberCustomCollapsibleScrollBehavior(
            state = headerState.topAppBarState,
        )
    val o = LocalOnBackPressedDispatcherOwner.current
    Box(modifier = Modifier.fillMaxSize()) {
        NetworkImage(
            data = chatGroupBean.avatarUrl,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .aspectRatio(1f)
                    .drawWithContent {
                        drawContent()
                        drawRect(Color(0x99000000))
                    },
        )
        Box(modifier = Modifier.fillMaxSize()) {
            val joinState = chatGroupBean.joinState
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .nestedScroll(scrollBehavior.nestedScrollConnection),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {

                val shouldTint = headerState.collapsedFraction >= 0.8f
                val tintColor = if (shouldTint) Color.Black else Color.White
                Row(
                    modifier = Modifier
                        .background(Color.White.copy(headerState.collapsedFraction))
                        .statusBarsPadding()
                        .fillMaxWidth()
                        .height(44.dp)
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        WakooIcons.ArrowLeft, contentDescription = "back", modifier = Modifier
                            .size(24.dp)
                            .click(onClick = {
                                o?.onBackPressedDispatcher?.onBackPressed()
                            }), tintColor
                    )
                    if (shouldTint) {
                        Text(
                            chatGroupBean.name,
                            color = WakooText,
                            textAlign = TextAlign.Center,
                            fontSize = 16.sp,

                            modifier = Modifier
                                .weight(1f)
                                .padding(horizontal = 16.dp)
                                .basicMarquee(),
                            maxLines = 1,
                        )
                    } else {
                        Weight(1f)
                    }
                    Box(modifier = Modifier.size(24.dp)) {
                        if (chatGroupBean.joinState.isMember) {
                            Icon(
                                imageVector = WakooIcons.More,
                                contentDescription = null,
                                modifier = Modifier.click(onClick = {
                                    nav.push(ChatGroupRoute.GroupSettings(chatGroupBean.id))
                                }),
                                tintColor
                            )
                        }
                    }
                }
                CustomCollapsibleHeader(state = headerState, scrollBehavior = scrollBehavior, applyAlphaModifier = false) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(284.dp)
                    ) {
                        Spacer(
                            modifier = Modifier
                                .padding(top = 92.dp)
                                .fillMaxSize()
                                .background(
                                    Color.White, RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                                )
                        )
                        Column(modifier = Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally) {
                            SizeHeight(60.dp)
                            SquareNetworkImage(
                                data = chatGroupBean.avatarUrl,
                                size = 96.dp,
                                shape = RoundedCornerShape(8.dp),
                            )
                            SizeHeight(16.dp)
                            Text(
                                text = chatGroupBean.name,
                                style = MaterialTheme.typography.titleSmall,
                                color = Color(0xFF111111),
                            )
                            SizeHeight(8.dp)
                            Text(
                                text = "ID:${chatGroupBean.publicId}",
                                style = MaterialTheme.typography.labelLarge,
                                color = Color(0xFF999999),
                            )
                            Weight(1f)
                            ExpandableText(
                                text = chatGroupBean.bulletin,
                                modifier = Modifier.padding(horizontal = 36.dp),
                                style =
                                    MaterialTheme.typography.labelLarge.copy(
                                        lineHeight = 15.sp,
                                    ),
                                color = Color(0xFF666666),
                                collapsedMaxLines = 2,
                                expandableTextColor = Color(0xFF15ABFF),
                            ) {
                                showDialog.value = true
                            }
                            Weight(1f)
                            HorizontalDivider(thickness = 8.dp, color = Color(0xFFF7F7F7))
                        }
                    }
                }
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.White)
                ) {

                    val memberViewModel = viewModel<ChatGroupOnlineMemberListViewModel>(initializer = {
                        ChatGroupOnlineMemberListViewModel(chatGroupBean.id, chatGroupBean.firstPageMembers)
                    })
                    if (!joinState.isMember) {
                        Column(modifier = Modifier.fillMaxWidth()) {
                            Row(
                                modifier =
                                    Modifier
                                        .padding(start = 16.dp, top = 24.dp, end = 16.dp)
                                        .fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.SpaceBetween,
                            ) {
                                Text(
                                    text = "群组成员".localized,
                                    style = MaterialTheme.typography.titleSmall,
                                    color = Color(0xFF111111),
                                )

                                Text(
                                    text = "${chatGroupBean.onlineMemberCnt}/${chatGroupBean.memberCnt}",
                                    style = MaterialTheme.typography.labelLarge,
                                    color = Color(0xFF999999),
                                )
                            }

                            SizeHeight(10.dp)

                            if (self.isCN) {
                                PartMembers(chatGroupBean.firstPageMembers) {
                                    nav.push(ChatGroupRoute.ChatGroupMemberList(chatGroupBean.id, chatGroupBean.firstPageMembers))
                                }
                            } else {
                                ChatGroupMemberGrid(chatGroupBean.id, memberViewModel)
                            }
                        }
                    } else {
                        if (self.isCN) {
                            SizeHeight(20.dp)
                            DetailTabContent(onRefresh = onRefresh)
                        } else {
                            ChatGroupMemberGrid(chatGroupBean.id, memberViewModel)
                        }
                    }
                }
            }

            Box(
                modifier =
                    Modifier
                        .align(Alignment.BottomCenter)
                        .fillMaxWidth()
                        .height(120.dp)
                        .background(
                            Brush.verticalGradient(
                                0f to Color(0x00FFFFFF),
                                0.5f to Color(0xCCFFFFFF),
                                1f to Color(0xFFFFFFFF),
                            ),
                        )
                        .padding(top = 22.dp),
            ) {
                if (!joinState.isMember) {
                    GradientButton(
                        text =
                            "申请中".localized.takeIf { joinState.isApplying } ?: "申请加入".localized,
                        onClick = onApply,
                        enabled = joinState.isNone,
                        modifier =
                            Modifier
                                .padding(horizontal = 32.dp)
                                .fillMaxWidth(),
                    )
                } else {
                    Row {
                        SizeWidth(16.dp)
                        SolidButton(
                            text = "进入聊天".localized,
                            onClick = onOpen,
                            modifier = Modifier.weight(1f),
                            backgroundColor = Color(0xFFC8FFC9),
                        )
                        SizeWidth(12.dp)
                        SolidButton(
                            text = "邀请".localized,
                            onClick = onInvite,
                            modifier = Modifier.weight(1.5f),
                        )
                        SizeWidth(16.dp)
                    }
                }
            }
        }
    }

    if (showDialog.value) {
        Dialog(
            onDismissRequest = {
                showDialog.value = false
            },
        ) {
            LongTextDialog(
                title = "群聊介绍".localized,
                content = chatGroupBean.bulletin,
                buttonContent = {
                    SolidButton(
                        text = "确定".localized,
                        onClick = {
                            showDialog.value = false
                        },
                        modifier =
                            Modifier
                                .padding(vertical = 20.dp)
                                .align(Alignment.CenterHorizontally),
                        height = 36.dp,
                        minWidth = 160.dp,
                    )
                },
            )
        }
    }
}

@Composable
fun PartMembers(list: List<ChatGroupMember>, onClick: OnAction) {
    val l = remember(list) { list.take(4) }
    val scrollState = rememberScrollState()
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
    ) {
        LazyVerticalGrid(
            columns = GridCells.Fixed(5),
            modifier = Modifier
                .height(112.dp)
                .padding(horizontal = 12.dp),

            horizontalArrangement = Arrangement.spacedBy(7.75.dp),
        ) {
            items(l) { item ->
                MemberItem(item)
            }
            if (list.size > 4) {
                item {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .click(onClick = onClick), horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Image(painter = painterResource(R.drawable.ic_more_member), modifier = Modifier.size(56.dp), contentDescription = "more")
                        Text("加入群组\n查看全部".localized, fontSize = 12.sp, color = WakooGrayText)
                    }
                }
            }
        }
        Box(modifier = Modifier.fillMaxSize()) {
            Image(
                painter = painterResource(R.drawable.card_task), modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(375 / 215f),
                contentDescription = "",
                contentScale = ContentScale.FillBounds
            )
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 50.dp), horizontalArrangement = Arrangement.Center
            ) {
                Text("加入群组，解锁全部任务".localized, fontSize = 16.sp, color = WakooText)
                SizeWidth(2.dp)
                Icon(Icons.Default.KeyboardArrowRight, tint = WakooText, contentDescription = "arr")
            }
        }
    }
}

@Composable
private fun ChatGroupMemberGrid(
    groupId: String,
    viewModel: ChatGroupOnlineMemberListViewModel =
        viewModel<ChatGroupOnlineMemberListViewModel>(initializer = {
            ChatGroupOnlineMemberListViewModel(groupId)
        }),
) {
    val nav = LocalAppNavController.root
    StateListPaginateLayout<OnlineKey, ChatGroupMember, ChatGroupOnlineMemberListViewModel>(viewModel = viewModel, refreshEnable = false) { _, list ->
        LazyVerticalGrid(
            columns = GridCells.Fixed(5),
            modifier =
                Modifier
                    .padding(horizontal = 12.dp)
                    .fillMaxSize(),
            horizontalArrangement = Arrangement.spacedBy(7.75.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(top = 10.dp, bottom = 150.dp),
        ) {
            items(list) { item ->
                MemberItem(item)
            }
        }
    }
}

@Composable
private fun MemberItem(item: ChatGroupMember) {
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .widthIn(max = 64.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Box {
            AvatarNetworkImage(
                user = item.user,
                size = 56.dp,
            )
            if (item.role == ChatGroupMember.ROLE_OWNER) {
                ChatGroupOwner(modifier = Modifier.align(Alignment.BottomCenter))
            }
            if (item.role == ChatGroupMember.ROLE_ADMIN) {
                ChatGroupAdmin(modifier = Modifier.align(Alignment.BottomCenter))
            }
            if (item.isOnline) {
                Box(
                    modifier =
                        Modifier
                            .size(14.dp)
                            .align(Alignment.TopEnd)
                            .padding(2.dp)
                            .background(Color.White, CircleShape)
                            .padding(2.dp)
                            .background(
                                WakooGreen,
                                CircleShape,
                            ),
                )
            }
        }
        SizeHeight(6.dp)
        Text(
            text = item.user.name,
            color = Color(0xFF111111),
            style = MaterialTheme.typography.labelMedium,
            textAlign = TextAlign.Center,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
        )
    }
}

@Preview
@Composable
private fun PreviewChatGroupDescriptionScreen() {
    WakooTheme {
        val bean =
            ChatGroupBean(
                name = "星空闪耀",
                publicId = "100001",
                bulletin =
                    "val descriptionContent =\n" +
                        "        \"在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\" +\n" +
                        "            \"在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\" +\n" +
                        "            \"在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\"",
            )
        ChatGroupDescriptionScreen(bean)
    }
}
