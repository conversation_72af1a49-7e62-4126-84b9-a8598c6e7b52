package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.Background: ImageVector
    get() {
        if (_Background != null) {
            return _Background!!
        }
        _Background =
            ImageVector
                .Builder(
                    name = "Background",
                    defaultWidth = 24.dp,
                    defaultHeight = 24.dp,
                    viewportWidth = 24f,
                    viewportHeight = 24f,
                ).apply {
                    path(fill = SolidColor(Color(0xFF111111))) {
                        moveTo(20f, 3f)
                        curveTo(20.53f, 3f, 21.039f, 3.211f, 21.414f, 3.586f)
                        curveTo(21.789f, 3.961f, 22f, 4.47f, 22f, 5f)
                        verticalLineTo(19f)
                        curveTo(22f, 19.53f, 21.789f, 20.039f, 21.414f, 20.414f)
                        curveTo(21.039f, 20.789f, 20.53f, 21f, 20f, 21f)
                        horizontalLineTo(4f)
                        curveTo(3.47f, 21f, 2.961f, 20.789f, 2.586f, 20.414f)
                        curveTo(2.211f, 20.039f, 2f, 19.53f, 2f, 19f)
                        verticalLineTo(5f)
                        curveTo(2f, 4.47f, 2.211f, 3.961f, 2.586f, 3.586f)
                        curveTo(2.961f, 3.211f, 3.47f, 3f, 4f, 3f)
                        horizontalLineTo(20f)
                        close()
                        moveTo(20f, 5f)
                        horizontalLineTo(4f)
                        verticalLineTo(15.1f)
                        lineTo(8.995f, 10.106f)
                        curveTo(9.111f, 9.99f, 9.249f, 9.898f, 9.401f, 9.835f)
                        curveTo(9.552f, 9.772f, 9.715f, 9.74f, 9.879f, 9.74f)
                        curveTo(10.043f, 9.74f, 10.206f, 9.772f, 10.357f, 9.835f)
                        curveTo(10.509f, 9.898f, 10.647f, 9.99f, 10.763f, 10.106f)
                        lineTo(14.828f, 14.172f)
                        lineTo(16.066f, 12.934f)
                        curveTo(16.182f, 12.818f, 16.32f, 12.726f, 16.472f, 12.663f)
                        curveTo(16.623f, 12.6f, 16.786f, 12.568f, 16.95f, 12.568f)
                        curveTo(17.114f, 12.568f, 17.277f, 12.6f, 17.428f, 12.663f)
                        curveTo(17.58f, 12.726f, 17.718f, 12.818f, 17.834f, 12.934f)
                        lineTo(20f, 15.101f)
                        verticalLineTo(5f)
                        close()
                        moveTo(15.5f, 7f)
                        curveTo(15.898f, 7f, 16.279f, 7.158f, 16.561f, 7.439f)
                        curveTo(16.842f, 7.721f, 17f, 8.102f, 17f, 8.5f)
                        curveTo(17f, 8.898f, 16.842f, 9.279f, 16.561f, 9.561f)
                        curveTo(16.279f, 9.842f, 15.898f, 10f, 15.5f, 10f)
                        curveTo(15.102f, 10f, 14.721f, 9.842f, 14.439f, 9.561f)
                        curveTo(14.158f, 9.279f, 14f, 8.898f, 14f, 8.5f)
                        curveTo(14f, 8.102f, 14.158f, 7.721f, 14.439f, 7.439f)
                        curveTo(14.721f, 7.158f, 15.102f, 7f, 15.5f, 7f)
                        close()
                    }
                }.build()

        return _Background!!
    }

@Suppress("ObjectPropertyName")
private var _Background: ImageVector? = null
