package com.buque.wakoo.ui.screens.crony

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.app.OnClick
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.state.StateListPaginateLayout

@Composable
fun CronySettingsScreen(modifier: Modifier = Modifier) {
    val lm = LocalLoadingManager.current
    val vm = viewModel<CronyMemberListViewModel>()
    SegColorTitleScreenScaffold("亲友关系设置".localized, containerColor = Color.White) { pd ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(pd)
                .padding(top = 20.dp)
        ) {
            StateListPaginateLayout<Int, CronyItem, CronyMemberListViewModel>(viewModel = vm) { state, data ->
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 16.dp), verticalArrangement = Arrangement.spacedBy(32.dp)
                ) {
                    items(data) { item ->
                        Item(item) {
                            lm.show(null) {
                                vm.breakUp(item.id)
                                    .onSuccess {
                                        showToast("已解除亲友团关系".localized)
                                    }
                            }
                        }
                    }
                }
            }
        }
    }
}


@Composable
private fun Item(item: CronyItem, onClick: OnClick) {
    Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
        AvatarNetworkImage(item.user.basicUser, size = 48.dp)
        SizeWidth(8.dp)
        Column(modifier = Modifier.weight(1f)) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    item.user.nickname,
                    fontSize = 16.sp,
                    color = WakooText,
                    modifier = Modifier.weight(1f, false),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                SizeWidth(4.dp)
                item.tag.Content()
            }
            SizeHeight(6.dp)
            GenderAgeTag(item.user.basicUser)
        }
        SolidButton(
            "解除关系",
            fontSize = 14.sp,
            minWidth = 80.dp,
            height = 32.dp,
            backgroundColor = WakooText,
            textColor = WakooGreen,
            onClick = onClick
        )
    }
}