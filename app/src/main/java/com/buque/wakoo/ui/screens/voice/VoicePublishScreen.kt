package com.buque.wakoo.ui.screens.voice

import android.Manifest
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.app.Const
import com.buque.wakoo.app.DevicesKV
import com.buque.wakoo.bean.VoiceCardItem
import com.buque.wakoo.ext.formatMillisDuration
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.RecordingState
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.manager.localizedWithKey
import com.buque.wakoo.network.api.bean.VoicePublishConfig
import com.buque.wakoo.network.api.bean.VoiceTag
import com.buque.wakoo.ui.dialog.LongTextDialog
import com.buque.wakoo.ui.icons.MicVoice
import com.buque.wakoo.ui.icons.Ok
import com.buque.wakoo.ui.icons.Pause
import com.buque.wakoo.ui.icons.Play
import com.buque.wakoo.ui.icons.Retry
import com.buque.wakoo.ui.icons.Stop
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.ImeButtonScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.VoiceTagChip
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.media.manager.PlayMediaItem
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.ui.widget.state.isSuccess
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.PermissionUtils
import com.buque.wakoo.viewmodel.VoicePublishViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

/**
 * 声音发布页面的录音状态
 */
sealed interface VoiceState {
    data object Idle : VoiceState // 待录制

    data class Recording(
        val recorder: RecordingState.Recording,
    ) : VoiceState // 录制中

    data class Playing(
        val playItem: PlayMediaItem,
        val recorder: RecordingState.Completed,
    ) : VoiceState // 试听中
}

private val publishRuleContent =
    """
    为了维护社区良好氛围，保障所有用户的使用体验与合法权益，wakoo对用户发布的话题内容作出如下规范。请您在发布内容前请仔细阅读并遵守：
    平台严禁发布的内容类型包括但不限于：
    1. 违反法律法规：
    含有违法、暴力、血腥、赌博、毒品、枪支等违法信息；
    涉及政治敏感、宗教极端、种族歧视、民族分裂、国家安全等内容；
    2. 涉及色情或性暗示：
    包含露骨的性暗示、挑逗性内容，或以“约”“玩”等字眼引导低俗互动；
    含诱导裸聊等色情性质话题或暗号；
    3. 人身攻击与歧视：
    带有针对某类群体或个人的辱骂、人身攻击、恶意引战；
    内容中出现性别歧视、外貌羞辱、身材羞辱、职业羞辱等；
    4. 低质/引流内容：
    明显广告、引流、推广、二维码、拉群、外链等行为；
    大量重复无意义话题、刷屏式发起、虚假信息等；
    5. 诱导他人不当行为：
    诱导自残、自杀、暴力、非法交易等行为；
    发布易引发未成年人不当模仿或误导的危险话题；

    wakoo保留对所有用户发布的内容进行内容审核、隐藏、删除或限制展示的权利
    违反规定的用户，平台将视情节给予警告、封禁、限制发言、永久禁言等处理
    """.trimIndent().localizedWithKey("语音发布社区规则")

/**
 * 声音发布页面
 */
@Composable
fun VoicePublishScreen(
    viewModel: VoicePublishViewModel,
    toPreview: (VoiceCardItem, PlayMediaItem) -> Unit = { _, _ -> },
) {
    val audioRecordManager = viewModel.audioRecordManager

    var textContent by rememberSaveable { mutableStateOf("") }

    var isPublic by rememberSaveable { mutableStateOf(true) }

    // 监听录音管理器的状态变化
    val recordingState by audioRecordManager.recordingState.collectAsStateWithLifecycle()

    val voiceState by remember {
        derivedStateOf {
            val rState = recordingState
            when (rState) {
                is RecordingState.Recording -> {
                    VoiceState.Recording(rState)
                }

                is RecordingState.Completed -> {
                    VoiceState.Playing(
                        playItem =
                            PlayMediaItem.prefixTagAudio(
                                url = rState.finalOutputFile.absolutePath,
                                prefix = "publish-voice-",
                            ),
                        recorder = rState,
                    )
                }

                else -> {
                    VoiceState.Idle
                }
            }
        }
    }

    if (recordingState.hasError) {
        LaunchedEffect(Unit) {
            (recordingState as? RecordingState.Error)?.apply {
                showToast(message)
            }
        }
    }

    if (!LocalInspectionMode.current) {
        var firstEnter by remember {
            mutableStateOf(DevicesKV.getBoolean(Const.KVKey.FIRST_ENTER_PUBLISH, true))
        }

        if (firstEnter) {
            LaunchedEffect(Unit) {
                DevicesKV.putBoolean(Const.KVKey.FIRST_ENTER_PUBLISH, false)
            }

            Dialog(onDismissRequest = {
                firstEnter = false
            }) {
                LongTextDialog(
                    title = "内容合规准则".localized,
                    content = publishRuleContent,
                    buttonText = "确定".localized,
                    onButtonClick = {
                        firstEnter = false
                    },
                )
            }
        }
    }

    val configCState by viewModel.configStateFlow.collectAsStateWithLifecycle()

    TitleScreenScaffold(
        title = "发布声音内容".localized,
        modifier = Modifier.fillMaxSize(),
    ) { padding ->
        ImeButtonScaffold(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(padding),
            buttonModifier =
                Modifier
                    .fillMaxWidth()
                    .background(WakooWhite)
                    .padding(
                        top = 20.dp,
                        bottom = 24.dp,
                    ),
            buttonContent = {
                if (configCState.isSuccess) {
                    val nextEnable by remember {
                        derivedStateOf {
                            textContent.isNotBlank() &&
                                voiceState is VoiceState.Playing &&
                                viewModel.selectedTagIds.isNotEmpty()
                        }
                    }

                    GradientButton(
                        text = "下一步".localized,
                        onClick = {
                            (voiceState as? VoiceState.Playing)?.also {
                                toPreview(
                                    viewModel.startPreview(
                                        content = textContent,
                                        isPublish = isPublic,
                                        filePath = (voiceState as VoiceState.Playing).recorder.finalOutputFile.absolutePath,
                                        duration = recordingState.duration / 1000,
                                    ),
                                    it.playItem,
                                )
                            }
                        },
                        modifier =
                            Modifier
                                .padding(horizontal = 28.dp)
                                .fillMaxWidth(),
                        enabled = nextEnable,
                    )
                }
            },
            useScrollableLayout = false,
        ) {
            CStateLayout(configCState, useScrollableLayout = false, onRetry = {
                viewModel.loadPublishConfig()
            }) { data ->
                // 主要内容区域
                val audioLauncher =
                    rememberLauncherForActivityResult(PermissionUtils.Contracts) { granted ->
                        if (granted) {
                            audioRecordManager.startRecording(
                                maxDurationMillis = data.soundMaxDuration * 1000L,
                                minDurationMillis = data.soundMinDuration * 1000L,
                            )
                        } else {
                            showToast("无法获取录音权限，请到系统设置中手动授予录音权限".localized)
                        }
                    }

                Column(
                    modifier =
                        Modifier
                            .verticalScroll(rememberScrollState())
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp),
                ) {
                    SizeHeight(16.dp)

                    Text(
                        text = "录制声音内容".localized,
                        style = MaterialTheme.typography.labelLarge,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF666666),
                    )

                    SizeHeight(16.dp)

                    // 录制声音内容区域
                    RecordingSection(
                        config = data,
                        voiceState = voiceState,
                        onStartRecording = {
                            audioLauncher.launch(Manifest.permission.RECORD_AUDIO)
                        },
                        onStopRecording = {
                            audioRecordManager.stopRecording()
                        },
                        onStartPlaying = {
                            MediaPlayerManager.play(it.playItem)
                        },
                        onStopPlaying = {
                            MediaPlayerManager.pause(it.playItem)
                        },
                        onReRecord = {
                            MediaPlayerManager.release(it.playItem)
                            try {
                                it.recorder.finalOutputFile.delete()
                            } catch (e: Exception) {
                                LogUtils.e(
                                    throwable = e,
                                    message = "删除文件失败",
                                )
                            } finally {
                                audioRecordManager.reset()
                            }
                        },
                        onConfirm = {
                            audioRecordManager.stopRecording()
                        },
                    )

                    // 添加文字描述
                    TextDescriptionSection(
                        text = textContent,
                        onTextChange = { if (it.length <= 80) textContent = it },
                    )

                    SizeHeight(24.dp)

//                // 日记标签
                    TagSelectionSection(
                        selectIds = viewModel.selectedTagIds,
                        tags = data.tags,
                        onTagSelectionChange = { tag ->
                            viewModel.toggleSelectedByTag(tag)
                        },
                    )

                    SizeHeight(24.dp)

                    // 谁可以听
                    VisibilitySection(
                        isPublic = isPublic,
                        onVisibilityChange = { isPublic = it },
                    )

                    SizeHeight(32.dp)

                    val lifecycleOwner = LocalLifecycleOwner.current

                    // 清理资源
                    DisposableEffect(Unit) {
                        val observer =
                            LifecycleEventObserver { source, event ->
                                if (event == Lifecycle.Event.ON_STOP && voiceState is VoiceState.Playing) {
                                    MediaPlayerManager.pause((voiceState as VoiceState.Playing).playItem)
                                }
                            }

                        lifecycleOwner.lifecycle.addObserver(observer)

                        onDispose {
                            lifecycleOwner.lifecycle.removeObserver(observer)
                        }
                    }
                }
            }
        }
    }
}

/**
 * 录制声音内容区域
 */
@Composable
fun RecordingSection(
    config: VoicePublishConfig,
    voiceState: VoiceState,
    onStartRecording: (VoiceState.Idle) -> Unit,
    onStopRecording: (VoiceState.Recording) -> Unit,
    onStartPlaying: (VoiceState.Playing) -> Unit,
    onStopPlaying: (VoiceState.Playing) -> Unit,
    onReRecord: (VoiceState.Playing) -> Unit = {},
    onConfirm: (VoiceState.Playing) -> Unit = {},
) {
    Column {
        when (voiceState) {
            is VoiceState.Idle -> {
                RecordVoiceUI(
                    voiceState = voiceState,
                    topRecordTipText = "00:00",
                    bottomRecordTipText =
                        "点击后靠近麦克风开始说话，要求%ss-%ss".localizedFormat(
                            config.soundMinDuration,
                            config.soundMaxDuration,
                        ),
                    onClick = {
                        onStartRecording(voiceState)
                    },
                )
            }

            is VoiceState.Recording -> {
                RecordVoiceUI(
                    voiceState = voiceState,
                    topRecordTipText = "${"正在录制".localized} ${voiceState.recorder.formattedDuration}",
                    bottomRecordTipText = "点击结束录音".localized,
                    onClick = {
                        onStopRecording(voiceState)
                    },
                )
            }

            is VoiceState.Playing -> {
                val isPlaying by remember(voiceState.playItem.tag) {
                    derivedStateOf {
                        MediaPlayerManager.currentPlayingTag.value == voiceState.playItem.tag
                    }
                }

                var refreshFlag by remember {
                    mutableIntStateOf(0)
                }

                if (isPlaying) {
                    LaunchedEffect(Unit) {
                        while (isActive) {
                            delay(500)
                            refreshFlag++
                        }
                    }
                }

                val formattedTime by remember(voiceState.playItem.tag) {
                    derivedStateOf {
                        val totalDuration = MediaPlayerManager.getPlayerDuration(voiceState.playItem)
                        val currentPosition = MediaPlayerManager.getPlayerPosition(voiceState.playItem)
                        if (isPlaying) {
                            refreshFlag // 这是一个刷新标志，不能删除
                            if (totalDuration <= 0) {
                                "${currentPosition.formatMillisDuration}/${voiceState.recorder.formattedDuration}"
                            } else {
                                "${currentPosition.formatMillisDuration}/${totalDuration.formatMillisDuration}"
                            }
                        } else {
                            if (totalDuration <= 0) {
                                "${currentPosition.formatMillisDuration}/${voiceState.recorder.formattedDuration}"
                            } else {
                                "${currentPosition.formatMillisDuration}/${totalDuration.formatMillisDuration}"
                            }
                        }
                    }
                }

                RecordVoiceUI(
                    voiceState = voiceState,
                    topRecordTipText = formattedTime,
                    bottomRecordTipText = if (isPlaying) "试听中".localized else "点击试听".localized,
                    onReRecord = {
                        onReRecord(voiceState)
                    },
                    onConfirm = {
                        onConfirm(voiceState)
                    },
                    onClick = {
                        if (voiceState.playItem.playWhenReadyFlag) {
                            onStopPlaying(voiceState)
                        } else {
                            onStartPlaying(voiceState)
                        }
                    },
                )
            }
        }
    }
}

/**
 * 待录制状态UI
 */
@Composable
fun RecordVoiceUI(
    topRecordTipText: String,
    bottomRecordTipText: String,
    voiceState: VoiceState,
    onReRecord: () -> Unit = {},
    onConfirm: () -> Unit = {},
    onClick: () -> Unit,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxWidth(),
    ) {
        Box(
            modifier =
                Modifier
                    .padding(horizontal = 40.dp)
                    .widthIn(min = 116.dp)
                    .height(24.dp)
                    .background(
                        color = Color(0xFFF8F8F8),
                        shape = CircleShape,
                    ).padding(horizontal = 16.dp),
            contentAlignment = Alignment.Center,
        ) {
            Text(
                text = topRecordTipText,
                style = MaterialTheme.typography.labelLarge,
                color = Color(0xFF666666),
            )
        }

        SizeHeight(16.dp)

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceEvenly,
        ) {
            if (voiceState is VoiceState.Playing) {
                LabelIconButton(
                    label = "重录".localized,
                    icon = WakooIcons.Retry,
                    onClick = onReRecord,
                )
            }

            val sweepAngle =
                when (voiceState) {
                    is VoiceState.Idle -> {
                        0f
                    }

                    is VoiceState.Recording -> {
                        360f * voiceState.recorder.duration / voiceState.recorder.config.maxDurationMillis
                    }

                    else -> {
                        360f
                    }
                }

            // 录音按钮
            Box(
                modifier =
                    Modifier
                        .size(80.dp)
                        .drawWithCache {
                            val strokeWidth1 = 1.dp.toPx()
                            val strokeWidth2 = 3.dp.toPx()
                            val style1 =
                                Stroke(
                                    width = strokeWidth1,
                                    cap = StrokeCap.Round,
                                )
                            val style2 =
                                Stroke(
                                    width = strokeWidth2,
                                    cap = StrokeCap.Round,
                                )

                            val brush =
                                Brush.horizontalGradient(
                                    colors =
                                        listOf(
                                            Color(0xFFA3FF2C),
                                            Color(0xFF31FFA1),
                                        ),
                                )
                            onDrawWithContent {
                                drawContent()

                                drawCircle(
                                    color = Color(0xFF66FE6B), // 直接指定一个颜色,
                                    style = style1,
                                )

                                drawArc(
                                    brush = brush, // 直接指定一个颜色
                                    startAngle = -90f,
                                    sweepAngle = sweepAngle,
                                    useCenter = false,
                                    style = style2,
                                )
                            }
                        }.padding(6.dp)
                        .clip(CircleShape)
                        .background(
                            Brush.horizontalGradient(
                                colors =
                                    listOf(
                                        Color(0xFFA3FF2C),
                                        Color(0xFF31FFA1),
                                    ),
                            ),
                        ).clickable(onClick = onClick),
                contentAlignment = Alignment.Center,
            ) {
                when (voiceState) {
                    is VoiceState.Idle -> {
                        Icon(
                            imageVector = WakooIcons.MicVoice,
                            contentDescription = null,
                            tint = Color.Black,
                        )
                    }

                    is VoiceState.Recording -> {
                        Icon(
                            imageVector = WakooIcons.Stop,
                            contentDescription = null,
                            tint = Color.Black,
                        )
                    }

                    is VoiceState.Playing -> {
                        val isPlaying by remember(voiceState.playItem.tag) {
                            derivedStateOf {
                                MediaPlayerManager.currentPlayingTag.value == voiceState.playItem.tag
                            }
                        }
                        Icon(
                            imageVector = if (isPlaying) WakooIcons.Pause else WakooIcons.Play,
                            contentDescription = null,
                            tint = Color.Black,
                        )
                    }
                }
            }

            if (voiceState is VoiceState.Playing) {
                LabelIconButton(
                    label = "确定".localized,
                    icon = WakooIcons.Ok,
                    onClick = onConfirm,
                )
            }
        }

        Column(
            modifier =
                Modifier
                    .heightIn(74.dp)
                    .padding(horizontal = 30.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            SizeHeight(14.dp)

            if (voiceState is VoiceState.Idle) {
                Text(
                    text = "点击开始录音".localized,
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF111111),
                )

                SizeHeight(2.dp)
            }

            Text(
                text = bottomRecordTipText,
                style = MaterialTheme.typography.labelLarge,
                color = Color(0xFFB6B6B6),
            )
        }
    }
}

@Composable
private fun LabelIconButton(
    label: String,
    icon: ImageVector,
    onClick: () -> Unit,
) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        IconButton(
            onClick = onClick,
            modifier =
                Modifier
                    .clip(CircleShape)
                    .size(40.dp)
                    .background(Color(0xFFF8F8F8)),
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
            )
        }

        SizeHeight(5.dp)

        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF999999),
            modifier = Modifier.noEffectClick(onClick = onClick),
        )
    }
}

/**
 * 文字描述输入区域
 */
@Composable
private fun TextDescriptionSection(
    text: String,
    onTextChange: (String) -> Unit,
) {
    Column {
        Text(
            text = "添加文字描述".localized,
            style = MaterialTheme.typography.labelLarge,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF666666),
        )

        SizeHeight(16.dp)

        AppTextField(
            value = text,
            onValueChange = onTextChange,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(112.dp),
            placeholder = "请输入你的声音日记内容".localized,
            maxLength = 80,
            backgroundColor = Color(0xFFF7F7F7),
        )
    }
}

/**
 * 标签选择区域
 */
@Composable
private fun TagSelectionSection(
    selectIds: Set<Int>,
    tags: List<VoiceTag>,
    onTagSelectionChange: (VoiceTag) -> Unit,
) {
    Column {
        Text(
            text = "日记标签".localized,
            style = MaterialTheme.typography.labelLarge,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF666666),
        )

        SizeHeight(16.dp)

        FlowRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(4.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            tags.forEach {
                VoiceTagChip(selectIds.contains(it.id), it) {
                    onTagSelectionChange(it)
                }
            }
        }
    }
}

/**
 * 可见性设置区域
 */
@Composable
private fun VisibilitySection(
    isPublic: Boolean,
    onVisibilityChange: (Boolean) -> Unit,
) {
    Column {
        Text(
            text = "谁可以听".localized,
            style = MaterialTheme.typography.labelLarge,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF666666),
        )

        SizeHeight(16.dp)

        // 公开选项
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .noEffectClick {
                        onVisibilityChange(true)
                    },
        ) {
            RadioButton(
                selected = isPublic,
                modifier = Modifier.size(16.dp),
                onClick = {
                    onVisibilityChange(true)
                },
                colors =
                    RadioButtonDefaults.colors(
                        selectedColor = Color(0xFF66FE6B),
                        unselectedColor = Color(0xFFE9EAEF),
                    ),
            )

            SizeWidth(8.dp)

            Text(
                text = "公开".localized,
                style = MaterialTheme.typography.labelLarge,
                color = if (isPublic) Color(0xFF111111) else Color(0xFFB6B6B6),
            )
        }

        SizeHeight(16.dp)

        // 私密选项
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .noEffectClick {
                        onVisibilityChange(false)
                    },
        ) {
            RadioButton(
                selected = !isPublic,
                modifier = Modifier.size(16.dp),
                onClick = { onVisibilityChange(false) },
                colors =
                    RadioButtonDefaults.colors(
                        selectedColor = Color(0xFF66FE6B),
                        unselectedColor = Color(0xFFE9EAEF),
                    ),
            )

            SizeWidth(8.dp)

            Text(
                text = "仅自己可见".localized,
                style = MaterialTheme.typography.labelLarge,
                color = if (!isPublic) Color(0xFF111111) else Color(0xFFB6B6B6),
            )
        }
    }
}

@Preview()
@Composable
private fun VoicePublishScreenPreview() {
    WakooTheme {
        val context = LocalContext.current
        VoicePublishScreen(
            remember {
                VoicePublishViewModel(context.applicationContext)
            },
        )
    }
}
