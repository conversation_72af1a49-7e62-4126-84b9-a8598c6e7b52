package com.buque.wakoo.ui.screens.chatgroup.screen

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.RecommendLiveRoom
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.ui.screens.liveroom.LiveRoomItem
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.StateListPaginateLayout
import com.buque.wakoo.viewmodel.BasicListPaginateViewModel

class GroupRoomListViewModel(val groupId: String) : BasicListPaginateViewModel<Int, RecommendLiveRoom>() {
    private val repo = GlobalRepository.chatGroupRepo
    private var currentPage = 1
    override suspend fun loadData(
        pageKey: Int,
        pageSize: Int
    ): Result<List<RecommendLiveRoom>> {
        return repo.getRoomList(groupId, pageKey).map {
            it.data
        }
    }

    override fun getFirstPageKey(dataKey: Any): Int {
        currentPage = 1
        return currentPage
    }

    override fun getNextPageKey(
        cState: CState<List<RecommendLiveRoom>>,
        dataKey: Any,
        pageKey: Int?
    ): Int {
        return ++currentPage
    }

    override fun getDistinctSelector(): (RecommendLiveRoom) -> String {
        return {
            it.id.toString()
        }
    }

}

@Composable
fun ChatGroupRoomListScreen(groupId: String) {
    val vm = viewModel<GroupRoomListViewModel>(initializer = { GroupRoomListViewModel(groupId) })
    StateListPaginateLayout<Int, RecommendLiveRoom, GroupRoomListViewModel>(modifier = Modifier.fillMaxSize(), viewModel = vm) { state, data ->
        LazyColumn(modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
            , verticalArrangement = Arrangement.spacedBy(12.dp)) {
            items(data) { item ->
                LiveRoomItem(item) {
                    LiveRoomManager.joinRoom(it)
                }
            }
        }
    }
}