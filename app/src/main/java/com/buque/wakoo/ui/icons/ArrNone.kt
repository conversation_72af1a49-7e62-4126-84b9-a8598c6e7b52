package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val ArrowNone: ImageVector
    get() {
        if (_ArrowNone != null) {
            return _ArrowNone!!
        }
        _ArrowNone = ImageVector.Builder(
            name = "ArrowNone",
            defaultWidth = 12.dp,
            defaultHeight = 12.dp,
            viewportWidth = 12f,
            viewportHeight = 12f
        ).apply {
            path(fill = SolidColor(Color(0xFFB6B6B6))) {
                moveTo(5.732f, 10.697f)
                curveTo(5.859f, 10.918f, 6.178f, 10.918f, 6.306f, 10.697f)
                lineTo(8.243f, 7.342f)
                curveTo(8.37f, 7.121f, 8.211f, 6.845f, 7.956f, 6.845f)
                horizontalLineTo(4.082f)
                curveTo(3.827f, 6.845f, 3.667f, 7.121f, 3.795f, 7.342f)
                lineTo(5.732f, 10.697f)
                close()
            }
            path(fill = SolidColor(Color(0xFFB6B6B6))) {
                moveTo(6.306f, 1.281f)
                curveTo(6.178f, 1.06f, 5.859f, 1.06f, 5.732f, 1.281f)
                lineTo(3.795f, 4.636f)
                curveTo(3.667f, 4.856f, 3.827f, 5.133f, 4.082f, 5.133f)
                horizontalLineTo(7.956f)
                curveTo(8.211f, 5.133f, 8.37f, 4.856f, 8.242f, 4.636f)
                lineTo(6.306f, 1.281f)
                close()
            }
        }.build()

        return _ArrowNone!!
    }

@Suppress("ObjectPropertyName")
private var _ArrowNone: ImageVector? = null
