package com.buque.wakoo.ui.screens.liveroom.panel

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.lerp
import androidx.compose.ui.zIndex
import com.buque.wakoo.bean.RankListContainer
import com.buque.wakoo.ext.getValue
import com.buque.wakoo.ext.rememberIsFirstComposition
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.RankTimely
import com.buque.wakoo.navigation.RoomRankTab
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooSecondaryText
import com.buque.wakoo.ui.theme.WakooSecondaryUnSelected
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AdaptiveScrollableTabRow
import com.buque.wakoo.ui.widget.NoIndicationInteractionSource
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.UserListItem
import com.buque.wakoo.ui.widget.adaptiveTabIndicatorOffset
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.liveroom.RoomRankListViewModel
import kotlinx.coroutines.launch
import kotlin.math.absoluteValue

private val TABS: List<RoomRankTab> =
    listOf(
        RoomRankTab.Contribution, // 贡献
        RoomRankTab.Charm, // 魅力
    )

private val SUBTABS: List<RankTimely> =
    listOf(
        RankTimely.Daily, // 贡献
        RankTimely.Weekly, // 魅力
    )

@Composable
fun DialogScope.LiveRoomRankPanel(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier =
            modifier
                .fillMaxHeight(0.75f)
                .fillMaxWidth()
                .background(
                    color = WakooWhite,
                    shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp),
                ).navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        val pagerState = rememberPagerState(pageCount = { TABS.size })
        val scope = rememberCoroutineScope()

        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(48.dp),
            contentAlignment = Alignment.Center,
        ) {
            AdaptiveScrollableTabRow(
                selectedTabIndex = pagerState.currentPage,
                indicator = { tabPositions ->
                    if (pagerState.currentPage < tabPositions.size) {
                        Box(
                            modifier =
                                Modifier
                                    .adaptiveTabIndicatorOffset(tabPositions[pagerState.currentPage])
                                    .requiredWidth(12.dp)
                                    .height(3.dp)
                                    .background(
                                        WakooSecondarySelected,
                                        CircleShape,
                                    ),
                        )
                    }
                },
                tabSpacing = 40.dp,
                edgePadding = 20.dp,
            ) {
                TABS.forEachIndexed { index, tab ->
                    Tab(
                        selected = pagerState.currentPage == index,
                        onClick = {
                            scope.launch {
                                pagerState.animateScrollToPage(index)
                            }
                        },
                        interactionSource = remember { NoIndicationInteractionSource() },
                        selectedContentColor = WakooSecondarySelected,
                        unselectedContentColor = WakooSecondaryUnSelected,
                        content = {
                            Box(
                                modifier =
                                    Modifier
                                        .padding(bottom = 4.dp),
                                contentAlignment = Alignment.Center,
                            ) {
                                tab.TabContent(
                                    pagerState.currentPage == index,
                                )
                            }
                        },
                    )
                }
            }
        }

        val firstEnter by rememberIsFirstComposition()

        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize(),
            userScrollEnabled = false,
        ) { index ->
            Column(modifier = Modifier.fillMaxWidth()) {
                val subPagerState = rememberPagerState(pageCount = { SUBTABS.size })
                Box(
                    modifier =
                        Modifier
                            .align(Alignment.CenterHorizontally)
                            .background(Color(0xFFF7F7F7), CircleShape)
                            .padding(4.dp),
                ) {
                    AdaptiveScrollableTabRow(
                        selectedTabIndex = subPagerState.currentPage,
                        edgePadding = 3.dp,
                        indicator = { tabPositions ->
                            // --- 2. 自定义指示器 ---
                            // tabPositions 列表包含了每个 Tab 准确的测量信息
                            if (tabPositions.isNotEmpty() && subPagerState.currentPage < tabPositions.size) {
                                val currentPage = subPagerState.currentPage
                                val currentPageOffset = subPagerState.currentPageOffsetFraction

                                // 获取当前和目标 Tab 的位置信息
                                val currentTab = tabPositions[currentPage]
                                val targetTab =
                                    tabPositions.getOrNull(
                                        (currentPage + currentPageOffset.let { if (it > 0) 1 else -1 }).coerceIn(SUBTABS.indices),
                                    ) ?: currentTab

                                // 使用 lerp 对 Dp 值进行线性插值，以实现平滑过渡
                                val indicatorWidth =
                                    lerp(currentTab.width, targetTab.width, currentPageOffset.absoluteValue)
                                val indicatorOffset =
                                    lerp(currentTab.left, targetTab.left, currentPageOffset.absoluteValue)
                                // 绘制指示器 Box

                                Box(
                                    modifier = Modifier.fillMaxSize(),
                                ) {
                                    Box(
                                        modifier =
                                            Modifier
                                                .align(Alignment.CenterStart)
                                                .offset(x = indicatorOffset)
                                                .width(indicatorWidth)
                                                .height(22.dp)
                                                .background(WakooWhite, CircleShape),
                                    )
                                }
                            }
                        },
                    ) {
                        // --- 3. Tab 标签项 ---
                        SUBTABS.forEachIndexed { index, tabItem ->
                            Tab(
                                selected = subPagerState.currentPage == index,
                                onClick = {
                                    scope.launch {
                                        subPagerState.animateScrollToPage(index)
                                    }
                                },
                                modifier = Modifier.zIndex(1f), // 确保 Tab 内容（文本）绘制在指示器之上
                                content = {
                                    Box(
                                        modifier =
                                            Modifier
                                                .widthIn(min = 76.dp)
                                                .height(22.dp)
                                                .padding(horizontal = 16.dp),
                                        contentAlignment = Alignment.Center,
                                    ) {
                                        tabItem.TabContent(selected = subPagerState.currentPage == index)
                                    }
                                },
                                // 移除默认的选中/未选中时的颜色变化，我们用自己的指示器
                                selectedContentColor = WakooText,
                                unselectedContentColor = WakooSecondaryText,
                                interactionSource = remember { NoIndicationInteractionSource() },
                            )
                        }
                    }
                }

                HorizontalPager(
                    state = subPagerState,
                    modifier = Modifier.fillMaxSize(),
                ) { subIndex ->
                    CStateListPaginateLayout<String, Int, RankListContainer, RoomRankListViewModel>(
                        reqKey = roomInfoState.id,
                        modifier = Modifier.fillMaxSize(),
                        emptyCheckProvider = {
                            if (it.isEmpty()) {
                                true
                            } else {
                                it.data
                                    .first()
                                    .getList(TABS[index], SUBTABS[subIndex])
                                    .isEmpty()
                            }
                        },
                        autoRefresh = firstEnter && index == 0 && subIndex == 0,
                    ) { paginateState, l ->
                        val list = l.first().getList(TABS[index], SUBTABS[subIndex])
                        LazyColumn(
                            modifier = Modifier.fillMaxSize(),
                            contentPadding = PaddingValues(bottom = 36.dp),
                        ) {
                            itemsIndexed(list) { i, item ->
                                UserListItem(
                                    user = item.userInfo,
                                    modifier = Modifier.padding(16.dp),
                                    startContent = {
                                        Text(
                                            text = "${i + 1}",
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = Color(0x80666666),
                                            fontFamily = FontFamily.MI_SANS,
                                        )
                                        SizeWidth(14.dp)
                                        AvatarNetworkImage(
                                            user = item.userInfo,
                                            size = 48.dp,
                                            onClick = {
                                                roomInfoState.sendEvent(
                                                    RoomEvent.PanelDialog {
                                                        LiveRoomUserInfoPanel(item.userInfo, roomInfoState)
                                                    },
                                                )
                                            }
                                        )
                                    },
                                ) {
                                    Text(
                                        text = "${if (index == 0) "贡献值".localized else "魅力值".localized}${item.value}",
                                        style = MaterialTheme.typography.labelLarge,
                                        color = Color(0x80666666),
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
