package com.buque.wakoo.ui.widget

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withLink
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.manager.localized

private const val EXPAND_ACTION_TAG = "expand"
private const val ELLIPSIS_SUFFIX = "... "

class BadExpandException : java.lang.Exception()

/**
 * 一个可折叠和展开的文本组件，带有自定义的可点击后缀。
 * 此实现通过迭代测量来精确计算截断点，确保后缀在任何情况下都能完整显示。
 *
 * @param text The full text to be displayed.
 * @param modifier The modifier to be applied to the Text.
 * @param collapsedMaxLines 在折叠状态下显示的最大行数。
 * @param expandableText 用于展开操作的文本 (例如, "全文")。
 * @param style 主体文本的样式。
 * @param expandableTextColor 可展开操作文本的颜色。
 */
@Composable
fun ExpandableText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    fontWeight: FontWeight? = null,
    fontFamily: FontFamily? = null,
    textAlign: TextAlign? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
    collapsedMaxLines: Int = 3,
    ellipsisSuffix: String = ELLIPSIS_SUFFIX,
    expandableText: String = "全文".localized,
    style: TextStyle = LocalTextStyle.current,
    expandableTextColor: Color = MaterialTheme.colorScheme.primary,
    onExpandAction: () -> Unit = { throw BadExpandException() },
) {
    // 状态：是否展开
    var isExpanded by rememberSaveable(text) { mutableStateOf(false) }
    // 状态：计算出的折叠后文本
    var collapsedText by remember(text) { mutableStateOf<AnnotatedString?>(null) }
    // 状态：文本是否真的需要截断
    var isTruncated by remember(text) { mutableStateOf(false) }

    // 在 Composable 函数体中创建 TextMeasurer
    val textMeasurer = rememberTextMeasurer()

    // 完整的展开时文本
    val fullTextAnnotated =
        remember(text) {
            buildAnnotatedString { append(text) }
        }

    // 根据状态决定显示哪个文本
    val textToDisplay =
        if (isExpanded || !isTruncated) {
            fullTextAnnotated
        } else {
            collapsedText ?: fullTextAnnotated
        }

    // 点击“全文”后的展开操作
    val expandAction = {
        try {
            onExpandAction()
        } catch (_: BadExpandException) {
            isExpanded = true
        }
    }

    Text(
        text = textToDisplay,
        modifier =
            Modifier
                .animateContentSize()
                .then(modifier),
        color = color,
        fontSize = fontSize,
        fontWeight = fontWeight,
        fontFamily = fontFamily,
        textAlign = textAlign,
        lineHeight = lineHeight,
        style = style,
        maxLines = if (isExpanded) Int.MAX_VALUE else collapsedMaxLines,
        onTextLayout = { textLayoutResult ->
            // 如果已展开或已计算完成，则不再执行
            if (isExpanded || collapsedText != null) return@Text

            // 检查文本是否真的溢出了
            if (textLayoutResult.hasVisualOverflow) {
                isTruncated = true

                val lastLineIndex = collapsedMaxLines - 1

                // 安全检查：确保 lastLineIndex 在有效范围内
                if (lastLineIndex >= textLayoutResult.lineCount) {
                    // 如果请求的行数超过了实际行数，直接显示完整文本
                    isTruncated = false
                    return@Text
                }

                // 获取最后一行的结束可见字符的索引
                var cutOffIndex = textLayoutResult.getLineEnd(lastLineIndex, visibleEnd = true)

                // 安全检查：确保 cutOffIndex 在文本长度范围内
                cutOffIndex = cutOffIndex.coerceIn(0, text.length)

                val suffix = "$ellipsisSuffix$expandableText"

                // --- 核心修复逻辑：迭代验证 ---
                // 我们从初始猜测的截断点开始，向前回退，直到拼接后的字符串能被容纳

                // 获取最后一行的宽度，这是我们可用的空间
                // 注意：在多行文本中，最后一行的可用宽度可能不等于容器总宽度（例如，如果前面有浮动元素）
                // 为了简单和通用，我们使用容器总宽度作为约束
                val containerWidth = textLayoutResult.size.width

                var fits = false
                while (!fits && cutOffIndex > 0) {
                    // 安全检查：确保 cutOffIndex 不超过文本长度
                    val safeCutOffIndex = cutOffIndex.coerceIn(0, text.length)

                    // 构建候选的截断文本
                    val truncatedText = text.substring(0, safeCutOffIndex)
                    val candidateText =
                        buildAnnotatedString {
                            append(truncatedText)
                            append(suffix)
                        }

                    // 如果测量后的行数 <= 1，说明这个截断点是安全的
                    // 我们要比较的是截断后的最后一行，而不是整个文本
                    // 一个更精确的方法是：
                    val lineStart = textLayoutResult.getLineStart(lastLineIndex)

                    // 安全检查：确保 lineStart 和 cutOffIndex 都在有效范围内
                    val safeLineStart = lineStart.coerceIn(0, text.length)
                    val safeEndIndex = safeCutOffIndex.coerceIn(safeLineStart, text.length)

                    val textOnLastLine = if (safeEndIndex > safeLineStart) {
                        text.substring(safeLineStart, safeEndIndex)
                    } else {
                        "" // 如果范围无效，使用空字符串
                    }
                    val lastLineText =
                        buildAnnotatedString {
                            append(textOnLastLine)
                            append(suffix)
                        }
                    val lastLineLayoutResult =
                        textMeasurer.measure(
                            text = lastLineText,
                            style = style,
                            constraints = Constraints(maxWidth = containerWidth),
                        )

                    if (lastLineLayoutResult.lineCount <= 1) {
                        fits = true
                    } else {
                        cutOffIndex--
                    }
                }

                // 如果循环结束了还没找到（几乎不可能，除非后缀比一行还长），
                // 就使用最后一次尝试的截断点，但要确保在安全范围内
                val finalCutoff = cutOffIndex.coerceIn(0, text.length)

                val finalTruncatedText = if (finalCutoff > 0) {
                    text.substring(0, finalCutoff)
                } else {
                    "" // 如果截断点为0或负数，使用空字符串
                }

                collapsedText =
                    buildAnnotatedString {
                        append(finalTruncatedText)
                        append(ellipsisSuffix)

                        val link =
                            LinkAnnotation.Clickable(
                                tag = EXPAND_ACTION_TAG,
                                linkInteractionListener = { expandAction() },
                            )

                        withLink(link) {
                            withStyle(
                                style =
                                    SpanStyle(
                                        color = expandableTextColor,
                                        fontWeight = FontWeight.Bold,
                                    ),
                            ) {
                                append(expandableText)
                            }
                        }
                    }
            }
        },
    )
}

@Preview
@Composable
fun PreviewExpandableText() {
    val longText =
        "在这里, 分享不设限, 聊天无拘束。每一句话都可能是故事的开始, 每一个人都是特别的存在。欢迎你的加入, 一起制造点不一样的热闹。在这里, 分享不设限, 聊天无拘束。每一句话都可能是故事的开始, 每一个人都是特别的存在。欢迎你的加入, 一起制造点不一样的热闹吧..."

    Column(modifier = Modifier.padding(16.dp)) {
        ExpandableText(
            text = longText,
            collapsedMaxLines = 3, // Show 3 lines when collapsed
            expandableText = "全文", // Your custom suffix text
            style = TextStyle(fontSize = 16.sp, color = Color.Gray),
            expandableTextColor = Color(0xFF007BFF), // A nice blue color for the link
        )
    }
}
