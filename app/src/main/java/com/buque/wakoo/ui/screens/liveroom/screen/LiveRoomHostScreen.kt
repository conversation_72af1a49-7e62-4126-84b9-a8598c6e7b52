package com.buque.wakoo.ui.screens.liveroom.screen

import android.Manifest
import android.content.Intent
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect
import androidx.lifecycle.compose.LifecycleResumeEffect
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.manager.VoiceLiveService
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.AppNavController
import com.buque.wakoo.navigation.AppNavDisplay
import com.buque.wakoo.navigation.CtrlKey
import com.buque.wakoo.navigation.LiveRoomRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.appEntry
import com.buque.wakoo.navigation.appEntryProvider
import com.buque.wakoo.navigation.rememberAppNavController
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.widget.gift.GiftViewModel
import com.buque.wakoo.utils.PermissionUtils

object LiveRoomNavCtrlKey : CtrlKey<AppNavController>

@Composable
fun LiveRoomHostScreen(basicInfo: BasicRoomInfo) {
    val controller =
        rememberAppNavController(
            key = LiveRoomNavCtrlKey,
            LiveRoomRoute.Home,
        )

    val context = LocalContext.current

    val viewModel = LiveRoomManager.attachLiveRoomViewModel(basicInfo)

    val isInMic by viewModel.roomInfoState.rememberInMicState(LocalSelfUserProvider.currentId)

    val launcher =
        rememberLauncherForActivityResult(ActivityResultContracts.RequestPermission()) { result ->
            if (!result && isInMic) {
                showToast("未授予录音权限，其他用户将听不到你的声音".localized)
            }
        }

    if (isInMic) {
        LaunchedEffect(Unit) {
            if (!PermissionUtils.hasAudioPermission(context)) {
                launcher.launch(Manifest.permission.RECORD_AUDIO)
            }
        }
    }

    // 启动前台服务
    LifecycleResumeEffect(Unit) {
        viewModel.restoreRtcSettings()
        try {
            val intent = Intent(context, VoiceLiveService::class.java)
            context.startForegroundService(intent)
        } catch (_: Exception) {
        }
        onPauseOrDispose { }
    }

    val giftViewModel: GiftViewModel =
        viewModel<GiftViewModel>(initializer = {
            GiftViewModel(
                bid = viewModel.roomInfoState.id,
                rcId = viewModel.roomInfoState.imId,
                type = ConversationType.CHATROOM,
            )
        })

    LocalAppNavController.ProvideController(controller) {
        AppNavDisplay(
            backStack = controller.backStack,
            entryProvider =
                appEntryProvider {
                    appEntry<LiveRoomRoute.Home> {
                        BackHandler {
                            viewModel.sendEvent(RoomEvent.CollapseRoom)
                        }
                        LiveRoomManager.AutoCollapse(viewModel.roomId)

                        LifecycleEventEffect(Lifecycle.Event.ON_START) {
                            viewModel.refreshRoomInfo()
                        }

                        if (viewModel.roomInfoState.isPrivateRoom) {
                            PrivateRoomScreen(viewModel, giftViewModel)
                        } else {
                            LiveRoomScreen(viewModel, giftViewModel)
                        }
                    }
                    appEntry<LiveRoomRoute.BlackList> {
                        RoomBlackListScreen(viewModel)
                    }
                    appEntry<LiveRoomRoute.AdminList> {
                        RoomAdminListScreen(viewModel)
                    }

                    appEntry<LiveRoomRoute.BackgroundSet> {
                        RoomBackgroundSetScreen(viewModel)
                    }
                },
        )
    }
}
