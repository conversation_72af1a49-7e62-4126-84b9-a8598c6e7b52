package com.buque.wakoo.ui.floating

import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.ext.click
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.richtext.RichText
import com.buque.wakoo.ui.widget.richtext.RichTextScope
import com.buque.wakoo.utils.eventBus.tryToLink

data class TwoAvatarMessageFloatingTask(
    val avatars: List<Any> = emptyList(),
    val bgImg: Any,
    val link: Pair<String, String>? = null,
    override val priority: Int,
    val aspectRatio: Float = 7.35f,
    val content: RichTextScope.() -> Unit,
) : AbsFloatingBanner() {
    @Composable
    override fun Render() {
        Box(
            modifier =
                Modifier
                    .fillMaxWidth(0.8f)
                    .aspectRatio(aspectRatio),
            contentAlignment = Alignment.Center,
        ) {
            NetworkImage(
                data = bgImg,
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.Crop,
            )
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center,
            ) {
                Row(
                    modifier = Modifier.padding(horizontal = 8.dp),
                    horizontalArrangement = Arrangement.spacedBy((-4).dp),
                ) {
                    avatars.forEach {
                        NetworkImage(
                            data = it,
                            modifier =
                                Modifier
                                    .size(24.dp)
                                    .clip(CircleShape),
                        )
                    }
                }
                RichText(
                    content = content,
                    modifier =
                        Modifier
                            .weight(1f)
                            .basicMarquee(velocity = 60.dp),
                    fontSize = 14.sp,
                    color = WakooWhite,
                )

                if (link != null) {
                    NetworkImage(
                        data = link.first,
                        modifier =
                            Modifier
                                .padding(start = 8.dp)
                                .size(48.dp, 20.dp)
                                .click(noEffect = true) {
                                    link.second.tryToLink()
                                },
                        contentScale = ContentScale.Fit,
                    )
                }

                SizeWidth(4.dp)
            }
        }
    }
}
