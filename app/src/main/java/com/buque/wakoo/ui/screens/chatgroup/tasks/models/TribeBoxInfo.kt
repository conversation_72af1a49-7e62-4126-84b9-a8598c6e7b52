package com.buque.wakoo.ui.screens.chatgroup.tasks.models


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class Bonus(
    @SerialName("bonus_desc")
    val bonusDesc: String = "",
    @SerialName("bonus_icon")
    val bonusIcon: String = "",
    @SerialName("bonus_name")
    val bonusName: String = ""
)

@Serializable
data class BonusLevel(
    @SerialName("bonus_level")
    val level: Int,
    @SerialName("bonus_list")
    val bonusList: List<Bonus>
)

sealed class BonusWrapper {
    data class LevelItem(val level: Int) : BonusWrapper()

    data class BonusItem(val bonus: Bonus) : BonusWrapper()
}

/**
 * ```
 *     "has_can_collect_reward": false, // 是否可以领取奖励
 *     "is_collected_last_week_reward": false // 是否已经领取奖励
 * ```
 */
@Serializable
data class TribeBoxInfo(
    @SerialName("bonus_bin_rewards")
    val bonusBoxRewards: List<BonusBoxReward> = listOf(),
    @SerialName("bonus_bin_rule")
    val bonusBoxRule: String = "",
    @SerialName("bonus_mission_rule")
    val bonusTaskRule: String = "",
    @SerialName("last_week_group_active_point")
    val lastWeekTribeActivePoint: Int = 0,
    @SerialName("group_active_point")
    val tribeActivePoint: Int = 0,
    @SerialName("has_can_collect_reward")
    val canCollect: Boolean = false,
    @SerialName("is_collected_last_week_reward")
    val hasCollect: Boolean = false
) {

    val collectEnable: Boolean
        get() = if (canCollect) hasCollect.not() else hasCollect

    @Serializable
    data class BonusBoxReward(
        @SerialName("bonus_level")
        val bonusLevel: Int = 0,
        @SerialName("bonus_list")
        val bonusList: List<Bonus> = listOf(),
        @SerialName("bonus_rule")
        val bonusRule: String = "",
        @SerialName("is_finished")
        val isFinished: Boolean = false,
        @SerialName("tribe_active_point")
        val tribeActivePoint: Int = 0,
        @SerialName("user_active_point")
        val userActivePoint: Int = 0,
        @SerialName("need_finish_make_cp_task")
        val needMakeCp: Boolean = false,
        @SerialName("need_finish_public_cp_task")
        val needPublicCp: Boolean = false,
        @SerialName("bonus_task_requirement")
        val taskRequirement: String = ""
    )
}