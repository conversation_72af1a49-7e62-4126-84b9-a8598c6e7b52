package com.buque.wakoo.ui.screens.dressup

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.ModalBottomSheetProperties
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.DressUpCategory
import com.buque.wakoo.bean.DressUpPropItem
import com.buque.wakoo.bean.DressUpPropItem.PriceConf
import com.buque.wakoo.bean.DressUpTabsItem
import com.buque.wakoo.bean.DressUpUIItem
import com.buque.wakoo.bean.DressupTab
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.navigation.dialog.CenterDialog
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.navigation.dialog.filterDoResult
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.ui.dialog.DialogButton
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.icons.ArrowRight
import com.buque.wakoo.ui.icons.DressUpLine
import com.buque.wakoo.ui.icons.GreenCircleChecked
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.widget.EffectVideoAnimationLoopView
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.richtext.RichText
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.dressup.DressUpShopViewModel
import com.buque.wakoo.viewmodel.dressup.DressUpTabViewModel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

//region 装扮tab
@Composable
fun DressupShopTabScreen(
    modifier: Modifier = Modifier,
    toTabListScreen: (tabName: String, tabType: Int) -> Unit,
    toDressUpCenter: (tabType: Int) -> Unit,
) {
    var selectedItem =
        remember {
            mutableStateOf<DressUpPropItem?>(null)
        }
    val viewModel = viewModel<DressUpTabViewModel>()
    val diamondBalance by viewModel.diamondState.collectAsStateWithLifecycle()
    val integralBalance by viewModel.integralState.collectAsStateWithLifecycle()
    val scope = rememberCoroutineScope()
    val dialogController = rememberDialogController()
    val config by AppConfigManager.uiConfigFlow.collectAsState()

    LaunchedEffect(Unit) {
        dialogController.events
            .onEach { event ->
                event.filterDoResult("lookDressUp") {
//                    if (it.result == true) {
//                        toDressUpCenter(-1)
//                    }
                    val item = it.result as? DressUpPropItem
                    if (item != null) {
                        toDressUpCenter(item.propInfo.propType)
                    }
                }
            }.launchIn(this)
    }
    val cState = viewModel.state.value
    SegColorTitleScreenScaffold("装扮商城".localized, actions = {
        WakooTitleBarDefaults.IconButtonAction(WakooIcons.DressUpLine, onClick = {
            toDressUpCenter(-1)
        })
    }) { pv ->
        CStateLayout(cState, onRetry = {
            viewModel.refreshState()
        }) {
            DressupShopTabContent(it, pv, config.decoStoreHasSweat, toTabListScreen, onItemClicked = {
                selectedItem.value = it
            })
        }

        DressUpItemDetail(selectedItem, diamondBalance, integralBalance, config.decoStoreHasSweat) { item, conf, payway ->
            scope.launch {
                viewModel.purchase(item, conf.day, payway).onSuccess {
                    val dressUpPropItem = selectedItem.value!!
                    dialogController.post(PurshaseSuccessDialog(dressUpPropItem))
                    selectedItem.value = null
                }
            }
        }
    }
}

@Composable
private fun DressupShopTabContent(
    list: List<DressUpUIItem>,
    pv: PaddingValues,
    decoStoreHasSweat: Boolean = false,
    toTabListScreen: (tabName: String, tabType: Int) -> Unit = { _, _ -> },
    onItemClicked: (DressUpPropItem) -> Unit = {},
) {
    LazyVerticalGrid(
        GridCells.Fixed(3),
        modifier =
            Modifier
                .padding(pv)
                .fillMaxSize()
                .background(color = Color.White),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(horizontal = 16.dp),
    ) {
        itemsIndexed(list, span = { index, it ->
            GridItemSpan(it.span)
        }) { index, item ->
            when (item) {
                is DressUpTabsItem -> {
                    LazyRow(horizontalArrangement = Arrangement.spacedBy(24.dp)) {
                        items(item.tabs) { tab ->
                            Box(
                                modifier =
                                    Modifier
                                        .widthIn(min = 64.dp)
                                        .click(noEffect = true) {
                                            toTabListScreen(tab.tabName, tab.propType)
                                        },
                                contentAlignment = Alignment.BottomCenter,
                            ) {
                                Box(
                                    modifier =
                                        Modifier
                                            .width(64.dp)
                                            .height(48.dp)
                                            .background(
                                                brush =
                                                    Brush.verticalGradient(
                                                        listOf(
                                                            Color(0xFFDAFFCD),
                                                            Color(0xfff1fff2),
                                                        ),
                                                    ),
                                                RoundedCornerShape(12.dp),
                                            ),
                                )
                                Column(
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                ) {
                                    NetworkImage(
                                        tab.propBackground,
                                        modifier = Modifier.size(58.dp, 40.dp),
                                        contentScale = ContentScale.Inside,
                                    )
                                    SizeHeight(3.dp)
                                    Text(
                                        tab.tabName,
                                        color = Color(0xFFB6B6B6),
                                        fontSize = 11.sp,
                                        lineHeight = 15.sp,
                                    )
                                    SizeHeight(4.dp)
                                }
                            }
                        }
                    }
                }

                is DressUpCategory -> {
                    Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            item.tabName,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.SemiBold,
                            lineHeight = 22.sp,
                            color = Color(0xff111111),
                        )
                        Spacer(Modifier.weight(1f))
                        Text(
                            "查看更多".localized,
                            fontSize = 11.sp,
                            lineHeight = 22.sp,
                            color = Color(0xffb6b6b6),
                            fontWeight = FontWeight.Medium,
                            modifier =
                                Modifier
                                    .padding(vertical = 8.dp)
                                    .click(noEffect = true) {
                                        toTabListScreen(item.tabName, item.propType)
                                    },
                        )
                        Icon(
                            WakooIcons.ArrowRight,
                            contentDescription = "",
                            tint = Color(0xffb6b6b6),
                        )
                    }
                }

                is DressUpPropItem -> {
                    DressUpPropItemContent(item, showCoin = decoStoreHasSweat) {
                        onItemClicked(item)
                    }
                }

                else -> {}
            }
        }
    }
}

@Preview
@Composable
private fun previewShopTabList() {
    val jsonStr =
        "[{\"tab_name\":\"头像框\",\"list\":[{\"id\":7,\"prop_info\":{\"id\":3,\"name\":\"44\",\"icon\":\"https://s.test.wakooclub.com/aaceFc\",\"prop_type\":1,\"gain_type\":2,\"prop_desc\":null},\"price_conf\":[{\"day\":3,\"coin\":100,\"diamond\":0},{\"day\":7,\"coin\":500,\"diamond\":0},{\"day\":30,\"coin\":1500,\"diamond\":0}],\"preview_url\":null,\"native_region\":0},{\"id\":6,\"prop_info\":{\"id\":2,\"name\":\"33\",\"icon\":\"https://s.test.wakooclub.com/aaceFf\",\"prop_type\":1,\"gain_type\":2,\"prop_desc\":\"这是商品描述这是商品描述这是商品描述这是商品描述这是商品描述这是商品描述这是商品描述这是商品描述\"},\"price_conf\":[{\"day\":3,\"coin\":100,\"diamond\":0},{\"day\":7,\"coin\":500,\"diamond\":0},{\"day\":30,\"coin\":2000,\"diamond\":0}],\"preview_url\":null,\"native_region\":0},{\"id\":5,\"prop_info\":{\"id\":1,\"name\":\"男\",\"icon\":\"https://s.test.wakooclub.com/aaceFe\",\"prop_type\":1,\"gain_type\":1,\"prop_desc\":\"这是商品描述这是商品描述这是商品描述这是商品描述这是商品描述\"},\"price_conf\":[{\"day\":3,\"coin\":100,\"diamond\":0},{\"day\":7,\"coin\":500,\"diamond\":0},{\"day\":30,\"coin\":2500,\"diamond\":0}],\"preview_url\":null,\"native_region\":0},{\"id\":4,\"prop_info\":{\"id\":10,\"name\":\"“新年舞会”头像框\",\"icon\":\"https://s.test.wakooclub.com/aacevi\",\"prop_type\":1,\"gain_type\":2,\"prop_desc\":null},\"price_conf\":[{\"day\":3,\"coin\":300,\"diamond\":0},{\"day\":7,\"coin\":1500,\"diamond\":0},{\"day\":30,\"coin\":8000,\"diamond\":0}],\"preview_url\":null,\"native_region\":0},{\"id\":3,\"prop_info\":{\"id\":22,\"name\":\"“万圣之夜”头像框\",\"icon\":\"https://s.wakooclub.com/aabLde\",\"prop_type\":1,\"gain_type\":1,\"prop_desc\":\"\"},\"price_conf\":[{\"day\":3,\"coin\":100,\"diamond\":0},{\"day\":7,\"coin\":200,\"diamond\":0},{\"day\":30,\"coin\":800,\"diamond\":0}],\"preview_url\":null,\"native_region\":0},{\"id\":2,\"prop_info\":{\"id\":24,\"name\":\"“黄金骑士”头像框\",\"icon\":\"https://s.test.wakooclub.com/aacepD\",\"prop_type\":1,\"gain_type\":2,\"prop_desc\":null},\"price_conf\":[{\"day\":3,\"coin\":100,\"diamond\":0},{\"day\":7,\"coin\":200,\"diamond\":0},{\"day\":30,\"coin\":800,\"diamond\":0}],\"preview_url\":null,\"native_region\":0}],\"prop_type\":1,\"prop_background\":\"https://media.wakooclub.com/opsite%2Fdressup%2Fstore%2Fprop_store_tab_avatar_frame_bg.png\"},{\"tab_name\":\"勋章\",\"prop_type\":2,\"prop_background\":\"https://media.wakooclub.com/opsite%2Fdressup%2Fstore%2Fprop_store_tab_medal_bg.png\",\"list\":[{\"id\":15,\"prop_info\":{\"id\":2,\"name\":\"22\",\"icon\":\"https://s.test.wakooclub.com/aacevk\",\"prop_type\":2,\"gain_type\":1,\"prop_desc\":null,\"icon_width\":72,\"icon_height\":24},\"price_conf\":[{\"day\":3,\"coin\":200,\"diamond\":0},{\"day\":7,\"coin\":1000,\"diamond\":0},{\"day\":30,\"coin\":5000,\"diamond\":0}],\"preview_url\":null,\"native_region\":0},{\"id\":14,\"prop_info\":{\"id\":1,\"name\":\"神仙眷侣\",\"icon\":\"https://s.test.wakooclub.com/aaceEK\",\"prop_type\":2,\"gain_type\":2,\"prop_desc\":null,\"icon_width\":74,\"icon_height\":24},\"price_conf\":[{\"day\":3,\"coin\":100,\"diamond\":0},{\"day\":7,\"coin\":500,\"diamond\":0},{\"day\":30,\"coin\":2000,\"diamond\":0}],\"preview_url\":null,\"native_region\":0},{\"id\":13,\"prop_info\":{\"id\":45,\"name\":\"“娇夫悍妻”女款-80定制\",\"icon\":\"https://s.wakooclub.com/aabLeq\",\"prop_type\":2,\"gain_type\":1,\"prop_desc\":\"\",\"icon_width\":72,\"icon_height\":24},\"price_conf\":[{\"day\":3,\"coin\":100,\"diamond\":0},{\"day\":7,\"coin\":200,\"diamond\":0},{\"day\":30,\"coin\":500,\"diamond\":0}],\"preview_url\":null,\"native_region\":0},{\"id\":12,\"prop_info\":{\"id\":44,\"name\":\"“娇夫悍妻”男款-80定制\",\"icon\":\"https://s.wakooclub.com/aabLep\",\"prop_type\":2,\"gain_type\":1,\"prop_desc\":\"\",\"icon_width\":72,\"icon_height\":24},\"price_conf\":[{\"day\":3,\"coin\":100,\"diamond\":0},{\"day\":7,\"coin\":200,\"diamond\":0},{\"day\":30,\"coin\":500,\"diamond\":0}],\"preview_url\":null,\"native_region\":0},{\"id\":11,\"prop_info\":{\"id\":11,\"name\":\"nice地主\",\"icon\":\"https://s.test.wakooclub.com/aacepE\",\"prop_type\":2,\"gain_type\":2,\"prop_desc\":null,\"icon_width\":72,\"icon_height\":24},\"price_conf\":[{\"day\":3,\"coin\":100,\"diamond\":0},{\"day\":7,\"coin\":200,\"diamond\":0},{\"day\":30,\"coin\":300,\"diamond\":0}],\"preview_url\":null,\"native_region\":0}]}]"
    val tabs = AppJson.decodeFromString<List<DressupTab>>(jsonStr)
    val data = DressUpTabViewModel.convertToUIItems(tabs)
    DressupShopTabContent(data, PaddingValues(16.dp))
}
//endregion

//region 装扮tab详情列表

@Composable
fun DressupShopListScreen(
    type: Int,
    title: String,
    modifier: Modifier = Modifier,
    toDressUpCenter: () -> Unit,
) {
    var selectedItem =
        remember {
            mutableStateOf<DressUpPropItem?>(null)
        }
    val viewModel =
        viewModel<DressUpShopViewModel>(initializer = {
            DressUpShopViewModel(type)
        })
    val listState = rememberLazyGridState()
    val diamondBalance by viewModel.diamondState.collectAsStateWithLifecycle()
    val integralBalance by viewModel.integralState.collectAsStateWithLifecycle()
    val scope = rememberCoroutineScope()

    val dialogController = rememberDialogController()
    val config by AppConfigManager.uiConfigFlow.collectAsState()

    LaunchedEffect(Unit) {
        dialogController.events
            .onEach { event ->
                event.filterDoResult("lookDressUp") {
                    if (it.result == true) {
                        toDressUpCenter()
                    }
                }
            }.launchIn(this)
    }

    SegColorTitleScreenScaffold(title) {
        CStateListPaginateLayout<Any, Int, DressUpPropItem, DressUpShopViewModel>(
            "",
            emptyText = "暂无装扮".localized,
            viewModel = viewModel,
            listState = listState,
            modifier =
                Modifier
                    .background(color = Color.White)
                    .padding(it)
                    .fillMaxSize(),
        ) { _, list ->
            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                state = listState,
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                contentPadding = PaddingValues(horizontal = 16.dp),
            ) {
                items(list) {
                    DressUpPropItemContent(it, showCoin = config.decoStoreHasSweat) {
                        selectedItem.value = it
                    }
                }
            }

            DressUpItemDetail(selectedItem, diamondBalance, integralBalance, config.decoStoreHasSweat) { item, conf, payway ->
                scope.launch {
                    viewModel.purchase(item, conf.day, payway).onSuccess {
                        val dressUpPropItem = selectedItem.value!!
                        dialogController.post(PurshaseSuccessDialog(dressUpPropItem))
                        selectedItem.value = null
                    }
                }
            }
        }
    }
}

//endregion

//region 道具详情弹窗

/**
 * @param onBuyClick item 购买项目 conf 购买价格 payway付款方式0钻石1金币
 */
@Composable
private fun DressUpItemDetail(
    state: MutableState<DressUpPropItem?>,
    diamondBalance: Int = 0,
    integralBalance: Int = 0,
    showCoin: Boolean = false,
    onBuyClick: (item: DressUpPropItem, conf: PriceConf, payWay: Int) -> Unit = { _, _, _ -> }, // 购买回调
) {
    // 选中的购买时长
    val item = state.value
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    if (item != null) {
        ModalBottomSheet(
            onDismissRequest = {
                state.value = null
            },
            sheetState = sheetState,
            properties =
                ModalBottomSheetProperties(
                    shouldDismissOnBackPress = false,
                ),
            dragHandle = null,
        ) {
            var selectPrice by remember {
                mutableStateOf(item.priceConf.firstOrNull())
            }
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .heightIn(min = 156.dp)
                            .background(
                                brush =
                                    Brush.verticalGradient(
                                        listOf(
                                            Color(0xff04696d),
                                            Color(0xff001028),
                                        ),
                                    ),
                            ),
                ) {
                    // 道具名称
                    Text(
                        text = item.propInfo.name,
                        color = Color(0xFF3BC47F),
                        modifier =
                            Modifier
                                .padding(16.dp)
                                .background(color = Color(0xFFF7F7F7), CircleShape)
                                .padding(horizontal = 12.dp),
                        fontSize = 14.sp,
                        lineHeight = 22.sp,
                        textAlign = TextAlign.Center,
                        fontWeight = FontWeight.Bold,
                    )

                    // 头像1 勋章2 特效3 气泡5

                    // 道具大图
                    if (item.propInfo.propType == 1) {
                        Box(
                            contentAlignment = Alignment.Center,
                            modifier =
                                Modifier
                                    .align(Alignment.Center),
                        ) {
                            SelfUser?.let {
                                AvatarNetworkImage(it, size = 72.dp)
                            }
                            NetworkImage(
                                item.propInfo.icon,
                                modifier =
                                    Modifier
                                        .size(98.dp),
                                contentScale = ContentScale.Fit,
                            )
                        }
                    } else if (item.propInfo.propType == 3) {
                        Box(
                            modifier =
                                Modifier
                                    .padding(vertical = 20.dp)
                                    .width(139.dp)
                                    .aspectRatio(0.48601399f)
                                    .background(Color.Black)
                                    .align(Alignment.Center),
                        ) {
                            Image(
                                painter = painterResource(R.drawable.ic_effect_bg),
                                modifier = Modifier.fillMaxSize(),
                                contentDescription = null,
                            )
                            if (!item.propInfo.effectFile.isNullOrBlank()) {
                                EffectVideoAnimationLoopView(item.propInfo.effectFile)
                            } else {
                                NetworkImage(
                                    item.propInfo.icon,
                                    modifier = Modifier.fillMaxSize(),
                                    contentScale = ContentScale.Fit,
                                )
                            }
                        }
                    } else {
                        NetworkImage(
                            item.propInfo.icon,
                            modifier =
                                Modifier
                                    .align(Alignment.Center)
                                    .size(98.dp),
                            contentScale = ContentScale.Fit,
                        )
                    }
                }
                Column(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .background(color = Color.White)
                            .padding(horizontal = 16.dp, vertical = 16.dp),
                ) {
                    if (item.propInfo.gainType == 1) {
                        // 商品描述
                        Text(
                            text = "商品描述".localized,
                            color = Color(0xFFB6B6B6),
                            fontSize = 14.sp,
                            modifier = Modifier.align(Alignment.Start),
                        )
                        SizeHeight(33.dp)
                        Text(
                            "限时活动获得, 暂无法购买".localized,
                            modifier = Modifier.fillMaxWidth(),
                            fontSize = 12.sp,
                            lineHeight = 22.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xffb6b6b6),
                            textAlign = TextAlign.Center,
                        )
                    } else {
                        // 购买时长选择
                        Text(
                            text = "选择购买时长".localized,
                            color = Color(0xFFB6B6B6),
                            fontSize = 14.sp,
                        )
                        Spacer(modifier = Modifier.height(12.dp))
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(16.dp),
                        ) {
                            item.priceConf.forEach { conf ->
                                val isSelected = conf == selectPrice
                                Box(
                                    modifier =
                                        Modifier
                                            .weight(1f)
                                            .height(32.dp)
                                            .background(
                                                color = if (isSelected) Color(0x1A66FE6B) else Color(0xFFF7F7F7),
                                                shape = CircleShape,
                                            ).border(
                                                2.dp,
                                                color = if (isSelected) Color(0xFF66FE6B) else Color.Transparent,
                                                shape = CircleShape,
                                            ).click(noEffect = true) { selectPrice = conf },
                                ) {
                                    Text(
                                        text = "%s天".localizedFormat(conf.day),
                                        color = Color(0xFF222222),
                                        fontSize = 14.sp,
                                        lineHeight = 22.sp,
                                        fontWeight = FontWeight.SemiBold,
                                        modifier = Modifier.align(Alignment.Center),
                                    )
                                    if (isSelected) {
                                        Image(
                                            WakooIcons.GreenCircleChecked,
                                            contentDescription = "",
                                            modifier =
                                                Modifier
                                                    .size(16.dp)
                                                    .align(Alignment.BottomEnd),
                                        )
                                    }
                                }
                            }
                        }

                        Spacer(modifier = Modifier.height(24.dp))

                        // 商品描述
                        Text(
                            text = "商品描述".localized,
                            color = Color(0xFFB6B6B6),
                            fontSize = 14.sp,
                            modifier = Modifier.align(Alignment.Start),
                        )
                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = item.propInfo.propDesc ?: "无".localized,
                            color = Color(0xFF222222),
                            fontSize = 15.sp,
                            modifier = Modifier.align(Alignment.Start),
                        )

                        Spacer(modifier = Modifier.height(32.dp))

                        if (showCoin) {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                Box(
                                    modifier =
                                        Modifier
                                            .weight(1f)
                                            .height(44.dp)
                                            .background(color = Color(0xff111111), shape = CircleShape)
                                            .click {
                                                selectPrice?.let {
                                                    onBuyClick(item, it, 0)
                                                }
                                            },
                                    contentAlignment = Alignment.Center,
                                ) {
                                    RichText(
                                        color = Color(0xff66FE6B),
                                        fontSize = 16.sp,
                                        textAlign = TextAlign.Center,
                                        key = selectPrice,
                                    ) {
                                        append("钻石购买:".localized)
                                        InlineSizedContent(18.dp, 18.dp) {
                                            Image(
                                                painter = painterResource(R.drawable.ic_green_diamond_straight),
                                                contentDescription = null,
                                                modifier = Modifier.size(18.dp),
                                            )
                                        }
                                        withBuilder {
                                            withStyle(
                                                SpanStyle(
                                                    fontSize = 12.sp,
                                                ),
                                            ) {
                                                append(selectPrice?.diamond.toString())
                                            }
                                        }
                                    }
                                }
                                SizeWidth(15.dp)
                                Box(
                                    modifier =
                                        Modifier
                                            .weight(1f)
                                            .height(44.dp)
                                            .background(
                                                brush =
                                                    Brush.horizontalGradient(
                                                        listOf(
                                                            Color(0xffA3FF2C),
                                                            Color(0xff31FFA1),
                                                        ),
                                                    ),
                                                shape = CircleShape,
                                            ).click {
                                                selectPrice?.let {
                                                    onBuyClick(item, it, 1)
                                                }
                                            },
                                    contentAlignment = Alignment.Center,
                                ) {
                                    RichText(
                                        color = Color(0xff111111),
                                        fontSize = 16.sp,
                                        textAlign = TextAlign.Center,
                                        key = selectPrice,
                                    ) {
                                        append("积分兑换:".localized)
                                        InlineSizedContent(24.dp, 24.dp) {
                                            Image(
                                                painter = painterResource(R.drawable.ic_golden_coin),
                                                contentDescription = null,
                                                modifier = Modifier.size(24.dp),
                                            )
                                        }
                                        withBuilder {
                                            withStyle(
                                                SpanStyle(
                                                    fontSize = 12.sp,
                                                ),
                                            ) {
                                                append(selectPrice?.coin.toString())
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            // 底部余额、价格、购买按钮
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                RichText(
                                    modifier =
                                        Modifier
                                            .background(Color(0xFFF6F6F8), shape = CircleShape)
                                            .padding(horizontal = 6.dp, vertical = 4.dp),
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis,
                                ) {
                                    withBuilder {
                                        withStyle(
                                            SpanStyle(
                                                color = Color(0xffb6b6b6),
                                                fontSize = 11.sp,
                                            ),
                                        ) {
                                            append("账户余额:".localized)
                                        }
                                    }
                                    InlineSizedContent(18.dp, 18.dp) {
                                        Image(
                                            painter = painterResource(R.drawable.ic_green_diamond_straight),
                                            contentDescription = null,
                                            modifier = Modifier.size(18.dp),
                                        )
                                    }
                                    withBuilder {
                                        withStyle(
                                            SpanStyle(
                                                color = Color(0xFF3BC47F),
                                                fontSize = 14.sp,
                                                fontWeight = FontWeight.Bold,
                                            ),
                                        ) {
                                            append(diamondBalance.toString())
                                        }
                                    }
                                }
                                Spacer(modifier = Modifier.weight(1f))
                                // 价格
                                Row(
                                    modifier =
                                        Modifier
                                            .background(Color(0xff111111), shape = CircleShape)
                                            .padding(6.dp),
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    Image(
                                        painter = painterResource(R.drawable.ic_green_diamond_straight),
                                        contentDescription = null,
                                        modifier = Modifier.size(18.dp),
                                    )
                                    Text(
                                        text = "${selectPrice?.diamond}",
                                        color = Color(0xFF3BC47F),
                                        fontSize = 14.sp,
                                        fontWeight = FontWeight.Bold,
                                        modifier = Modifier.padding(start = 4.dp),
                                    )

                                    GradientButton(
                                        "立即购买".localized,
                                        height = 28.dp,
                                        modifier = Modifier.padding(start = 8.dp),
                                        onClick = {
                                            selectPrice?.let {
                                                onBuyClick(item, it, 0)
                                            }
                                        },
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
@Preview
private fun previewDressUpItemDetail() {
    val source =
        "{\"id\":7,\"prop_info\":{\"id\":3,\"name\":\"这是装扮名称\",\"icon\":\"https://s.test.wakooclub.com/aaceFc\",\"prop_type\":1,\"gain_type\":2,\"prop_desc\":null},\"price_conf\":[{\"day\":3,\"coin\":100,\"diamond\":0},{\"day\":7,\"coin\":500,\"diamond\":0},{\"day\":30,\"coin\":1500,\"diamond\":0}],\"preview_url\":null,\"native_region\":0}"
    val bean = AppJson.decodeFromString<DressUpPropItem>(source)
    var state =
        remember {
            mutableStateOf<DressUpPropItem?>(bean)
        }
    DressUpItemDetail(state)
}
//endregion

/**
 * 装扮道具item content
 */
@Composable
private fun DressUpPropItemContent(
    item: DressUpPropItem,
    showCoin: Boolean = false,
    onItemClicked: () -> Unit = {},
) {
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .click(onClick = onItemClicked, noEffect = true),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        NetworkImage(
            item.propInfo.icon,
            modifier =
                Modifier
                    .aspectRatio(0.85714286f)
                    .background(color = Color(0xFFF6F6F8), shape = RoundedCornerShape(10.dp)),
            contentScale = ContentScale.Inside,
        )

        Text(
            text = item.propInfo.name,
            modifier =
                Modifier
                    .padding(top = 6.dp)
                    .height(18.dp),
            color = Color(0xFF222222),
            fontSize = 11.sp,
            lineHeight = 22.sp,
            fontWeight = FontWeight.SemiBold,
            maxLines = 1,
        )
        SizeHeight(4.dp)
        Row(verticalAlignment = Alignment.CenterVertically) {
            val lowestPrice = item.priceConf.firstOrNull()
            if (lowestPrice != null && item.propInfo.gainType != 1) {
                RichText(
                    fontSize = 12.sp,
                    color = Color(0xffc8ccd7),
                ) {
                    InlineContent {
                        Image(
                            painter = painterResource(R.drawable.ic_green_diamond_straight),
                            contentDescription = "",
                            modifier = Modifier.size(12.dp),
                            contentScale = ContentScale.Fit,
                        )
                    }
                    append(lowestPrice.diamond.toString())
                    if (showCoin) {
                        append("/")
                        InlineContent {
                            Image(
                                painter = painterResource(R.drawable.ic_golden_coin),
                                contentDescription = "",
                                modifier = Modifier.size(16.dp),
                                contentScale = ContentScale.Fit,
                            )
                        }
                        append(lowestPrice.coin.toString())
                    }
                }
            } else {
                Text(
                    "活动获得".localized,
                    fontSize = 10.sp,
                    lineHeight = 22.sp,
                    color = Color(0xFF52CF84),
                    modifier =
                        Modifier
                            .background(color = Color(0xFFCFFFBE), RoundedCornerShape(4.dp))
                            .padding(
                                horizontal = 7.dp,
                                vertical = 3.dp,
                            ),
                )
            }
        }
    }
}

@Serializable
private class PurshaseSuccessDialog(
    val item: DressUpPropItem,
) : CenterDialog<Unit>() {
    @Composable
    override fun DialogScope.Content(param: Unit) {
        Surface(
            modifier =
                Modifier
                    .width(270.dp)
                    .wrapContentHeight(),
            shape = RoundedCornerShape(8.dp),
            color = Color.White,
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                // 标题
                Text(
                    text = "购买成功".localized,
                    style =
                        MaterialTheme.typography.titleMedium.copy(
                            fontSize = 17.sp,
                            lineHeight = 24.sp,
                        ),
                    color = Color(0xFF111111),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth(),
                )

                SizeHeight(12.dp)

                NetworkImage(
                    item.propInfo.icon,
                    modifier =
                        Modifier
                            .width(72.dp)
                            .aspectRatio(0.85714286f)
                            .background(color = Color(0xFFF6F6F8), shape = RoundedCornerShape(10.dp)),
                    contentScale = ContentScale.Inside,
                )

                Text(
                    text = item.propInfo.name,
                    modifier =
                        Modifier
                            .padding(top = 6.dp)
                            .height(18.dp),
                    color = Color(0xFF222222),
                    fontSize = 11.sp,
                    lineHeight = 22.sp,
                    fontWeight = FontWeight.SemiBold,
                    maxLines = 1,
                )
                SizeHeight(10.dp)

                Text("道具已放入您的装扮中心".localized, fontSize = 11.sp, lineHeight = 22.sp, color = Color(0xff999999))

                SizeHeight(20.dp)

                // 按钮区域
                Row(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                ) {
                    // 取消按钮
                    DialogButton(
                        config = DialogButtonStyles.Secondary.copy(text = "知道了".localized),
                        onClick = {
                            dismiss(null, "lookDressUp")
                        },
                        modifier = Modifier.weight(1f),
                    )

                    // 确认按钮
                    DialogButton(
                        config = DialogButtonStyles.Primary.copy(text = "去查看".localized),
                        onClick = {
                            dismiss(item, "lookDressUp")
                        },
                        modifier = Modifier.weight(1f),
                    )
                }
            }
        }
    }
}
