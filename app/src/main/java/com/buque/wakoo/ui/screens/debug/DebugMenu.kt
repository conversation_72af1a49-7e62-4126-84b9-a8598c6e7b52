package com.buque.wakoo.ui.screens.debug

import android.content.ClipData
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalClipboard
import androidx.compose.ui.platform.toClipEntry
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.buque.wakoo.BuildConfig
import com.buque.wakoo.app.DevicesKV
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.DeviceInfoManager
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.utils.eventBus.EventBus
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 调试菜单
 * 仅在 Debug 模式下显示，提供各种调试功能
 */
@Composable
fun DebugMenu(onDismiss: () -> Unit) {
    var showEnvironmentDialog by remember { mutableStateOf(false) }
    var showGitCommitsDialog by remember { mutableStateOf(false) }
    var showAccountManagerDialog by remember { mutableStateOf(false) }

    // 只在 Debug 模式下显示
    if (EnvironmentManager.isProdRelease) {
        LaunchedEffect(Unit) {
            onDismiss()
        }
        return
    }

    val scope = rememberCoroutineScope()

    Dialog(
        onDismissRequest = onDismiss,
        properties =
            DialogProperties(
                usePlatformDefaultWidth = false,
            ),
    ) {
        Card(
            modifier =
                Modifier
                    .fillMaxSize(),
            shape = RoundedCornerShape(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .padding(12.dp),
            ) {
                // 标题栏
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Text(
                        text = "调试菜单",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                    )

                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 应用信息
                Card(
                    modifier =
                        Modifier
                            .animateContentSize()
                            .fillMaxWidth(),
                ) {
                    var expanded by remember {
                        mutableStateOf(false)
                    }
                    Row(
                        modifier =
                            Modifier.noEffectClick {
                                expanded = !expanded
                            },
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text(
                            text = "账号信息",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium,
                        )
                        Weight()
                        Icon(
                            if (!expanded) {
                                Icons.AutoMirrored.Filled.KeyboardArrowRight
                            } else {
                                Icons.Default.KeyboardArrowDown
                            },
                            null,
                        )
                    }

                    if (expanded) {
                        Column {
                            InfoRow("手机号", DevicesKV.decodeString("_debug_current_phone").orEmpty())
                            InfoRow("设备id", DeviceInfoManager.deviceId)
                            InfoRow("uid", SelfUser?.id.orEmpty())
                            InfoRow("是否是主播", SelfUser?.isHQU?.toString().orEmpty())
                            InfoRow("是否是日区", SelfUser?.isJP?.toString().orEmpty())
                        }
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 应用信息
                Card(
                    modifier =
                        Modifier
                            .animateContentSize()
                            .fillMaxWidth(),
                ) {
                    var expanded by remember {
                        mutableStateOf(true)
                    }
                    Row(
                        modifier =
                            Modifier.noEffectClick {
                                expanded = !expanded
                            },
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text(
                            text = "应用信息",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium,
                        )
                        Weight()
                        Icon(
                            if (!expanded) {
                                Icons.AutoMirrored.Filled.KeyboardArrowRight
                            } else {
                                Icons.Default.KeyboardArrowDown
                            },
                            null,
                        )
                    }

                    if (expanded) {
                        Column {
                            InfoRow("版本", "${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})")
                            InfoRow("构建类型", BuildConfig.BUILD_TYPE)
                            InfoRow("构建环境", BuildConfig.FLAVOR_environment)
                            InfoRow("渠道", BuildConfig.FLAVOR_channel)
                            InfoRow("当前环境", EnvironmentManager.getCurrentEnvironmentKey())
                        }
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Git 信息
                Card(
                    modifier =
                        Modifier
                            .animateContentSize()
                            .fillMaxWidth(),
                ) {
                    var expanded by remember {
                        mutableStateOf(true)
                    }
                    Row(
                        modifier =
                            Modifier.noEffectClick {
                                expanded = !expanded
                            },
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text(
                            text = "Git 信息",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium,
                        )

                        Weight()

                        Icon(
                            if (!expanded) {
                                Icons.AutoMirrored.Filled.KeyboardArrowRight
                            } else {
                                Icons.Default.KeyboardArrowDown
                            },
                            null,
                        )
                    }

                    if (expanded) {
                        Column {
                            InfoRow("分支", BuildConfig.GIT_BRANCH)
                            InfoRow("提交", BuildConfig.GIT_SHORT_COMMIT_HASH)
                            InfoRow("消息", BuildConfig.GIT_COMMIT_MESSAGE)
                            InfoRow("作者", BuildConfig.GIT_COMMIT_AUTHOR)
                            InfoRow("提交时间", BuildConfig.GIT_COMMIT_DATE)
                            InfoRow(
                                "构建时间",
                                SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                                    .format(Date(BuildConfig.GIT_BUILD_TIME)),
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 调试功能列表
                Text(
                    text = "调试功能",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                )

                Spacer(modifier = Modifier.height(8.dp))

                LazyVerticalStaggeredGrid(
                    columns = StaggeredGridCells.Fixed(2),
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .weight(1f),
                    horizontalArrangement = Arrangement.spacedBy(5.dp),
                    verticalItemSpacing = 5.dp,
                ) {
                    item {
                        DebugMenuItem(
                            title = "环境切换",
                            description = "切换到不同的环境配置",
                            onClick = {
                                showEnvironmentDialog = true
                            },
                        )
                    }

                    item {
                        DebugMenuItem(
                            title = "Git 提交记录",
                            description = "查看最近5条提交记录",
                            onClick = {
                                showGitCommitsDialog = true
                            },
                        )
                    }

                    item {
                        DebugMenuItem(
                            title = "日志开关: ${if (EnvironmentManager.current.enableLog) "on" else "off"}",
                            description = "切换将重启app",
                            onClick = {
                                EnvironmentManager.switchLogEnable(!EnvironmentManager.current.enableLog)
                            },
                        )
                    }

                    item {
                        DebugMenuItem(
                            title = "debug",
                            description = "调试页面",
                            onClick = {
                                onDismiss()
                                LocalAppNavController.useRoot?.push(Route.Debug)
                            },
                        )
                    }

                    item {
                        DebugMenuItem(
                            title = "退出登录",
                            description = "返回到登录页面",
                            onClick = {
                                onDismiss()
                                scope.launch {
                                    AccountManager.logout()
                                }
                            },
                        )
                    }

                    item {
                        DebugMenuItem(
                            title = "账号管理",
                            description = "管理保存常用号码方便切换",
                            enabled = EnvironmentManager.isDevelopmentEnvironment,
                            onClick = {
                                showAccountManagerDialog = true
                            },
                        )
                    }

                    item {
                        DebugMenuItem(
                            title = "一键注册女号",
                            description = "手机号随机(可以设置号段)",
                            enabled = EnvironmentManager.isDevelopmentEnvironment,
                            onClick = {
                                appCoroutineScope.launch {
                                    onDismiss()
                                    AccountManager.logout()
                                    delay(500)
                                    EventBus.trySend(
                                        DebugRegister(
                                            false,
                                            DevicesKV.decodeString("_debug_account_prefix_mask_false").orEmpty(),
                                            DevicesKV.decodeString("_debug_account_suffix_mask_false").orEmpty(),
                                        ),
                                    )
                                }
                            },
                        )
                    }

                    item {
                        DebugMenuItem(
                            title = "一键注册男号",
                            description = "手机号随机(可以设置号段)",
                            enabled = EnvironmentManager.isDevelopmentEnvironment,
                            onClick = {
                                appCoroutineScope.launch {
                                    onDismiss()
                                    AccountManager.logout()
                                    delay(500)
                                    EventBus.trySend(
                                        DebugRegister(
                                            true,
                                            DevicesKV.decodeString("_debug_account_prefix_mask_true").orEmpty(),
                                            DevicesKV.decodeString("_debug_account_suffix_mask_true").orEmpty(),
                                        ),
                                    )
                                }
                            },
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 关闭按钮
                Button(
                    onClick = onDismiss,
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    Text("关闭")
                }
            }
        }
    }

    // 环境切换对话框
    if (showEnvironmentDialog) {
        DebugEnvironmentSwitchDialog(
            onDismiss = {
                showEnvironmentDialog = false
            },
        )
    }

    // Git 提交记录对话框
    if (showGitCommitsDialog) {
        DebugGitCommitsDialog(
            onDismiss = { showGitCommitsDialog = false },
        )
    }

    if (showAccountManagerDialog) {
        DebugAccountMangerDialog(
            onDismiss = {
                showAccountManagerDialog = false
                if (it) {
                    onDismiss()
                }
            },
        )
    }
}

/**
 * 信息行组件
 */
@Composable
private fun InfoRow(
    label: String,
    value: String,
) {
    val clipboardManager = LocalClipboard.current
    val scope = rememberCoroutineScope()

    Row(
        modifier =
            Modifier
                .fillMaxWidth()
                .clickable {
                    scope.launch {
                        clipboardManager.setClipEntry(
                            ClipData.newPlainText("plain text", value).toClipEntry(),
                        )
                        showToast("已复制")
                    }
                }.padding(vertical = 2.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
        )
        SizeWidth(5.dp)
        Text(
            text = value,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.End,
        )
    }
}

/**
 * 调试菜单项组件
 */
@Composable
private fun DebugMenuItem(
    title: String,
    description: String,
    enabled: Boolean = true,
    onClick: () -> Unit,
) {
    Card(
        modifier =
            Modifier
                .fillMaxWidth(),
        colors =
            CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface,
            ),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .clickable(enabled = enabled, onClick = onClick)
                    .padding(5.dp),
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color =
                    if (enabled) {
                        MaterialTheme.colorScheme.onSurface
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                    },
            )
            Spacer(modifier = Modifier.height(2.dp))
            Text(
                text = description,
                style = MaterialTheme.typography.labelLarge,
                color =
                    if (enabled) {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                    },
            )
        }
    }
}

data class DebugLogin(
    val account: String,
)

data class DebugRegister(
    val isMale: Boolean,
    val prefix: String,
    val suffix: String,
)
