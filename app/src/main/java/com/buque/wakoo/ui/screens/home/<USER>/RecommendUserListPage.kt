package com.buque.wakoo.ui.screens.home.child

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.bean.AreaRecommendUser
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.widget.CommonBanner
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.RecommendUserViewModel

@Composable
fun RecommendUserListPage(
    modifier: Modifier = Modifier,
    areaIndex: MutableState<Int> = mutableStateOf(0),
    toNavigate: (AppNavKey) -> Unit = {},
) {
    val viewModel = viewModel<RecommendUserViewModel>()
    val listState = rememberLazyListState()

    var cacheAreaIndex by rememberSaveable {
        mutableIntStateOf(0)
    }

    LaunchedEffect(areaIndex.value) {
        if (cacheAreaIndex != areaIndex.value) {
            val ret = viewModel.toggleRegionType(areaIndex.value)
            if (ret.success) {
                cacheAreaIndex = areaIndex.value
            } else {
                areaIndex.value = cacheAreaIndex
            }
        }
    }
    Column {
        CStateListPaginateLayout<Any, Int, AreaRecommendUser, RecommendUserViewModel>(
            reqKey = "",
            modifier = modifier,
            listState = listState,
            viewModel = viewModel,
            emptyText = "暂无推荐".localized,
            emptyId = R.drawable.ic_empty_for_all,
            wrapperBox = { content ->
                Column(modifier = Modifier.fillMaxSize()) {
                    CommonBanner(
                        202,
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp),
                    )
                    content()
                }
            },
        ) { paginateState, list ->
            val isJP = LocalSelfUserProvider.isJP
            LazyColumn(
                modifier =
                    Modifier
                        .fillMaxSize(),
                state = listState,
                verticalArrangement = Arrangement.spacedBy(16.dp),
                contentPadding = PaddingValues(16.dp),
            ) {
                items(list) { item ->
                    val user = item.user
                    Column(
                        modifier =
                            Modifier
                                .background(
                                    color = Color.White,
                                    shape = RoundedCornerShape(topStart = 24.dp, topEnd = 12.dp, bottomStart = 12.dp, bottomEnd = 12.dp),
                                ).click {
                                    toNavigate(Route.UserProfile(user))
                                }.padding(12.dp),
                    ) {
                        Row {
                            AvatarNetworkImage(
                                user = user,
                                size = 60.dp,
                            )
                            SizeWidth(12.dp)
                            Column(
                                verticalArrangement = Arrangement.Center,
                                modifier = Modifier.heightIn(min = 60.dp),
                            ) {
                                Row(verticalAlignment = Alignment.CenterVertically) {
                                    Row(
                                    modifier = Modifier.weight(1f),
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {Text(
                                        text = user.name,
                                        style = MaterialTheme.typography.bodyLarge,
                                        color = Color(0xFF111111),
                                        fontWeight = FontWeight.Medium,
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis,modifier = Modifier.weight(1f, false),
                                    )
                                    SizeWidth(1.dp)
                                    GenderAgeTag(user = user)
                                    }
                                    Text(
                                        buildAnnotatedString {
                                            withStyle(
                                                SpanStyle(
                                                    fontFamily = FontFamily.MI_SANS,
                                                    fontSize = 12.sp,
                                                    fontWeight = FontWeight.W900,
                                                    color = Color(0xff111111),
                                                ),
                                            ) {
                                                append(item.followerCnt.toString())
                                            }
                                            append("粉丝".localized)
                                        },
                                        fontSize = 11.sp,
                                        color = Color(0xff666666),
                                        textAlign = TextAlign.Center,
                                        modifier =
                                            Modifier
                                                .background(
                                                    brush = Brush.horizontalGradient(listOf(Color(0xffEDFFD5), Color(0xffD6FFEC))),
                                                    shape = RoundedCornerShape(topStart = 12.dp, bottomEnd = 12.dp),
                                                ).padding(horizontal = 5.dp, vertical = 3.dp),
                                    )
                                }

                                val tags =
                                    remember(item.regionLabel, user.height, item.constellation) {
                                        buildList {
                                            add(if (user.height > 0) "${user.height}cm" else "")
                                            add(item.constellation)
                                            if (isJP) {
                                                add(item.regionLabel)
                                            }
                                        }.filter { it.isNotEmpty() }
                                            .joinToString(separator = " | ")
                                    }

                                SizeHeight(10.dp)
                                Text(tags, fontSize = 12.sp, color = Color(0xff999999))
                            }
                        }

                        if (item.albums.isNotEmpty()) {
                            SizeHeight(10.dp)
                            Row(
                                modifier =
                                    Modifier
                                        .fillMaxWidth()
                                        .background(color = Color.White, shape = RoundedCornerShape(12.dp)),
                                horizontalArrangement = Arrangement.spacedBy(8.dp),
                            ) {
                                SizeWidth(64.dp)
                                item.albums.take(4).forEach {
                                    NetworkImage(
                                        it.mediaUrl,
                                        modifier =
                                            Modifier
                                                .size(59.dp)
                                                .clip(RoundedCornerShape(8.dp)),
                                        contentScale = ContentScale.Crop,
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
