package com.buque.wakoo.ui.screens.liveroom.screen

import android.Manifest
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.icons.Close
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.utils.datapoints.Analytics
import kotlinx.coroutines.delay
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GuideInfo(
    val desc: String = "", // 她是官方匹配的专属...
    @SerialName("guide_audioroom_id")
    val guideAudioroomId: Int = 0, // 13
    @SerialName("guide_private_room_id")
    val guidePrivateRoomId: Int = 0, // 12
    @SerialName("guide_user_extra")
    val guideUserExtra: GuideUserExtra = GuideUserExtra(),
    val title: String = "", // 欢迎来到UCOO
    @SerialName("guide_user")
    val user: BasicUser,
)

@Serializable
data class GuideUserExtra(
    @SerialName("constellation_label")
    val constellationLabel: String = "", // 金牛座
)

@Composable
fun InviteToPrivateRoomScreen(
    info: GuideInfo,
    firstEnter: Boolean,
) {
    val rootNavController = LocalAppNavController.root
    BackHandler(firstEnter) {
    }

    val context = LocalContext.current
    Column(
        modifier =
            Modifier
                .fillMaxSize()
                .paint(painterResource(id = R.drawable.bg_welcome_new_user), contentScale = ContentScale.Crop),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        if (firstEnter) {
            Spacer(modifier = Modifier.height(62.dp))
        } else {
            Image(
                imageVector = WakooIcons.Close,
                contentDescription = "close",
                modifier =
                    Modifier
                        .statusBarsPadding()
                        .padding(end = 10.dp)
                        .padding(8.dp)
                        .clickable(onClick = {
                            rootNavController.popIs<Route.InviteToPrivateRoom>()
                        })
                        .size(24.dp)
                        .align(Alignment.End),
                colorFilter = ColorFilter.tint(WakooGrayText),
            )
        }

        Image(
            painter = painterResource(id = R.drawable.ic_welcome_new_user_title),
            contentDescription = null,
            modifier =
                Modifier
                    .height(32.dp),
            contentScale = ContentScale.FillHeight,
        )

        Box(
            modifier =
                Modifier
                    .padding(24.dp)
                    .fillMaxWidth()
                    .aspectRatio(327f / 458f)
                    .clip(RoundedCornerShape(16.dp)),
        ) {
            NetworkImage(
                data = info.user.avatar,
                modifier = Modifier.fillMaxSize(),
            )

            Column(
                modifier =
                    Modifier
                        .align(Alignment.BottomStart)
                        .fillMaxWidth()
                        .background(
                            Brush.verticalGradient(
                                listOf(
                                    Color(0x00000000),
                                    Color(0x80000000),
                                ),
                            ),
                        )
                        .padding(start = 16.dp, top = 25.dp, bottom = 16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                Text(
                    text = info.user.name,
                    color = Color.White,
                    fontSize = 18.sp,
                )

                val userDesc =
                    remember(info, context) {
                        buildString {
                            append(info.user.age)
                            append("岁".localized)

                            if (info.user.height > 0) {
                                append("丨")
                                append(info.user.formatHeight)
                            }

                            if (info.guideUserExtra.constellationLabel.isNotEmpty()) {
                                append("丨")
                                append(info.guideUserExtra.constellationLabel)
                            }
                        }
                    }

                Text(
                    text = userDesc,
                    color = Color.White,
                    fontSize = 14.sp,
                )
            }
        }

        val launcher =
            rememberLauncherForActivityResult(ActivityResultContracts.RequestPermission()) { result ->
                // 关闭
                rootNavController.popIs<Route.InviteToPrivateRoom>()
                if (!result) {
                    if (firstEnter) {
                        // 拒绝权限返回语音房
                        LiveRoomManager.joinRoom(roomId = info.guideAudioroomId.toString(), isPrivateRoom = false)
                    }
                    Analytics.trace(
                        Analytics.Event.NEWBIE_PR_GUIDE_MIC_REFUSE,
                        mapOf("private_room_id" to info.guidePrivateRoomId.toString()),
                    )
                } else {
                    // 进入私密小屋
                    LiveRoomManager.joinRoom(roomId = info.guidePrivateRoomId.toString(), isPrivateRoom = true, autoUpMic = true)
                    Analytics.trace(
                        Analytics.Event.NEWBIE_PR_GUIDE_MIC_GRANT,
                        mapOf("private_room_id" to info.guidePrivateRoomId.toString()),
                    )
                }
            }

        var downCounter by remember {
            mutableIntStateOf(0)
        }

        var launched by remember {
            mutableStateOf(false)
        }

        LaunchedEffect(Unit) {
            downCounter = 10
            while (downCounter > 0) {
                delay(1000)
                downCounter--
            }
            if (!launched) {
                if (firstEnter) {
                    launched = true
                    launcher.launch(Manifest.permission.RECORD_AUDIO)
                } else {
                    // 关闭
                    rootNavController.popIs<Route.InviteToPrivateRoom>()
                }
            }
        }

        Text(
            text = info.desc,
            modifier = Modifier.padding(top = 12.dp, start = 24.dp, end = 24.dp),
            fontSize = 14.sp,
            color = WakooGrayText,
            textAlign = TextAlign.Center,
        )

        Box(
            modifier =
                Modifier
                    .padding(top = 16.dp, start = 20.dp, end = 20.dp)
                    .fillMaxWidth()
                    .height(48.dp)
                    .clip(CircleShape)
                    .background(Color(0xFFFE669E), CircleShape)
                    .clickable(enabled = downCounter > 0) {
                        Analytics.trace(Analytics.Event.NEWBIE_PR_POPUP_ACCEPT, mapOf("private_room_id" to info.guideAudioroomId))
                        launched = true
                        launcher.launch(Manifest.permission.RECORD_AUDIO)
                    },
            contentAlignment = Alignment.Center,
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = "和TA聊聊".localized,
                    color = Color.White,
                    fontSize = 16.sp,
                    lineHeight = 16.sp,
                )
                Text(
                    text = "(${downCounter}s)",
                    color = Color.White,
                    fontSize = 16.sp,
                    lineHeight = 16.sp,
                )
            }
        }
    }
}

@Preview
@Composable
fun PreviewNewcomerReceptionGuideScreen() {
    WakooTheme {
        InviteToPrivateRoomScreen(GuideInfo(user = BasicUser.sampleGirl, desc = "她正在找人语音连麦聊天..."), false)
    }
}
