package com.buque.wakoo.ui.widget.media.selector

import android.Manifest
import android.content.Intent
import android.os.Build
import android.provider.Settings
import androidx.activity.compose.LocalActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.app.ActivityCompat
import androidx.core.net.toUri
import androidx.lifecycle.compose.LifecycleStartEffect
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.BuildConfig
import com.buque.wakoo.app.Const
import com.buque.wakoo.app.DevicesKV
import com.buque.wakoo.ext.formatMillisDuration
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.icons.ImagePlaceholder
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.theme.WakooLightGrayBg
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.ButtonStyles
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.WakooTitleBar
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.media.data.common.ImageItem
import com.buque.wakoo.ui.widget.media.data.common.MediaItem
import com.buque.wakoo.ui.widget.media.data.common.PlaceholderMediaItem
import com.buque.wakoo.ui.widget.media.data.common.SelectorMediaType
import com.buque.wakoo.ui.widget.media.data.common.VideoItem
import com.buque.wakoo.ui.widget.media.previewer.TransitionOverlay
import com.buque.wakoo.ui.widget.media.previewer.rememberPreviewState
import com.buque.wakoo.ui.widget.media.previewer.showMediaItemPreviewer
import com.buque.wakoo.ui.widget.state.StateComponent
import com.buque.wakoo.utils.PermissionUtils
import com.buque.wakoo.utils.upload.rememberMediaSelectLauncher
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch
import me.saket.telephoto.zoomable.rememberZoomablePeekOverlayState
import me.saket.telephoto.zoomable.zoomablePeekOverlay

// 根据 Android 版本确定需要请求的权限列表
private fun getMediaPermissions(mediaType: SelectorMediaType) =
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
        // Android 14 (API 34) 及以上
        when (mediaType) {
            SelectorMediaType.IMAGE -> {
                arrayOf(
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED,
                )
            }

            SelectorMediaType.VIDEO -> {
                arrayOf(
                    Manifest.permission.READ_MEDIA_VIDEO,
                    Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED,
                )
            }

            SelectorMediaType.ALL -> {
                arrayOf(
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.READ_MEDIA_VIDEO,
                    Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED,
                )
            }
        }
    } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        // Android 13 (API 33) 及以上
        when (mediaType) {
            SelectorMediaType.IMAGE -> {
                arrayOf(
                    Manifest.permission.READ_MEDIA_IMAGES,
                )
            }

            SelectorMediaType.VIDEO -> {
                arrayOf(
                    Manifest.permission.READ_MEDIA_VIDEO,
                )
            }

            SelectorMediaType.ALL -> {
                arrayOf(
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.READ_MEDIA_VIDEO,
                )
            }
        }
    } else {
        // Android 12 (API 32) 及以下
        arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
    }

data class MediaSelectorResult(
    val fromSystemSelector: Boolean,
    val list: List<MediaItem>,
)

@Composable
fun MediaSelectorScreen(
    resultKey: String,
    mediaType: SelectorMediaType,
    maxSelectCount: Int,
    modifier: Modifier = Modifier,
    selectedItem: List<MediaItem>? = null,
) {
    val controller = LocalAppNavController.root
    MediaSelectorPage(
        mediaType = mediaType,
        maxSelectCount = maxSelectCount,
        modifier = modifier,
        selectedItem = selectedItem,
    ) { fromSystem, list ->
        controller.setResult(resultKey, MediaSelectorResult(fromSystem, list))
        controller.popIs<Route.MediaSelector>()
    }
}

@Composable
fun MediaSelectorPage(
    mediaType: SelectorMediaType,
    maxSelectCount: Int,
    modifier: Modifier = Modifier,
    selectedItem: List<MediaItem>? = null,
    onSelected: (Boolean, List<MediaItem>) -> Unit,
) {
    val context = LocalContext.current
    val activity = LocalActivity.current
    val permissions =
        remember(mediaType) {
            getMediaPermissions(mediaType)
        }

    var permissionFlag by
        remember(context) {
            // 0: 无权限
            // 1: 部分访问权限
            // 2: 全量访问权限
            mutableIntStateOf(
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                    if (PermissionUtils.hasPermissions(
                            context,
                            arrayOf(
                                Manifest.permission.READ_MEDIA_IMAGES,
                                Manifest.permission.READ_MEDIA_VIDEO,
                            ),
                        )
                    ) {
                        2
                    } else if (PermissionUtils.hasPermission(context, Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED)) {
                        1
                    } else {
                        0
                    }
                } else if (PermissionUtils.hasPermissions(context, permissions)) {
                    2
                } else {
                    0
                },
            )
        }

    var shouldManualRequestPrompt by remember(activity) {
        mutableStateOf(
            if (activity == null || permissionFlag > 0) {
                false
            } else {
                DevicesKV.getBoolean(
                    Const.KVKey.HAS_REQUESTED_MEDIA_PERMISSION,
                    false,
                ) &&
                    !ActivityCompat.shouldShowRequestPermissionRationale(
                        activity,
                        if (Build.VERSION.SDK_INT >=
                            Build.VERSION_CODES.TIRAMISU
                        ) {
                            Manifest.permission.READ_MEDIA_IMAGES
                        } else {
                            Manifest.permission.READ_EXTERNAL_STORAGE
                        },
                    )
            },
        )
    }

    var firstStart by remember {
        mutableStateOf(true)
    }

    val viewModel = viewModel<MediaListViewModel>(factory = MediaListViewModel.Factory(mediaType, maxSelectCount, selectedItem))

    LifecycleStartEffect(context, activity, viewModel) {
        if (firstStart) {
            firstStart = false
        } else {
            val oldPermissionFlag = permissionFlag
            permissionFlag =
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                    if (PermissionUtils.hasPermissions(
                            context,
                            arrayOf(
                                Manifest.permission.READ_MEDIA_IMAGES,
                                Manifest.permission.READ_MEDIA_VIDEO,
                            ),
                        )
                    ) {
                        2
                    } else if (PermissionUtils.hasPermission(context, Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED)) {
                        1
                    } else {
                        0
                    }
                } else if (PermissionUtils.hasPermissions(context, permissions)) {
                    2
                } else {
                    0
                }

            if (permissionFlag > 0 && oldPermissionFlag > 0) {
                viewModel.refresh()
            }

            shouldManualRequestPrompt =
                if (activity == null || permissionFlag > 0) {
                    false
                } else {
                    DevicesKV.getBoolean(
                        Const.KVKey.HAS_REQUESTED_MEDIA_PERMISSION,
                        false,
                    ) &&
                        !ActivityCompat.shouldShowRequestPermissionRationale(
                            activity,
                            if (Build.VERSION.SDK_INT >=
                                Build.VERSION_CODES.TIRAMISU
                            ) {
                                Manifest.permission.READ_MEDIA_IMAGES
                            } else {
                                Manifest.permission.READ_EXTERNAL_STORAGE
                            },
                        )
                }
        }
        onStopOrDispose {}
    }

    val launcher =
        rememberLauncherForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { result ->
            val oldPermissionFlag = permissionFlag
            permissionFlag =
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                    if (result.values.all { it }) {
                        2
                    } else if (result.values.any { it }) {
                        1
                    } else {
                        0
                    }
                } else {
                    if (result.values.all { it }) 2 else 0
                }
            if (permissionFlag <= 0) {
                showToast("您拒绝了相册权限，您可以使用系统选择器继续选择，也可以去设置中授予App相册权限".localized)

                shouldManualRequestPrompt =
                    if (activity == null) {
                        false
                    } else {
                        !ActivityCompat.shouldShowRequestPermissionRationale(
                            activity,
                            if (Build.VERSION.SDK_INT >=
                                Build.VERSION_CODES.TIRAMISU
                            ) {
                                Manifest.permission.READ_MEDIA_IMAGES
                            } else {
                                Manifest.permission.READ_EXTERNAL_STORAGE
                            },
                        )
                    }
            } else if (oldPermissionFlag > 0) {
                viewModel.refresh()
            }
        }

    val settingsLauncher =
        rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        }

    var useOriginMedia by rememberSaveable { mutableStateOf(true) }

    val scope = rememberCoroutineScope()

    val previewState = rememberPreviewState()

    // 转场覆盖层，负责在转场期间显示动画元素和全屏查看器。
    TransitionOverlay(state = previewState)

    Scaffold(
        modifier =
            Modifier
                .fillMaxWidth()
                .then(modifier),
        topBar = {
            WakooTitleBar(
                title = {
                    Text(
                        text =
                            when (mediaType) {
                                SelectorMediaType.IMAGE -> "所有图片".localized
                                SelectorMediaType.VIDEO -> "所有视频".localized
                                SelectorMediaType.ALL -> "图片和视频".localized
                            },
                        style = MaterialTheme.typography.titleMedium,
                    )
                },
                actions = {
                    WakooTitleBarDefaults.TextButtonAction(text = "设置".localized, onClick = {
                        val packageUri = "package:${BuildConfig.APPLICATION_ID}".toUri()
                        settingsLauncher.launch(Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, packageUri))
                    })
                },
            )
        },
        bottomBar = {
            BottomAppBar(modifier = Modifier.height(56.dp)) {
                WakooTitleBarDefaults.TextButtonAction(
                    text = "${"预览".localized}${if (viewModel.selectedIds.isNotEmpty()) "(${viewModel.selectedIds.size})" else ""}",
                    modifier = Modifier.widthIn(min = 70.dp),
                    enabled = viewModel.selectedIds.isNotEmpty(),
                    onClick = {
                        viewModel.selectedItems.takeIf { it.isNotEmpty() }?.also { list ->
                            previewState.showMediaItemPreviewer(list, list.first())
                        }
                    },
                )
                Weight(1f)

//                Row(verticalAlignment = Alignment.CenterVertically) {
//                    RadioButton(
//                        selected = useOriginMedia,
//                        modifier = Modifier.requiredSize(8.dp),
//                        enabled = hasPermission,
//                        onClick = {
//                            useOriginMedia = !useOriginMedia
//                        },
//                        colors =
//                            RadioButtonDefaults.colors(
//                                selectedColor = Color(0xFF66FE6B),
//                                unselectedColor = Color(0x80111111),
//                            ),
//                    )
//
//                    SizeWidth(8.dp)
//
//                    Text(
//                        text = "原图".localized,
//                        style = MaterialTheme.typography.bodyMedium,
//                        color = Color(0xFF111111),
//                    )
//                }
                Weight(1f)
                SolidButton(
                    text = "${"完成".localized}${if (viewModel.selectedIds.isNotEmpty()) "(${viewModel.selectedIds.size})" else ""}",
                    onClick = {
                        onSelected(false, viewModel.selectedItems)
                    },
                    modifier = Modifier.padding(end = 10.dp),
                    height = 30.dp,
                    fontSize = 14.sp,
                    enabled = viewModel.selectedIds.isNotEmpty(),
                    paddingValues = PaddingValues(horizontal = 8.dp),
                    config = ButtonStyles.Solid.copy(shape = RoundedCornerShape(8.dp), minWidth = 60.dp),
                )
            }
        },
    ) { padding ->
        Box(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(padding),
            contentAlignment = Alignment.Center,
        ) {
            if (permissionFlag > 0) {
                val mediaFeed by viewModel.mediaFeedFlow.collectAsStateWithLifecycle()

                val state: LazyGridState = rememberLazyGridState()

                if (!mediaFeed.isLoading && !mediaFeed.mediaFilling && mediaFeed.anchorIdIndex > -1) {
                    LaunchedEffect(Unit) {
                        snapshotFlow {
                            if (state.layoutInfo.visibleItemsInfo.isNotEmpty()) {
                                // 获取当前可见的最后一个项的索引
                                val lastVisibleItemIndex =
                                    state.layoutInfo.visibleItemsInfo
                                        .last()
                                        .index
                                if (lastVisibleItemIndex + 50 > mediaFeed.anchorIdIndex) {
                                    lastVisibleItemIndex
                                } else {
                                    -1
                                }
                            } else {
                                -1
                            }
                        }.filter { it > -1 }.collectLatest {
                            viewModel.loadMoreMediaInfo()
                        }
                    }
                }

                Column {
                    if (permissionFlag == 1) {
                        Row(
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .height(48.dp)
                                    .background(WakooLightGrayBg),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            Text(
                                text = "你已设置Wakoo只能访问相册部分照片".localized,
                                modifier =
                                    Modifier
                                        .padding(start = 10.dp, end = 15.dp)
                                        .weight(1f),
                                color = WakooGrayText,
                                style =
                                    MaterialTheme.typography.bodySmall.copy(
                                        lineHeight = 15.sp,
                                    ),
                                overflow = TextOverflow.Ellipsis,
                                maxLines = 2,
                            )

                            TextButton(onClick = {
                                launcher.launch(permissions)
                            }) {
                                Text(text = "管理".localized, color = WakooText, style = MaterialTheme.typography.bodySmall)
                            }
                        }
                    }

                    Box(contentAlignment = Alignment.Center) {
                        LazyVerticalGrid(
                            columns = GridCells.Fixed(4),
                            state = state,
                            modifier =
                                Modifier
                                    .fillMaxSize()
                                    .onGloballyPositioned {
                                        previewState.overlayClipRect = it.boundsInRoot()
                                    },
                            verticalArrangement = Arrangement.spacedBy(1.dp), // 行之间的间距
                            horizontalArrangement = Arrangement.spacedBy(1.dp), // 列之间的间距
                        ) {
                            items(mediaFeed.mediaItems, key = { it.id }) { item ->

                                val selectedNum by remember {
                                    derivedStateOf {
                                        viewModel.selectedIds
                                            .indexOfFirst { it == item.id }
                                            .takeIf { it > -1 }
                                            ?.plus(1)
                                            ?.toString()
                                    }
                                }

                                val maskColor by animateColorAsState(
                                    if (selectedNum == null) {
                                        Color.Black.copy(0.2f)
                                    } else {
                                        Color.Black.copy(0.5f)
                                    },
                                )
                                Box(
                                    modifier =
                                        Modifier
                                            .fillMaxWidth()
                                            .aspectRatio(1f),
                                ) {
                                    Box(
                                        modifier =
                                            Modifier.drawWithContent {
                                                drawContent()
                                                drawRect(maskColor)
                                            },
                                    ) {
                                        when (item) {
                                            is PlaceholderMediaItem -> {
                                                Image(
                                                    imageVector = WakooIcons.ImagePlaceholder,
                                                    contentDescription = null,
                                                    modifier = Modifier.fillMaxSize(),
                                                )
                                            }

                                            is ImageItem -> {
                                                NetworkImage(
                                                    data = item.uriString,
                                                    modifier =
                                                        with(previewState) {
                                                            Modifier
                                                                .fillMaxSize()
                                                                .noEffectClick {
                                                                    previewState.showMediaItemPreviewer(
                                                                        mediaFeed.mediaItems,
                                                                        item,
                                                                    )
                                                                }.registerGridItem("${item.albumId}-${item.id}")
                                                                .zoomablePeekOverlay(rememberZoomablePeekOverlayState())
                                                        },
                                                    memoryCacheKey = item.uriString,
                                                )
                                            }

                                            is VideoItem -> {
                                                NetworkImage(
                                                    data = item.uriString,
                                                    modifier =
                                                        with(previewState) {
                                                            Modifier
                                                                .fillMaxSize()
                                                                .noEffectClick {
                                                                    previewState.showMediaItemPreviewer(
                                                                        mediaFeed.mediaItems,
                                                                        item,
                                                                    )
                                                                }.registerGridItem("${item.albumId}-${item.id}")
                                                                .zoomablePeekOverlay(rememberZoomablePeekOverlayState())
                                                        },
                                                    memoryCacheKey = item.uriString,
                                                )
                                            }
                                        }
                                    }

                                    Box(
                                        modifier =
                                            Modifier
                                                .align(Alignment.TopEnd)
                                                .noEffectClick(enabled = item is MediaItem) {
                                                    viewModel.changeSelected(item as MediaItem)
                                                }.padding(start = 8.dp, top = 4.dp, end = 4.dp, bottom = 8.dp)
                                                .size(20.dp)
                                                .clip(CircleShape)
                                                .run {
                                                    if (selectedNum != null) {
                                                        background(WakooGreen)
                                                    } else {
                                                        border(1.5.dp, WakooWhite, CircleShape)
                                                    }
                                                },
                                        contentAlignment = Alignment.Center,
                                    ) {
                                        if (selectedNum != null) {
                                            Text(
                                                text = selectedNum!!,
                                                color = Color(0xFF111111),
                                                fontSize = 12.sp,
                                            )
                                        }
                                    }

                                    if (item is VideoItem) {
                                        Box(
                                            modifier =
                                                Modifier
                                                    .align(Alignment.BottomCenter)
                                                    .fillMaxWidth()
                                                    .height(24.dp)
                                                    .background(
                                                        Brush.verticalGradient(
                                                            listOf(
                                                                Color(0x11111111),
                                                                Color(0x99111111),
                                                            ),
                                                        ),
                                                    ),
                                        ) {
                                            Text(
                                                text = item.durationMillis.formatMillisDuration,
                                                modifier =
                                                    Modifier
                                                        .align(Alignment.CenterStart)
                                                        .padding(start = 4.dp),
                                                fontSize = 11.sp,
                                                color = WakooWhite,
                                            )
                                        }
                                    }
                                }
                            }
                        }

                        if (mediaFeed.mediaItems.isEmpty()) {
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                val launcher =
                                    rememberMediaSelectLauncher(mediaType, maxSelectCount) { list ->
                                        if (list.isNotEmpty()) {
                                            scope.launch {
                                                try {
                                                    onSelected(true, viewModel.loadMediaItems(context, list))
                                                } catch (e: Exception) {
                                                }
                                            }
                                        }
                                    }

                                if (permissionFlag == 2) {
                                    StateComponent.Empty(text = "相册为空".localized)
                                } else {
                                    StateComponent.Empty(text = "还没选择Wakoo能访问的照片，可以点击管理去添加照片".localized)
                                }

                                SizeHeight(10.dp)

                                OutlinedButton(
                                    text = "使用系统选择器".localized,
                                    onClick = {
                                        launcher.launch()
                                    },
                                )
                            }
                        }
                    }
                }
            } else {
                Column(
                    modifier = Modifier.padding(horizontal = 15.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    StateComponent.Error(
                        text =
                            if (shouldManualRequestPrompt) {
                                "您拒绝了相册权限，您可以使用系统选择器继续选择，也可以去设置中授予App相册权限".localized
                            } else {
                                "需要授予相册访问权限，才能继续使用相册功能".localized
                            },
                        buttonText =
                            if (shouldManualRequestPrompt) {
                                "设置".localized
                            } else {
                                "授权".localized
                            },
                        onClick = {
                            if (shouldManualRequestPrompt) { // 去设置中授权
                                val packageUri = "package:${BuildConfig.APPLICATION_ID}".toUri()
                                settingsLauncher.launch(Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, packageUri))
                            } else {
                                DevicesKV.putBoolean(
                                    Const.KVKey.HAS_REQUESTED_MEDIA_PERMISSION,
                                    true,
                                )
                                launcher.launch(permissions)
                            }
                        },
                    )

                    SizeHeight(10.dp)

                    val launcher =
                        rememberMediaSelectLauncher(mediaType, maxSelectCount) { list ->
                            if (list.isNotEmpty()) {
                                scope.launch {
                                    try {
                                        onSelected(true, viewModel.loadMediaItems(context, list))
                                    } catch (e: Exception) {
                                    }
                                }
                            }
                        }

                    OutlinedButton(
                        text = "使用系统选择器".localized,
                        onClick = {
                            launcher.launch()
                        },
                    )
                }
            }
        }
    }
}
