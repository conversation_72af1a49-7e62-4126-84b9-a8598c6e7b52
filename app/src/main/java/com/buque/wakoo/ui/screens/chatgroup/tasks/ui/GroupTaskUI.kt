package com.buque.wakoo.ui.screens.chatgroup.tasks.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.OnClick
import com.buque.wakoo.bean.RichItem
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.parseValue
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.navigation.ChatGroupRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.icons.IcAward
import com.buque.wakoo.ui.icons.IcMission
import com.buque.wakoo.ui.icons.IcMissionProgress
import com.buque.wakoo.ui.icons.QuestionLine
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.chatgroup.tasks.models.ActiveTaskInfo
import com.buque.wakoo.ui.screens.chatgroup.tasks.models.ActiveTaskInfo.Task
import com.buque.wakoo.ui.screens.chatgroup.tasks.models.Bonus
import com.buque.wakoo.ui.screens.chatgroup.tasks.models.BonusLevel
import com.buque.wakoo.ui.screens.chatgroup.tasks.models.BonusWrapper
import com.buque.wakoo.ui.screens.chatgroup.tasks.models.CPTaskCardInfo
import com.buque.wakoo.ui.screens.chatgroup.tasks.models.GroupTaskApi
import com.buque.wakoo.ui.screens.chatgroup.tasks.models.TribeBoxInfo
import com.buque.wakoo.ui.screens.chatgroup.tasks.models.TribeBoxInfo.BonusBoxReward
import com.buque.wakoo.ui.screens.chatgroup.tasks.models.TribeCPTaskInfo
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooSecondaryText
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.theme.WakooUI
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.richtext.EntryRichText
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.ui.widget.state.toCState
import com.buque.wakoo.utils.DateTimeUtils
import com.buque.wakoo.utils.eventBus.AppEvent
import com.buque.wakoo.utils.eventBus.EventBus
import com.buque.wakoo.utils.eventBus.tryToLink
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

//<editor-fold desc="群组任务Page">
private val colorCard = Color(0xFFF7F7F7)
private val colorRed = Color(0xFFFF3570)

typealias BoxItem = BonusBoxReward

/**
 * 活跃度宝箱
 */
@Composable
fun ActiveBoxCard(
    activeValue: Int,
    rewards: List<BoxItem>,
    rule: String = "",
) {
    val progress = remember(activeValue, rewards) {
        val max = rewards.maxOfOrNull { it.tribeActivePoint } ?: 0
        if (activeValue >= max) {
            1f
        } else {
            val size = rewards.size
            if (size == 0) {
                0f
            } else {
                var curIndex = 0
                for ((index, bonusBoxReward) in rewards.withIndex()) {
                    if (activeValue < bonusBoxReward.tribeActivePoint) {
                        curIndex = index
                        break
                    }
                }
                if (curIndex == 0) {
                    0.5f * (activeValue * 1f / rewards[0].tribeActivePoint) / size
                } else {
                    val start = rewards[curIndex - 1].tribeActivePoint
                    val end = rewards[curIndex].tribeActivePoint
                    (curIndex - 0.5f + 1f * (activeValue - start) / end) / size
                }
            }
        }
    }
    val context = LocalContext.current
    val nav = LocalAppNavController.root
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(colorCard, WakooUI.Shapes.corner12)
            .padding(16.dp)
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = "群组周活跃度".localized,
                color = WakooText,
                fontSize = 16.sp
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(text = activeValue.toString(), fontSize = 16.sp, color = WakooText)
            Weight()
            Text(
                text = "每周一0点更新".localized,
                fontSize = 12.sp,
                color = WakooGrayText
            )
            Spacer(modifier = Modifier.width(3.dp))
            Icon(
                WakooIcons.QuestionLine,
                contentDescription = "q1",
                tint = WakooGrayText,
                modifier = Modifier
                    .size(16.dp)
                    .click(onClick = {
                        nav.push(Route.Web(rule))
                    })
            )
        }

        Spacer(modifier = Modifier.height(16.dp))
        Box(modifier = Modifier.fillMaxWidth()) {
            LinearProgressIndicator(
                progress = { progress },
                strokeCap = StrokeCap.Round,
                trackColor = Color(0xFFE1E1E1),
                color = Color(0xFFFF3570),
                modifier = Modifier
                    .padding(top = 16.dp)
                    .fillMaxWidth()
                    .height(8.dp)
            )
            Row(
                modifier = Modifier.fillMaxWidth(),
            ) {
                rewards.forEach { item ->
                    Column(
                        modifier = Modifier.weight(1f),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_task_box),
                            contentDescription = "box",
                            modifier = Modifier
                                .size(40.dp)
                                .click {
                                    EventBus.trySend(AppEvent.CustomDialog {
                                        PreviewRewardUI(
                                            list = item.bonusList,
                                            desc = item.bonusRule
                                        ) {
                                            dismiss()
                                        }
                                    })
                                },
                            colorFilter = if (item.isFinished) null else ColorFilter.colorMatrix(ColorMatrix().apply { setToSaturation(0f) })
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = item.tribeActivePoint.toString(),
                            fontSize = 12.sp,
                            color = WakooGrayText,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth()
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = item.taskRequirement.ifEmpty { " " },
                            fontSize = 10.sp,
                            color = WakooGrayText,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth(1f)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun LastWeekRewardBar(enable: Boolean, buttonText: String = "领取".localized, onClick: OnClick = {}) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(colorCard, WakooUI.Shapes.corner12)
            .padding(16.dp, 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_box_task),
            contentDescription = "box",
            modifier = Modifier.size(40.dp),
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(text = "上周群组宝箱奖励".localized, color = WakooText, modifier = Modifier.weight(1f))
        Text(
            text = buttonText,
            style = TextStyle(fontSize = 14.sp, color = Color.White),
            modifier = Modifier
                .click(enabled = true, onClick = onClick)
                .graphicsLayer {
                    alpha = if (enable) 1f else 0.4f
                }
                .background(Color(0xFFFF3570), WakooUI.Shapes.chip)
                .widthIn(min = 72.dp)
                .padding(horizontal = 22.dp, 8.dp)
        )
    }
}

@Preview
@Composable
private fun CardPreview() {
    Column(
        modifier = Modifier
            .background(Color.Black)
            .padding(10.dp)
    ) {
        ActiveBoxCard(
            activeValue = 1000,
            rewards = listOf(
                BoxItem(isFinished = true, tribeActivePoint = 200),
                BoxItem(isFinished = true, tribeActivePoint = 500),
                BoxItem(needMakeCp = true, tribeActivePoint = 500),
                BoxItem(needPublicCp = true, tribeActivePoint = 500),
            ),
        )
        Spacer(modifier = Modifier.height(20.dp))
        LastWeekRewardBar(enable = false)
    }
}

class TribeBoxViewModel : ViewModel() {
    private val _boxInfo: MutableState<CState<TribeBoxInfo>> = mutableStateOf(CState.Loading())
    val boxInfo: State<CState<TribeBoxInfo>> = _boxInfo
    val api = GroupTaskApi.Companion.getInstance()


    fun getBoxInfo() {
        viewModelScope.launch {
            executeApiCallExpectingData { api.tribeBoxInfo() }.also {
                _boxInfo.value = it.toCState()
            }
        }
    }

    suspend fun getBoxReward() = executeApiCallExpectingData {
        api.collectBoxReward()
    }.onSuccess {
        getBoxInfo()
    }
}

/**
 * 群组任务容器组件
 * 这是一个使用Jetpack Compose构建的UI组件，用于展示和管理群组任务相关的内容
 *
 * @param modifier 用于修饰组件的样式参数，可以设置大小、padding等属性
 * @param vm 群组盒视图模型，用于管理组件的状态和业务逻辑，默认通过viewModel()获取
 */
@Composable
fun GroupTaskContainer(
    modifier: Modifier = Modifier,
    vm: TribeBoxViewModel = viewModel(),
    tabIndex: Int = 0,
    onRefreshGroupInfo: OnAction = {},
    onDismiss: OnAction = {}
) {
    // 调用Content组件来实际渲染内容，并将传入的modifier和vm参数传递下去
    Content(modifier, tabIndex = tabIndex, vm = vm, onRefreshGroupInfo = onRefreshGroupInfo, onDismiss = onDismiss)
}


@Composable
private fun Content(
    modifier: Modifier,
    tabIndex: Int = 0,
    vm: TribeBoxViewModel = viewModel(),
    cpTaskViewModel: CPTaskViewModel = viewModel(),
    activeTaskViewModel: ActiveTaskViewModel = viewModel(),
    onRefreshGroupInfo: OnAction = {},
    onDismiss: OnAction = {}
) {
    val scope = rememberCoroutineScope()
    val state by vm.boxInfo
    val scrollState = rememberScrollState()
    LaunchedEffect(key1 = Unit) {
        vm.getBoxInfo()
    }
    var collecting by remember {
        mutableStateOf(false)
    }
    CStateLayout(
        state = state,
        modifier = modifier,
        onRetry = {
            vm.getBoxInfo()
        }
    ) { boxInfo ->
        Column(
            modifier = modifier
                .fillMaxWidth()
                .verticalScroll(scrollState)
                .padding(16.dp)
                .padding(bottom = 120.dp)
        ) {
            ActiveBoxCard(
                activeValue = boxInfo.tribeActivePoint,
                rewards = boxInfo.bonusBoxRewards,
                rule = boxInfo.bonusBoxRule
            )
            Spacer(modifier = Modifier.height(8.dp))
            val enableCollect = boxInfo.collectEnable
            LastWeekRewardBar(
                enable = enableCollect,
                buttonText = if (boxInfo.hasCollect) "查看奖励".localized else "领取".localized
            ) {
                if (!enableCollect) return@LastWeekRewardBar
                if (collecting) return@LastWeekRewardBar
                collecting = true
                scope.launch {
                    vm.getBoxReward().onSuccess { obj ->
                        val bonus = obj.parseValue<List<BonusLevel>>("rewards").orEmpty()
                        val isCollect = obj.parseValue<Boolean>("is_collect_this_time", false)
                        if (bonus.isNotEmpty()) {
                            EventBus.trySend(AppEvent.CustomDialog {
                                val title = if (isCollect) "恭喜获得".localized else "查看奖励".localized
                                val buttonText = if (isCollect) "开心收下".localized else "我知道了".localized
                                RewardDialogUI(list = bonus, title, buttonText) {
                                    dismiss()
                                }
                            })
                        }
                    }
                    collecting = false
                    vm.getBoxInfo()
                    onRefreshGroupInfo()
                }
            }
            Spacer(modifier = Modifier.height(8.dp))
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(colorCard, RoundedCornerShape(12.dp))
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                val pagerState = rememberPagerState(initialPage = tabIndex) {
                    2
                }
                val tabs = listOf(
                    "群组CP任务".localized,
                    "群组活跃任务".localized
                )
                val selectedTabIndex = pagerState.currentPage
                TabRow(
                    selectedTabIndex, modifier = Modifier.width(200.dp),
                    containerColor = Color.Transparent,
                    contentColor = Color.Transparent,
                    divider = {}, indicator = { tabPositions ->
                        if (selectedTabIndex < tabPositions.size) {
                            Box(modifier = Modifier.tabIndicatorOffset(tabPositions[selectedTabIndex])) {
                                Box(
                                    modifier = Modifier
                                        .size(12.dp, 3.dp)
                                        .align(Alignment.Center)
                                        .background(WakooText, WakooUI.Shapes.chip)
                                )
                            }
                        }
                    }) {
                    tabs.forEachIndexed { index, title ->
                        Box(
                            modifier = Modifier
                                .height(28.dp)
                                .weight(1f)
                        ) {
                            val isSelected = index == pagerState.currentPage
                            Text(
                                text = title,
                                color = if (isSelected) WakooText else WakooGrayText,
                                textAlign = TextAlign.Center,
                                fontSize = 14.sp,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .click {
                                        scope.launch {
                                            pagerState.scrollToPage(index)
                                        }
                                    }
                                    .align(Alignment.Center)
                            )
                        }
                    }
                }
                val mod = Modifier
                    .fillMaxWidth()
                    .heightIn(min = 200.dp)
                Spacer(modifier = Modifier.height(16.dp))
                HorizontalPager(
                    state = pagerState,
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = 340.dp),
                    userScrollEnabled = false
                ) { pageIndex ->
                    when (pageIndex) {
                        0 -> {
                            CPTaskPage(mod, vm = cpTaskViewModel)
                        }

                        else -> {
                            ActiveTaskPage(modifier = mod, vm = activeTaskViewModel, onRefreshGroupInfo = onRefreshGroupInfo, onDismiss = onDismiss)
                        }
                    }
                }
            }
        }
    }
}
//</editor-fold>

//<editor-fold desc="群组CP任务">
data class MissionItem(val icon: ImageVector, val title: String, val desc: List<RichItem>)

@Composable
private fun CPTaskCard(
    cpTaskCardInfo: CPTaskCardInfo,
    tribeType: Int,
    onClick: OnClick = {},
    titleText: String = "",
    progressText: String = "",
    awardText: String = "",
    avatarUI: @Composable RowScope.() -> Unit = {}
) {
    Column(
        modifier = Modifier
            .background(Brush.verticalGradient(listOf(Color(0xFFFFE8F0), Color.White, Color.White)), RoundedCornerShape(8.dp))
            .padding(12.dp)
    ) {

        val isMakeCpType = cpTaskCardInfo.taskType == CPTaskCardInfo.Companion.FIRST_MAKE_CP
        val targetDesc = cpTaskCardInfo.taskTargetRichDesc
        val progressDesc = cpTaskCardInfo.taskProcessRichDesc
        val bonusDesc = cpTaskCardInfo.taskBonusRichDesc
        val titleRich = cpTaskCardInfo.title

        val list = remember(titleText, progressText, awardText, targetDesc, progressDesc, bonusDesc) {
            listOf(
                MissionItem(WakooIcons.IcMission, titleText, targetDesc),
                MissionItem(WakooIcons.IcMissionProgress, progressText, progressDesc),
                MissionItem(WakooIcons.IcAward, awardText, bonusDesc),
            )
        }

        if (titleRich.isNotEmpty()) {
            EntryRichText(rich = titleRich, fontSize = 12.sp)
            Spacer(modifier = Modifier.height(6.dp))
        }

        list.forEach { item ->
            SizeHeight(6.dp)
            Row(verticalAlignment = Alignment.CenterVertically) {
                Image(item.icon, modifier = Modifier.size(20.dp), contentDescription = null)
                SizeWidth(4.dp)
                Text(item.title, color = WakooText, fontWeight = FontWeight.Medium)
            }
            SizeHeight(6.dp)
            EntryRichText(rich = item.desc, fontSize = 12.sp)
            SizeHeight(6.dp)
            HorizontalDivider(color = Color(0xFFE5E5E5), thickness = 0.5.dp)
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()

                .padding(vertical = 12.dp)
        ) {
            Text(
                text = if (cpTaskCardInfo.taskType == CPTaskCardInfo.Companion.FIRST_MAKE_CP) {
                    if (tribeType == 2) {
                        "群组内从未组过CP的男成员 (%d人)".localizedFormat(cpTaskCardInfo.count)
                    } else {
                        "群组内从未组过CP的女成员 (%d人)".localizedFormat(cpTaskCardInfo.count)
                    }
                } else "群组内尚未官宣的CP (%d对)".localizedFormat(cpTaskCardInfo.count),
                color = WakooText,
                fontSize = 14.sp
            )
            Spacer(modifier = Modifier.height(12.dp))
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(32.dp)
                    .click(onClick = onClick),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (cpTaskCardInfo.count > 0) {
                    avatarUI()
                } else {
                    Text(
                        text = "暂无成员...".localized,
                        fontSize = 12.sp,
                        color = WakooSecondaryText
                    )
                }
                Image(
                    Icons.Default.KeyboardArrowRight,
                    contentDescription = null,
                    modifier = Modifier
                        .size(20.dp)
                        .graphicsLayer { alpha = 0.5f })
            }
        }
    }
}

@Composable
fun CPTaskPageContent(
    tribeCPTaskInfo: TribeCPTaskInfo,
    modifier: Modifier = Modifier,
    tribeType: Int = 2,
    onClickMakeCP: OnClick = {},
    onClickPublicCP: OnClick = {}
) {
    Column(
        modifier.fillMaxWidth()
    ) {
        val userList = remember(tribeCPTaskInfo.makeCpTask) {
            tribeCPTaskInfo.makeCpTask.users.take(7)
        }
        val title1 = "群组脱单任务".localized
        val title2 = "群组官宣任务".localized

        val progress1 = "脱单任务进度".localized
        val progress2 = "官宣任务进度".localized

        val bonus1 = "脱单任务奖励".localized
        val bonus2 = "官宣任务奖励".localized
        CPTaskCard(
            tribeCPTaskInfo.makeCpTask,
            tribeType, onClick = onClickMakeCP,
            titleText = title1, progressText = progress1, awardText = bonus1
        ) {
            Row(modifier = Modifier.weight(1f), verticalAlignment = Alignment.CenterVertically) {
                userList.forEach { simpleUser ->
                    NetworkImage(
                        simpleUser.avatar, modifier = Modifier
                            .size(32.dp)
                            .clip(
                                CircleShape
                            )
                            .border(0.5.dp, Color.White, CircleShape)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                }
            }
        }
        Spacer(modifier = Modifier.height(12.dp))

        val cpList = remember(tribeCPTaskInfo.publicCpTask) {
            tribeCPTaskInfo.publicCpTask.cpRelations.take(4)
        }
        CPTaskCard(
            tribeCPTaskInfo.publicCpTask,
            tribeType,
            onClick = onClickPublicCP,
            titleText = title2,
            progressText = progress2,
            awardText = bonus2
        ) {
            Row(modifier = Modifier.weight(1f), verticalAlignment = Alignment.CenterVertically) {
                cpList.forEach { cp ->
                    Box(modifier = Modifier.size((32 + 24).dp, 32.dp)) {
                        NetworkImage(
                            cp.user1.avatar, modifier = Modifier
                                .size(32.dp)
                                .clip(
                                    CircleShape
                                )
                                .border(0.5.dp, Color.White, CircleShape)
                        )
                        NetworkImage(
                            cp.user2.avatar, modifier = Modifier
                                .size(32.dp)
                                .align(Alignment.CenterEnd)
                                .clip(
                                    CircleShape
                                )
                                .border(0.5.dp, Color.White, CircleShape)
                        )
                    }
                    Spacer(modifier = Modifier.width(6.dp))
                }
            }
        }
    }
}

class CPTaskViewModel : ViewModel() {
    private val _data: MutableState<CState<TribeCPTaskInfo>> = mutableStateOf(CState.Loading())
    val data: State<CState<TribeCPTaskInfo>> = _data
    private val api = GroupTaskApi.Companion.getInstance()

    init {
        refresh()
    }

    fun refresh() {
        viewModelScope.launch {
            _data.value = executeApiCallExpectingData { api.tribeCPTaskInfo() }.toCState()
        }
    }
}

@Composable
private fun CPTaskPage(modifier: Modifier = Modifier, vm: CPTaskViewModel = viewModel()) {
    val state by vm.data
    val nav = LocalAppNavController.current
    CStateLayout(state = state, modifier = modifier, onRetry = { vm.refresh() }) {
        CPTaskPageContent(
            modifier = Modifier.fillMaxSize(),
            tribeCPTaskInfo = it,
            onClickMakeCP = {
                nav.push(ChatGroupRoute.MemberNoCP)
            },
            onClickPublicCP = {
                nav.push(ChatGroupRoute.MemberWithCPNoPublic)
            })
    }
}

private fun AnnotatedString.Builder.append(text: String, color: Color = Color.White) {
    withStyle(SpanStyle(color = color)) {
        append(text)
    }
}

//</editor-fold>

@Preview
@Composable
private fun BonusPreview() {
    Column(
        modifier = Modifier
            .width(300.dp)
            .background(Color.Black)
            .padding(15.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(20.dp))
        RewardDialogUI(list = buildList {
            add(BonusLevel(1, listOf(Bonus("勋章*8天", "", "名称"), Bonus("勋章*8天", "", "名称"))))
            add(BonusLevel(2, listOf(Bonus("勋章*8天", "", "名称"), Bonus("勋章*8天", "", "名称"))))
            add(BonusLevel(4, listOf(Bonus("勋章*8天", "", "名称"), Bonus("勋章*8天", "", "名称"))))
        })
        Spacer(modifier = Modifier.height(50.dp))
        ActiveReward(title = "恭喜获得", icon = "", desc = "活跃度+10", buttonText = "开心收下")
    }
}
//<editor-fold desc="群组活跃任务">


@Composable
private fun GroupTaskItem(
    title: String,
    icon: String,
    buttonText: String,
    textColor: Color,
    buttonColor: Brush,
    modifier: Modifier = Modifier,
    richText: List<RichItem> = emptyList(),
    enable: Boolean = true,
    onClick: OnClick = {}
) {
    Row(modifier = modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
        Column(modifier = Modifier.weight(1f)) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                NetworkImage(icon, modifier = Modifier.size(20.dp))
                SizeWidth(4.dp)
                Text(text = title, color = WakooText)
            }
            Spacer(modifier = Modifier.height(8.dp))
            EntryRichText(rich = richText, fontSize = 12.sp)
        }
        Text(
            text = buttonText,
            color = if (!enable) WakooGrayText else textColor,
            modifier = Modifier
                .widthIn(min = 72.dp)
                .then(
                    if (enable) Modifier
                        .background(buttonColor, WakooUI.Shapes.chip)
                        .border(1.dp, Color(0xFFFF3570), WakooUI.Shapes.chip)
                    else Modifier.border(1.dp, WakooGrayText, WakooUI.Shapes.chip)
                )
                .click(onClick = onClick, enabled = enable)
                .padding(horizontal = 10.dp),
            lineHeight = 32.sp,
            textAlign = TextAlign.Center
        )
    }
}


typealias ActiveTask = Task

@Composable
private fun ActiveTaskPageContent(
    list: List<Task>,
    modifier: Modifier = Modifier,
    onTaskItemClick: (Task) -> Unit = {}
) {
    Column(
        modifier = modifier
            .background(Brush.verticalGradient(listOf(Color(0xFFFFE8F0), Color.White, Color.White)), RoundedCornerShape(8.dp))
            .padding(horizontal = 12.dp, 4.dp)
    ) {
        val colorRed = Color(0xFFFF3570)
        list.forEachIndexed { index, task ->
            val buttonColor = if (task.taskIsFinished && !task.taskIsCollected) {
                SolidColor(colorRed)
            } else {
                SolidColor(Color.Transparent)
            }
            GroupTaskItem(
                title = task.taskName,
                icon = task.taskIcon,
                buttonText = task.taskBtnLabel,
                textColor = if (task.taskIsFinished && !task.taskIsCollected) Color.White else colorRed,
                buttonColor = buttonColor,
                enable = !task.taskIsCollected,
                modifier = Modifier.padding(vertical = 12.dp),
                richText = task.taskBonusRichDesc
            ) {
                onTaskItemClick(task)
            }
            if (index != list.lastIndex) {
                HorizontalDivider(color = Color(0xFFE5E5E5), thickness = 1.dp)
            }
        }
    }
}

@Preview
@Composable
private fun ActiveTaskPreview() {
    ActiveTaskPageContent(
        list = buildList {
            add(
                ActiveTask(
                    taskType = ActiveTask.SEND_MESSAGE,
                    taskName = "群组发言",
                    taskBtnLabel = "领取"
                )
            )
            add(ActiveTask(taskName = "群组签到", taskBtnLabel = "签到"))
            add(ActiveTask(taskName = "赠送任意U币礼物", taskBtnLabel = "去完成"))
            add(
                ActiveTask(
                    taskName = "在群组中送1次礼物",
                    taskBtnLabel = "已完成",
                    taskIsFinished = true
                )
            )
            add(
                ActiveTask(
                    taskName = "与CP在群组中共同发言",
                    taskBtnLabel = "已完成",
                    taskIsFinished = true
                )
            )
        }, modifier = Modifier
            .background(Color.Black)
            .padding(16.dp)
    )
}

class ActiveTaskViewModel : ViewModel() {
    val api = GroupTaskApi.Companion.getInstance()

    private val _state: MutableState<CState<ActiveTaskInfo>> = mutableStateOf(CState.Loading())
    val state: State<CState<ActiveTaskInfo>> = _state

    fun refresh() {
        viewModelScope.launch {
            _state.value = executeApiCallExpectingData { api.getActiveTask() }.toCState()
        }
    }
}

@Composable
private fun ActiveTaskPage(
    modifier: Modifier = Modifier,
    vm: ActiveTaskViewModel = viewModel(),
    onRefreshGroupInfo: OnAction = {},
    onDismiss: OnAction = {}
) {
    val scope = rememberCoroutineScope()
    val context = LocalContext.current
    val state by vm.state
    LaunchedEffect(key1 = vm) {
        vm.refresh()
    }
    CStateLayout(
        state = state,
        modifier = modifier
            .fillMaxSize()
            .heightIn(min = 400.dp),
        onRetry = {
            vm.refresh()
        }
    ) { taskInfo ->
        val tasks = taskInfo.tasks
        if (tasks.isEmpty()) {

        } else {
            Column(modifier = Modifier.fillMaxSize()) {

                if (taskInfo.leftDuration > 0) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        val style = TextStyle(fontSize = 12.sp, color = Color.Red, fontFamily = FontFamily.Monospace)
                        Text(text = taskInfo.awardHint, style = style)
                        TickDownEffect(
                            leftMillis = taskInfo.leftDuration * 1000L,
                            onComplete = { vm.refresh() }) {
                            Text(
                                text = "倒计时 %s".localizedFormat(DateTimeUtils.formatTimeWithHours(it)), style = style
                            )
                        }
                    }
                }
                var posting by remember {
                    mutableStateOf(false)
                }
                ActiveTaskPageContent(list = tasks, modifier = Modifier.fillMaxSize()) { task ->
                    if (task.taskIsCollected) return@ActiveTaskPageContent
                    if (posting) return@ActiveTaskPageContent
                    if (task.taskIsFinished) {
                        //去领取
                        posting = true
                        scope.launch {
                            executeApiCallExpectingData { vm.api.collectReward(mapOf("mission_id" to task.taskId.toString())) }
                                .onSuccess { tbinfo ->
                                    vm.refresh()
                                    EventBus.trySend(AppEvent.CustomDialog {
                                        ActiveReward(
                                            title = "恭喜获得".localized,
                                            icon = tbinfo.bonusIcon,
                                            desc = tbinfo.bonusName,
                                            buttonText = "开心收下".localized
                                        ) {
                                            dismiss()
                                        }
                                    })
                                }
                            posting = false
                            onRefreshGroupInfo()
                        }
                    } else {
                        //去完成｜签到
                        when (task.taskType) {
                            ActiveTask.CHECK_IN -> {
                                posting = true
                                scope.launch {
                                    executeApiCallExpectingData { vm.api.checkIn() }
                                        .onSuccess {
                                            vm.refresh()
                                            showToast("签到成功".localized)
                                        }
                                    posting = false
                                    onRefreshGroupInfo()
                                }
                            }

                            else -> {
                                posting = true
                                scope.launch {
                                    executeApiCallExpectingData { vm.api.finishTask(task.taskId) }
                                        .onSuccess { obj ->
                                            obj.parseValue<String>("jump_link").orEmpty().takeIf { it.isNotEmpty() }?.also {
                                                onDismiss()
                                                it.tryToLink()
                                            }

                                        }
                                    posting = false
                                    onRefreshGroupInfo()
                                }
                            }
                        }
                    }

                }
            }
        }
    }
}

/**
 * [leftMillis] 剩余的毫秒
 */
@Composable
fun TickDownEffect(
    leftMillis: Long,
    key: Any = Unit,
    onComplete: () -> Unit = {},
    content: @Composable (leftMillis: Long) -> Unit,
) {
    var left by remember {
        mutableLongStateOf(leftMillis)
    }
    LaunchedEffect(key) {
        val rem = left % 1000L
        if (rem != 0L) {
            delay(rem)
            left -= rem
        }
        while (isActive) {
            delay(1000L)
            left -= 1000L
            if (left <= 0) {
                onComplete()
                break
            }
        }
    }
    content(left)
}

@Composable
private fun ActiveReward(
    title: String,
    icon: String,
    desc: String,
    buttonText: String,
    onClick: OnClick = {}
) {
    Column(
        modifier = Modifier
            .size(263.dp, 280.dp)
            .paint(painterResource(R.drawable.bg_group_task_small_dialog), contentScale = ContentScale.FillBounds),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(title, color = colorRed, fontSize = 18.sp, lineHeight = 34.sp, fontWeight = FontWeight.Medium)
        Spacer(modifier = Modifier.height(16.dp))
        NetworkImage(icon, modifier = Modifier.size(120.dp))
        Box(Modifier.weight(1f), contentAlignment = Alignment.Center) {
            Text(text = desc, fontSize = 18.sp, color = colorRed, fontWeight = FontWeight.Medium)
        }
        Box(
            modifier = Modifier
                .size(180.dp, 44.dp)
                .paint(
                    painterResource(id = R.drawable.bg_pink_button),
                    contentScale = ContentScale.Crop
                )
                .click(onClick = onClick),
            contentAlignment = Alignment.Center
        ) {
            Text(text = buttonText, fontSize = 16.sp, color = Color.White)
        }
        Spacer(modifier = Modifier.height(16.dp))
    }
}

//</editor-fold>

@Composable
private fun RewardDialogUI(
    list: List<BonusLevel>,
    title: String = "恭喜获得".localized,
    buttonText: String = "开心收下".localized,
    onClick: OnClick = {}
) {

    val bonusItem: List<BonusWrapper> = remember(list) {
        buildList {
            list.forEach {
                add(BonusWrapper.LevelItem(it.level))
                it.bonusList.forEach { bonus: Bonus ->
                    add(BonusWrapper.BonusItem(bonus))
                }
            }
        }
    }
    Column(
        modifier = Modifier
            .size(309.dp, 486.dp)
            .paint(
                painterResource(R.drawable.bg_task_bg_big),
                contentScale = ContentScale.FillBounds
            ),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(72.dp))
        Text(title, fontSize = 18.sp, color = colorRed, fontWeight = FontWeight.Medium, lineHeight = 34.sp)
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            contentAlignment = Alignment.Center
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(bonusItem) { item ->
                    when (item) {
                        is BonusWrapper.BonusItem -> {
                            val bonus = item.bonus
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(start = 38.dp), verticalAlignment = Alignment.CenterVertically
                            ) {
                                NetworkImage(
                                    bonus.bonusIcon, modifier = Modifier
                                        .size(36.dp)
                                        .background(Color(0x1AFFFFFF), WakooUI.Shapes.small),
                                    contentScale = ContentScale.Inside
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(text = "${bonus.bonusName}${bonus.bonusDesc}", color = colorRed, fontWeight = FontWeight.Medium)
                            }
                        }

                        is BonusWrapper.LevelItem -> {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.Center,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                val c = Color(0xFF666666)
                                HorizontalDivider(modifier = Modifier.width(28.dp), thickness = 1.dp, color = c)
                                Text(
                                    text = "%d级宝箱".localizedFormat(item.level),
                                    color = c,
                                    modifier = Modifier.padding(horizontal = 6.dp)
                                )
                                HorizontalDivider(modifier = Modifier.width(28.dp), thickness = 1.dp, color = c)
                            }
                        }
                    }

                }
            }
        }
        Spacer(modifier = Modifier.height(16.dp))
        Box(
            modifier = Modifier
                .size(180.dp, 44.dp)
                .paint(
                    painterResource(id = R.drawable.bg_pink_button),
                    contentScale = ContentScale.Crop
                )
                .click(onClick = onClick),
            contentAlignment = Alignment.Center
        ) {
            Text(text = buttonText, fontSize = 16.sp, color = Color.White)
        }
        Spacer(modifier = Modifier.height(16.dp))
    }

}

@Composable
fun PreviewRewardUI(list: List<Bonus>, desc: String, onClick: OnClick = {}) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Brush.verticalGradient(listOf(Color(0xFFFF99B7), Color(0xFFFF5284))), RoundedCornerShape(32.dp))
            .padding(horizontal = 16.dp)
            .padding(bottom = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(34.dp), contentAlignment = Alignment.Center
        ) {
            Image(
                painterResource(R.drawable.label_title_red),
                modifier = Modifier.size(144.dp, 34.dp),
                contentDescription = "i",
                contentScale = ContentScale.FillBounds
            )
            Text(text = "奖励预览".localized, color = Color.White, fontSize = 18.sp)
        }
        Spacer(modifier = Modifier.height(16.dp))
        if (list.size < 3) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center
            ) {
                list.forEachIndexed { index, bonus ->
                    BonusItem(
                        bonus = bonus,
                        modifier = Modifier
                            .width(74.dp)
                    )
                    if (index < list.lastIndex) {
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                }
            }
        } else {
            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(list) { bonus ->
                    BonusItem(
                        bonus = bonus,
                        modifier = Modifier
                            .fillMaxWidth()
                    )
                }
            }
        }
        Spacer(modifier = Modifier.height(12.dp))
        Text(
            text = desc,
            fontSize = 12.sp,
            color = Color.White,
            lineHeight = 18.sp
        )
        Spacer(modifier = Modifier.height(16.dp))

        SolidButton(
            "我知道了".localized,
            textColor = colorRed,
            backgroundColor = Color.White,
            onClick = onClick,
            height = 36.dp,
            modifier = Modifier.widthIn(min = 160.dp)
        )
    }
}


@Composable
private fun BonusItem(bonus: Bonus, modifier: Modifier = Modifier) {
    Column(
        modifier = modifier
            .padding(8.dp)
            .heightIn(min = 102.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        NetworkImage(
            bonus.bonusIcon,
            modifier = Modifier
                .size(72.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(Color.White),
            contentScale = ContentScale.Inside
        )
        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = if (bonus.bonusDesc.isNotEmpty()) "${bonus.bonusName}\n${bonus.bonusDesc}" else bonus.bonusName,
            fontSize = 11.sp,
            lineHeight = 16.sp,
            color = Color.White,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun GroupTaskPendant(hasRedDot: Boolean, modifier: Modifier = Modifier) {
    Box(modifier = modifier.size(63.dp, 72.dp)) {
        Image(
            painterResource(R.drawable.icon_box_pendant),
            modifier = Modifier
                .size(56.dp)
                .align(Alignment.TopCenter),
            contentScale = ContentScale.FillBounds,
            contentDescription = "icon"
        )
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(20.dp)
                .background(colorRed, WakooUI.Shapes.chip)
                .align(Alignment.BottomCenter), contentAlignment = Alignment.Center
        ) {
            BasicText(
                "群组任务".localized,
                style = TextStyle(color = Color.White),
                autoSize = TextAutoSize.StepBased(minFontSize = 6.sp, maxFontSize = 12.sp)
            )
        }
        if (hasRedDot) {
            Box(
                modifier = Modifier
                    .padding(bottom = 16.dp)
                    .size(8.dp)
                    .background(Color(0xFFF53F3F), CircleShape)
                    .border(1.dp, Color.White, CircleShape)
                    .align(Alignment.BottomEnd)
            )
        }
    }
}

@Preview
@Composable
private fun GroupTaskPreview() {
    GroupTaskPendant(true)
}

