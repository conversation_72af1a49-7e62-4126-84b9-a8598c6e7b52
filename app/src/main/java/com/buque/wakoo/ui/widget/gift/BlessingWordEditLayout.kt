package com.buque.wakoo.ui.widget.gift

import android.R.attr.maxLength
import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.GradientButton

@Composable
fun BlessingWordEditLayout(
    lastBlessWord: String = "",
    title: String = "填写祝福语".localized,
    placeholder: String = "新年将至，快来为你的亲朋好友送上温馨的祝福吧！".localized,
    hint: String = "请填写新年祝福语，最多20字".localized,
    onButtonClick: (String) -> Unit = {},
) {
    var blessWord by remember {
        mutableStateOf(lastBlessWord)
    }

    Column(
        modifier =
            Modifier
                .background(Color.White, RoundedCornerShape(12.dp))
                .padding(16.dp, 20.dp),
    ) {
        Text(
            text = title,
            color = WakooText,
            modifier =
                Modifier
                    .fillMaxWidth(1f)
                    .padding(bottom = 12.dp),
            textAlign = TextAlign.Center,
            fontSize = 17.sp,
        )

        AppTextField(
            blessWord,
            {
                blessWord = it
            },
            textStyle =
                TextStyle(
                    fontSize = 15.sp,
                    lineHeight = 22.sp,
                    color = WakooText,
                ),
            modifier =
                Modifier
                    .fillMaxWidth()
                    .heightIn(96.dp),
            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 8.dp),
            placeholder = placeholder,
            maxLength = 20,
            backgroundColor = Color(0xFFF7F7F7),
            supportingStyle =
                MaterialTheme.typography.bodyMedium.copy(
                    color = Color(0xFFB6B6B6),
                ),
        )

        Text(
            text = hint,
            lineHeight = 16.sp,
            fontSize = 13.sp,
            color = Color(0xFF999999),
            modifier = Modifier.padding(top = 12.dp, bottom = 20.dp),
        )

        GradientButton(
            "确认并赠送".localized,
            onClick = {
                onButtonClick(blessWord)
            },
            enabled = blessWord.isNotEmpty(),
            modifier =
                Modifier
                    .fillMaxWidth(1f),
        )
    }
}

@Preview(device = "spec:width=270dp,height=850.9dp,dpi=440", showBackground = true)
@Composable
fun BlessingWordEditLayoutPreviewer() {
    BlessingWordEditLayout()
}
