package com.buque.wakoo.ui.screens.chatgroup.tasks.models


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * ```
 * {
 *     "user_active_point": 1000, // 当前用户活跃度
 *     "tribe_active_point": 1000, // 当前部落活跃度
 *     "user_active_point_bonus": 10, // 用户活跃度奖励
 *     "tribe_active_point_bonus": 10,    // 部落活跃度奖励
 *     "contribution_bonus": 10,               // 贡献值
 *     "bonus_icon": "icon url",     // 奖励图标
 *     "bonus_name": "奖励名称",     // 奖励名称
 *     "bonus_desc": "奖励描述"
 *
 * }
 * ```
 */
@Serializable
data class TribeActiveInfo(
    @SerialName("bonus_desc")
    val bonusDesc: String = "",
    @SerialName("bonus_icon")
    val bonusIcon: String = "",
    @SerialName("bonus_name")
    val bonusName: String = "",
    @SerialName("contribution_bonus")
    val contributionBonus: Int = 0,
    @SerialName("tribe_active_point")
    val tribeActivePoint: Int = 0,
    @SerialName("tribe_active_point_bonus")
    val tribeActivePointBonus: Int = 0,
    @SerialName("user_active_point")
    val userActivePoint: Int = 0,
    @SerialName("user_active_point_bonus")
    val userActivePointBonus: Int = 0
)