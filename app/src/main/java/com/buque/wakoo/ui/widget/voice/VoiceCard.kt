package com.buque.wakoo.ui.widget.voice

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.buque.wakoo.R
import com.buque.wakoo.bean.LiveRoomCardItem
import com.buque.wakoo.bean.VoiceCardItem
import com.buque.wakoo.ext.by
import com.buque.wakoo.ext.orNull
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.icons.FollowBg
import com.buque.wakoo.ui.icons.FollowedBg
import com.buque.wakoo.ui.icons.HeartFill
import com.buque.wakoo.ui.icons.More
import com.buque.wakoo.ui.icons.NotFeel
import com.buque.wakoo.ui.icons.PauseCircleFill
import com.buque.wakoo.ui.icons.PlayCircleFill
import com.buque.wakoo.ui.icons.Report
import com.buque.wakoo.ui.icons.StarFill
import com.buque.wakoo.ui.icons.UserForbidLine
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AudioWaveAnimation
import com.buque.wakoo.ui.widget.Rotatable
import com.buque.wakoo.ui.widget.RotationState
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SkewedGradientText
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.popup.BubbleShape

/** 广场Feed流的Card */
@Composable
fun UserVoiceFeedCard(
    item: VoiceCardItem,
    modifier: Modifier = Modifier,
    isPlaying: Boolean = false,
    duration: Int = 0,
    useWeight: Boolean = true,
    onToggleLike: () -> Unit = {},
    onToggleFavorite: () -> Unit = {},
    onToggleFollow: () -> Unit = {},
    onReport: () -> Unit = {},
    onDisLiked: () -> Unit = {},
    onBlacked: () -> Unit = {},
    onUserClick: () -> Unit = {},
    onVoiceClick: () -> Unit = {},
) {
    Column(modifier = modifier) {
        Box(
            modifier =
                Modifier.weight(
                    weight = 1f,
                    fill = false,
                ) to Modifier by useWeight,
        ) {
            NetworkImage(
                data = item.background,
                modifier =
                    Modifier
                        .padding(top = 36.dp)
                        .fillMaxWidth()
                        .matchParentSize()
                        .clip(RoundedCornerShape(30.dp)),
                preview = ColorPainter(Color.Green),
            )

            Column(
                modifier =
                    Modifier
                        .zIndex(1f)
                        .fillMaxWidth()
                        .padding(horizontal = 27.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                VoiceOwnerContent(user = item.user, onUserClick = onUserClick)

                SizeHeight(16.dp)

                VoiceTagContent(tags = item.tags)

                SizeHeight(32.dp)

                VoiceTextContent(
                    text = item.title,
                    modifier =
                        Modifier.weight(
                            weight = 1f,
                            fill = false,
                        ) to Modifier by useWeight,
                    enabledScroll = useWeight,
                )

                SizeHeight(27.dp)

                VoiceAnimatedStateArea(
                    isPlaying = isPlaying,
                    duration = duration,
                    icon = {
                        Image(
                            painter = painterResource(id = R.drawable.ic_voice_tag),
                            contentDescription = "null",
                            modifier = Modifier.size(28.dp),
                        )
                    },
                    onVoiceClick = onVoiceClick,
                )

                SizeHeight(27.dp)
            }

            var expanded by rememberSaveable { mutableStateOf(false) }

            Box(
                modifier =
                    Modifier
                        .align(Alignment.TopEnd)
                        .padding(top = 35.dp, end = 5.dp),
            ) {
                IconButton(
                    onClick = {
                        expanded = true
                    },
                ) {
                    Icon(
                        imageVector = WakooIcons.More,
                        contentDescription = null,
                    )
                }

                val bubbleShape =
                    remember {
                        BubbleShape(arrowPositionBias = 0.85f)
                    }

                // 弹出菜单主体
                DropdownMenu(
                    expanded = expanded, // 菜单的展开状态
                    onDismissRequest = { expanded = false }, // 点击菜单外部或按返回键时关闭菜单
                    shape = bubbleShape,
                    offset = DpOffset((-6).dp, (-12).dp),
                    tonalElevation = 0.dp,
                    shadowElevation = 0.dp,
                    containerColor = Color.White,
                ) {
                    // 菜单项
                    Row(
                        modifier =
                            Modifier
                                .clickable(onClick = {
                                    onReport()
                                    expanded = false
                                })
                                .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                    ) {
                        Icon(
                            imageVector = WakooIcons.Report,
                            modifier = Modifier.size(20.dp, 20.dp),
                            contentDescription = null,
                            tint = Color(0xFF111111),
                        )
                        Text(
                            text = "举报".localized,
                            modifier = Modifier.weight(1f),
                            color = Color(0xFF666666),
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center,
                        )
                    }

                    HorizontalDivider(
                        modifier = Modifier.padding(horizontal = 12.dp),
                        thickness = 0.5.dp,
                        color = Color(0xFFE5E5E5),
                    )

                    Row(
                        modifier =
                            Modifier
                                .clickable(onClick = {
                                    onDisLiked()
                                    expanded = false
                                })
                                .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                    ) {
                        Icon(
                            imageVector = WakooIcons.NotFeel,
                            modifier = Modifier.size(20.dp, 20.dp),
                            contentDescription = null,
                            tint = Color(0xFF111111),
                        )
                        Text(
                            text = "不感兴趣".localized,
                            modifier = Modifier.weight(1f),
                            color = Color(0xFF666666),
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center,
                        )
                    }

                    HorizontalDivider(
                        modifier = Modifier.padding(horizontal = 12.dp),
                        thickness = 0.5.dp,
                        color = Color(0xFFE5E5E5),
                    )

                    Row(
                        modifier =
                            Modifier
                                .clickable(onClick = {
                                    onBlacked()
                                    expanded = false
                                })
                                .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                    ) {
                        Icon(
                            imageVector = WakooIcons.UserForbidLine,
                            modifier = Modifier.size(20.dp, 20.dp),
                            contentDescription = null,
                            tint = Color(0xFF111111),
                        )
                        Text(
                            text = "拉黑".localized,
                            modifier = Modifier.weight(1f),
                            color = Color(0xFF666666),
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center,
                        )
                    }
                }
            }
        }

        Image(
            painter = painterResource(id = R.drawable.bg_voice_card_bottom),
            contentDescription = "null",
            modifier =
                Modifier
                    .padding(horizontal = 12.dp)
                    .fillMaxWidth()
                    .offset(y = (-5).dp),
            contentScale = ContentScale.FillWidth,
        )

        SizeHeight(16.dp)

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceAround,
            verticalAlignment = Alignment.Bottom,
        ) {
            Box(
                modifier = Modifier.padding(horizontal = 12.dp),
                contentAlignment = Alignment.Center,
            ) {
                Image(
                    imageVector = if (!item.isFollow) WakooIcons.FollowBg else WakooIcons.FollowedBg,
                    modifier = Modifier.matchParentSize(),
                    contentDescription = "null",
                    contentScale = ContentScale.FillBounds,
                )

                TextButton(onClick = onToggleFollow) {
                    SkewedGradientText(
                        text = if (!item.isFollow) "关注TA".localized else "已关注".localized,
                        enableBrush = !item.isFollow,
                        modifier =
                            Modifier.padding(
                                top = 6.dp,
                                start = 6.dp,
                            ),
                        minFontSize = 10.sp,
                        maxFontSize = 16.sp,
                    )
                }
            }

            RoundedButton(
                imageVector = WakooIcons.HeartFill,
                enableTint = !item.isLike,
                onClick = onToggleLike,
            )

            RoundedButton(
                imageVector = WakooIcons.StarFill,
                enableTint = !item.isFavorite,
                onClick = onToggleFavorite,
            )
        }
    }
}

@Composable
fun LiveRoomFeedCard(
    item: LiveRoomCardItem,
    modifier: Modifier = Modifier,
    useWeight: Boolean = true,
    onJoinRoom: () -> Unit = {},
) {
    Column(modifier = modifier) {
        Box(
            modifier =
                Modifier.weight(
                    weight = 1f,
                    fill = false,
                ) to Modifier by useWeight,
        ) {
            NetworkImage(
                data = item.background,
                modifier =
                    Modifier
                        .padding(top = 36.dp)
                        .fillMaxWidth()
                        .matchParentSize()
                        .clip(RoundedCornerShape(30.dp))
                        .border(
                            2.dp,
                            brush =
                                Brush.horizontalGradient(
                                    listOf(
                                        Color(0xFFA3FF2C),
                                        Color(0xFF31FFA1),
                                    ),
                                ),
                            shape = RoundedCornerShape(30.dp),
                        ),
                preview = ColorPainter(Color(0xFF272280)),
            )

            Column(
                modifier =
                    Modifier
                        .zIndex(1f)
                        .fillMaxWidth()
                        .padding(horizontal = 27.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Box {
                        Rotatable(
                            RotationState.PLAYING,
                            modifier =
                                Modifier.border(
                                    2.dp,
                                    brush =
                                        Brush.horizontalGradient(
                                            listOf(
                                                Color(0xFFA3FF2C),
                                                Color(0xFF31FFA1),
                                            ),
                                        ),
                                    CircleShape,
                                ),
                            animationDurationMillis = 5000,
                        ) {
                            AvatarNetworkImage(
                                user = item.user,
                                size = 90.dp,
                                border = BorderStroke(4.dp, WakooWhite),
                            )
                        }
                        Row(
                            modifier =
                                Modifier
                                    .align(Alignment.BottomCenter)
                                    .background(
                                        Brush.horizontalGradient(
                                            listOf(
                                                Color(0xFFA3FF2C),
                                                Color(0xFF31FFA1),
                                            ),
                                        ),
                                        CircleShape,
                                    ).padding(horizontal = 10.dp, vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            AudioWaveAnimation(
                                isPlaying = true,
                                modifier =
                                    Modifier
                                        .size(12.dp)
                                        .padding(bottom = 1.dp),
                                barColor = Color(0xff111111),
                                barSpacing = 1.5.dp,
                                maxHeightFraction = 1f,
                                minHeightFraction = 0.2f,
                                fromCenter = false,
                                radius = 3.dp,
                            )
                            Spacer(Modifier.width(2.dp))
                            Text(
                                text = "Live",
                                fontSize = 12.sp,
                                lineHeight = 12.sp,
                                fontFamily = FontFamily.MI_SANS,
                            )
                        }
                    }

                    SizeHeight(12.dp)

                    Text(
                        text = item.title,
                        style = MaterialTheme.typography.bodyMedium,
                        color = WakooWhite,
                    )
                }

                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .weight(1f),
                    contentAlignment = Alignment.Center,
                ) {
                    LiveMicLayout(item = item)
                }

                Row(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(56.dp)
                            .background(Color(0x2effffff), shape = CircleShape)
                            .border(1.dp, Color.White, CircleShape)
                            .padding(horizontal = 12.dp)
                            .clickable(onClick = onJoinRoom),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    AudioWaveAnimation(
                        isPlaying = true,
                        modifier =
                            Modifier
                                .size(16.dp)
                                .padding(bottom = 1.dp),
                        barColor = WakooGreen, // 类似 Spotify 的绿色
                        barSpacing = 3.5.dp,
                        fromCenter = false,
                        maxHeightFraction = 1f,
                        minHeightFraction = 0.2f,
                    )
                    SizeWidth(4.dp)
                    BasicText(
                        text = "进入语音互动房间".localized,
                        style =
                            MaterialTheme.typography.titleSmall.copy(
                                color = Color(0xffA3FF2C),
                                fontFamily = FontFamily.MI_SANS,
                                textAlign = TextAlign.Center,
                            ),
                        maxLines = 1,
                        autoSize =
                            TextAutoSize.StepBased(
                                minFontSize = 10.sp,
                                maxFontSize = 16.sp,
                            ),
                    )
                }

                SizeHeight(50.dp)
            }

            if (item.lastUsers.isNotEmpty()) {
                Row(
                    modifier =
                        Modifier
                            .align(Alignment.TopEnd)
                            .padding(top = 50.dp, end = 16.dp)
                            .background(color = Color(0x26ffffff), CircleShape)
                            .padding(2.dp),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy((-4).dp),
                    ) {
                        item.lastUsers.take(3).forEachIndexed { index, user ->
                            NetworkImage(
                                user.avatarUrl,
                                modifier =
                                    Modifier
                                        .zIndex(4f - index)
                                        .size(20.dp)
                                        .border(1.dp, WakooWhite, CircleShape)
                                        .clip(CircleShape),
                            )
                        }
                    }
                    Text(
                        "${item.inRoomUserCnt}",
                        color = Color.White,
                        fontSize = 10.sp,
                        lineHeight = 10.sp,
                        modifier = Modifier.padding(start = 4.dp, end = 10.dp),
                    )
                }
            }
        }

        Image(
            painter = painterResource(id = R.drawable.bg_voice_card_bottom),
            contentDescription = "null",
            modifier =
                Modifier
                    .padding(horizontal = 12.dp)
                    .fillMaxWidth()
                    .offset(y = (-5).dp),
            contentScale = ContentScale.FillWidth,
        )
    }
}

@Preview
@Composable
private fun LiveRoomFeedCardPreview() {
    WakooTheme {
        LiveRoomFeedCard(
            LiveRoomCardItem.preview,
        )
    }
}

/** 其他List流的Card */
@Composable
fun UserVoiceListCard(
    item: VoiceCardItem,
    showOwnerInfo: Boolean,
    modifier: Modifier = Modifier,
    isPlaying: Boolean = false,
    duration: Int = 0,
    useWeight: Boolean = false,
    showActionArea: Boolean = true,
    onVoiceTextContent: @Composable ColumnScope.() -> Unit = {
        VoiceTextContent(
            text = item.title,
            modifier =
                Modifier.weight(
                    weight = 1f,
                    fill = false,
                ) to Modifier by useWeight,
            enabledScroll = useWeight,
        )
    },
    onToggleLike: () -> Unit = {},
    onToggleFavorite: () -> Unit = {},
    onReport: () -> Unit = {},
    onDelete: () -> Unit = {},
    onUserClick: () -> Unit = {},
    onVoiceClick: () -> Unit = {},
) {
    Column(modifier = modifier) {
        Box(
            Modifier.weight(
                weight = 1f,
                fill = false,
            ) to Modifier by useWeight,
        ) {
            NetworkImage(
                data = item.background,
                modifier =
                    Modifier
                        .padding(top = if (showOwnerInfo) 36.dp else 0.dp)
                        .fillMaxWidth()
                        .matchParentSize()
                        .clip(
                            if (showActionArea) {
                                RoundedCornerShape(
                                    topStart = 30.dp,
                                    topEnd = 30.dp,
                                )
                            } else {
                                RoundedCornerShape(30.dp)
                            },
                        ),
                preview = ColorPainter(Color.Green),
            )

            Column(
                modifier =
                    Modifier
                        .zIndex(1f)
                        .fillMaxWidth()
                        .padding(horizontal = 27.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                if (showOwnerInfo) {
                    VoiceOwnerContent(user = item.user, onUserClick = onUserClick)
                }

                SizeHeight(16.dp)

                VoiceTagContent(tags = item.tags)

                SizeHeight(if (showOwnerInfo) 32.dp else 8.dp)

                onVoiceTextContent()

                SizeHeight(if (showOwnerInfo) 27.dp else 20.dp)

                VoiceAnimatedStateArea(
                    isPlaying = isPlaying,
                    duration = duration,
                    icon = {
                        Image(
                            imageVector = if (!isPlaying) WakooIcons.PlayCircleFill else WakooIcons.PauseCircleFill,
                            contentDescription = "null",
                            modifier = Modifier.size(32.dp),
                        )
                    },
                    onVoiceClick = onVoiceClick,
                    rotationState = RotationState.STOPPED,
                )

                SizeHeight(if (showOwnerInfo) 27.dp else 20.dp)
            }
        }

        if (showActionArea) {
            VoiceActionArea(
                item = item,
                modifier = Modifier.fillMaxWidth(),
                onToggleLike = onToggleLike,
                onToggleFavorite = onToggleFavorite,
                onReport = onReport,
                onDelete = onDelete,
            )
        }
    }
}

@Composable
private fun RoundedButton(
    imageVector: ImageVector,
    enableTint: Boolean,
    onClick: () -> Unit = {},
) {
    Box(
        modifier =
            Modifier
                .size(56.dp)
                .clip(RoundedCornerShape(14.dp))
                .clickable(onClick = onClick)
                .background(if (enableTint) Color(0xFFE9EAEF) else Color(0xFF111111)),
        contentAlignment = Alignment.Center,
    ) {
        Image(
            imageVector = imageVector,
            contentDescription = "null",
            colorFilter = enableTint.orNull { ColorFilter.tint(Color(0xFF111111)) },
        )
    }
}
