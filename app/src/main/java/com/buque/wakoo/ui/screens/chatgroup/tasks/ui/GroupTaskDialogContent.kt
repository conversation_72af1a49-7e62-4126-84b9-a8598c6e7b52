package com.buque.wakoo.ui.screens.chatgroup.tasks.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowLeft
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.icons.QuestionLine
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.utils.eventBus.tryToLink

@Composable
fun GroupTaskDialogContent(boxVM: TribeBoxViewModel, tabIndex: Int = 0, onRefresh: OnAction = {}, onDismiss: OnAction = {}) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(375 / 642f)
            .background(Color.White, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .navigationBarsPadding()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .height(48.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                Icons.Default.KeyboardArrowLeft,
                contentDescription = null,
                modifier = Modifier
                    .size(24.dp)
                    .click(onClick = onDismiss)
            )
            Text(
                "群组任务".localized,
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center,
                fontSize = 16.sp,
                color = WakooText
            )
            Image(
                WakooIcons.QuestionLine, contentDescription = "q",
                modifier = Modifier
                    .size(24.dp)
                    .click(onClick = {
                        boxVM.boxInfo.value.dataOrNull?.bonusTaskRule?.tryToLink()
                    })
            )
        }
        GroupTaskContainer(vm = boxVM, tabIndex = tabIndex, onRefreshGroupInfo = onRefresh, onDismiss = onDismiss)
    }
}