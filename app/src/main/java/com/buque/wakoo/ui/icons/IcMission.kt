package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.IcMission: ImageVector
    get() {
        if (_IcMission != null) {
            return _IcMission!!
        }
        _IcMission = ImageVector.Builder(
            name = "IcMission",
            defaultWidth = 20.dp,
            defaultHeight = 20.dp,
            viewportWidth = 20f,
            viewportHeight = 20f
        ).apply {
            path(fill = SolidColor(Color(0xFF111111))) {
                moveTo(14.167f, 1.667f)
                horizontalLineTo(16.667f)
                curveTo(17.127f, 1.667f, 17.5f, 2.04f, 17.5f, 2.5f)
                verticalLineTo(17.5f)
                curveTo(17.5f, 17.96f, 17.127f, 18.333f, 16.667f, 18.333f)
                horizontalLineTo(3.333f)
                curveTo(2.873f, 18.333f, 2.5f, 17.96f, 2.5f, 17.5f)
                verticalLineTo(2.5f)
                curveTo(2.5f, 2.04f, 2.873f, 1.667f, 3.333f, 1.667f)
                horizontalLineTo(5.833f)
                verticalLineTo(0f)
                horizontalLineTo(7.5f)
                verticalLineTo(1.667f)
                horizontalLineTo(12.5f)
                verticalLineTo(0f)
                horizontalLineTo(14.167f)
                verticalLineTo(1.667f)
                close()
                moveTo(14.167f, 3.333f)
                verticalLineTo(5f)
                horizontalLineTo(12.5f)
                verticalLineTo(3.333f)
                horizontalLineTo(7.5f)
                verticalLineTo(5f)
                horizontalLineTo(5.833f)
                verticalLineTo(3.333f)
                horizontalLineTo(4.167f)
                verticalLineTo(16.667f)
                horizontalLineTo(15.833f)
                verticalLineTo(3.333f)
                horizontalLineTo(14.167f)
                close()
                moveTo(5.833f, 6.667f)
                horizontalLineTo(14.167f)
                verticalLineTo(8.333f)
                horizontalLineTo(5.833f)
                verticalLineTo(6.667f)
                close()
                moveTo(5.833f, 10f)
                horizontalLineTo(14.167f)
                verticalLineTo(11.667f)
                horizontalLineTo(5.833f)
                verticalLineTo(10f)
                close()
            }
        }.build()

        return _IcMission!!
    }

@Suppress("ObjectPropertyName")
private var _IcMission: ImageVector? = null
