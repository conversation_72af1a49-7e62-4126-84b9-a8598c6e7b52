package com.buque.wakoo.ui.screens.messages.chat

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.fadeIn
import androidx.compose.animation.slideIn
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.core.net.toUri
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.OnContent
import com.buque.wakoo.bean.BegGiftBean
import com.buque.wakoo.bean.ConversationConfig
import com.buque.wakoo.bean.PrivilegedGiftRemindBean
import com.buque.wakoo.bean.UIConfig
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.bean.user.ONLINE_STATUS_ONLINE
import com.buque.wakoo.bean.user.SelfUserInfo
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.bean.user.UserProfileInfo
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.bean.SendParams
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.inter.MsgFilter
import com.buque.wakoo.im.utils.WatchMessageEventEffect
import com.buque.wakoo.im_business.interf.IC2CAction
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.MessageC2CUserScaffold
import com.buque.wakoo.im_business.message.MessageThemeBubble
import com.buque.wakoo.im_business.message.UIMessageEntry
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.message.types.UCImageMessage
import com.buque.wakoo.im_business.message.ui.entry.C2CContent
import com.buque.wakoo.im_business.message.ui.entry.CPInviteME
import com.buque.wakoo.im_business.message.ui.entry.CronyAcceptEntry
import com.buque.wakoo.im_business.message.ui.entry.CronyMessageEntry
import com.buque.wakoo.im_business.message.ui.entry.GiftMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.GuideCPSuccessME
import com.buque.wakoo.im_business.message.ui.entry.ImageMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.MakCPSuccessME
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
import com.buque.wakoo.im_business.message.ui.entry.PrivateRoomEventEntry
import com.buque.wakoo.im_business.message.ui.entry.TimeMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.VoiceCallEventEntry
import com.buque.wakoo.im_business.panel.voice.AudioCenterStatusWidget
import com.buque.wakoo.im_business.viewmodel.C2CChatViewModel
import com.buque.wakoo.im_business.wigets.C2CBottomBar
import com.buque.wakoo.im_business.wigets.C2CBottomPanel
import com.buque.wakoo.im_business.wigets.ChatScaffold
import com.buque.wakoo.im_business.wigets.KeyboardPanelState
import com.buque.wakoo.im_business.wigets.rememberPanelState
import com.buque.wakoo.im_business.wigets.withAutoHidePanel
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.navigation.rememberLauncherForResult
import com.buque.wakoo.network.api.service.C2CChatApi
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.dialog.AnyPopDialogProperties
import com.buque.wakoo.ui.dialog.FreeGiftRemindContent
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.floating.BuddyFloatingItem
import com.buque.wakoo.ui.icons.More
import com.buque.wakoo.ui.icons.Report
import com.buque.wakoo.ui.icons.UserForbidLine
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.widget.AudioStateBar
import com.buque.wakoo.ui.widget.CommonPendantBanner
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.WakooTitleBar
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults
import com.buque.wakoo.ui.widget.drag.FloatingLayoutManager
import com.buque.wakoo.ui.widget.gift.C2CBottomGiftPanel
import com.buque.wakoo.ui.widget.gift.GiftItemDetailDialogContent
import com.buque.wakoo.ui.widget.gift.GiftPosition
import com.buque.wakoo.ui.widget.gift.GiftViewModel
import com.buque.wakoo.ui.widget.gift.OpenGiftPanelAction
import com.buque.wakoo.ui.widget.gift.UserGiftWallTab
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.media.previewer.MediaPreviewState
import com.buque.wakoo.ui.widget.media.previewer.MediaViewerAlbum
import com.buque.wakoo.ui.widget.media.previewer.MediaViewerKey
import com.buque.wakoo.ui.widget.media.previewer.TransitionOverlay
import com.buque.wakoo.ui.widget.media.previewer.rememberPreviewState
import com.buque.wakoo.ui.widget.media.selector.MediaSelectorResult
import com.buque.wakoo.ui.widget.overScrollVertical
import com.buque.wakoo.ui.widget.pagination.PaginateState
import com.buque.wakoo.ui.widget.popup.BubbleShape
import com.buque.wakoo.ui.widget.rememberOverscrollFlingBehavior
import com.buque.wakoo.utils.eventBus.EventBusEffect
import com.buque.wakoo.utils.eventBus.tryToLink
import com.buque.wakoo.viewmodel.UserProfileViewModel
import kotlinx.coroutines.launch

@Composable
fun C2CChatScreen(
    user: User,
    viewModel: C2CChatViewModel,
    modifier: Modifier = Modifier,
) {
    val rootNavController = LocalAppNavController.root

    val density = LocalDensity.current

    val scope = rememberCoroutineScope()

    val listState = rememberLazyListState()

    val panelState = rememberPanelState(listState, viewModel.panels, 12)

    val userViewModel =
        viewModel<UserProfileViewModel>(key = "profile-${user.id}") {
            UserProfileViewModel(user)
        }

    val targetUserInfo by userViewModel.userProfileInfoFlow.collectAsStateWithLifecycle()

    val selfUser = LocalSelfUserProvider.current

    val giftViewModel =
        viewModel<GiftViewModel>(key = "gift-${user.id}", initializer = {
            GiftViewModel(user.id, ConversationType.C2C)
        })

    val c2cConfig by viewModel.c2cConfig

    val uiConfig by AppConfigManager.uiConfigFlow.collectAsStateWithLifecycle()

    val launcher =
        rootNavController.rememberLauncherForResult<Route.MediaSelector, MediaSelectorResult> { result ->
            if (result.list.isNotEmpty()) {
                IMCompatCore.sendMessages(
                    SendParams(user.id, ConversationType.C2C),
                    result.list.map {
                        MessageBundle.Image.create(it.uriString.toUri(), "", it.width, it.height)
                    },
                )
            }
        }

    val dialogController = rememberDialogController(false, key = "dialog-c2c-${user.id}")

    WatchMessageEventEffect(
        object : IMCompatListener {
            override val filter: MsgFilter = MsgFilter(targetUserInfo.id)

            override fun onRecvNewCustomMessage(
                message: UCCustomMessage,
                offline: Boolean,
            ) {
                super.onRecvNewCustomMessage(message, offline)
                when (message.cmd) {
                    IMEvent.CONFIRM_CP -> userViewModel.refresh()
                }
            }
        },
    )

    val lm = LocalLoadingManager.current

    val previewState = rememberPreviewState()

    // 转场覆盖层，负责在转场期间显示动画元素和全屏查看器。
    TransitionOverlay(state = previewState)

    //region 礼物相关方法

    val openGiftWall = {
        dialogController.easyPost(
            dialogProperties = AnyPopDialogProperties(useCustomAnimation = true),
            content = {
                Column(modifier = Modifier.fillMaxWidth().heightIn(max = 540.dp)) {
                    UserGiftWallTab(targetUserInfo.id) { gift ->
                        val targetUserId = user.id.toInt()
                        dialogController.easyPost {
                            GiftItemDetailDialogContent(
                                gift = gift.gift,
                                targetUserId = targetUserId,
                                purpose = 2,
                                sceneType = 4,
                                sceneId = targetUserId,
                                onClose = {
                                    dismiss()
                                },
                            )
                        }
                    }
                }
            },
        )
    }

    val openGiftPanel = { position: GiftPosition?, count: Int ->
        giftViewModel.fetchGiftData()
        dialogController.easyPost(
            dialogProperties = AnyPopDialogProperties(useCustomAnimation = true),
            content = {
                val giftModel = giftViewModel.giftListModelState
                C2CBottomGiftPanel(
                    position,
                    giftModel,
                    defaultCount = count,
                    onOpenWall = {
                        openGiftWall()
                    },
                ) { gift, params ->
                    giftViewModel.sendGiftC2C(gift, params)
                }
            },
        )
    }

    //endregion

    val onAction =
        remember {
            object : IC2CAction {
                override fun onSendMessage(message: MessageBundle) {
                    IMCompatCore.sendC2CMessage(targetUserInfo.id, message)
                }

                override fun onSendMultipleMessage(messages: List<MessageBundle>) {
                    if (messages.isNotEmpty()) {
                        IMCompatCore.sendMessages(SendParams(user.id, ConversationType.C2C), messages)
                    } else {
                        showToast("消息发送失败".localized)
                    }
                }

                override fun onResendMessage(message: UCInstanceMessage) {
                    IMCompatCore.sendMessage(SendParams(user.id, ConversationType.C2C), message.base)
                }

                override fun onReportUser() {
                    rootNavController.push(Route.Report(1, targetUserInfo.id))
                }

                override fun onForbidUser() {
                    viewModel.blackUser(targetUserInfo.id)
                }

                override fun onGoMediaSelector() {
                    launcher.launch(Route.MediaSelector())
                }

                override fun onShowGiftPanel() {
                    openGiftPanel(null, 1)
                }

                override fun onAddOppositeAsFriend() {
                    lm.show(null) {
                        userViewModel.addFriendFromPrivateChat()
                    }
                }

                override fun onNavigation(route: AppNavKey) {
                    rootNavController.push(route)
                }

                override fun toCp() {
                    scope.launch {
                        viewModel.toCp()
                    }
                }

                override fun onJoinVoiceRoom(roomId: Int) {
                    LiveRoomManager.joinRoom(roomId = roomId.toString())
                }

                override fun onPreview(message: UCInstanceMessage) {
                    if (message !is UCImageMessage) return
                    val (startIndex, album) = viewModel.getPreviewImageList(true, message)
                    previewState.enterPreview(
                        key = MediaViewerKey(album = MediaViewerAlbum(album), initialIndex = startIndex),
                        radius = with(density) { 10.dp.toPx() },
                    )
                }

                override fun onSendFreeGift(isAnchorMsg: Boolean) {
                    lm.show(scope) {
                        val targetUid = user.id

                        executeApiCallExpectingData {
                            if (isAnchorMsg) {
                                C2CChatApi.instance.giveFreeGiftByAnchor(mapOf("target_user_id" to targetUid))
                            } else {
                                C2CChatApi.instance.returnGiftToAnchor(mapOf("target_user_id" to targetUid))
                            }
                        }.onSuccess {
                        }
                    }
                }

                override fun onShowCharityGiftPanel(
                    bean: BegGiftBean,
                    isSelf: Boolean,
                ) {
                    if (!isSelf) {
                        dialogController.easyPost {
                            val targetUserId = user.id.toInt()
                            GiftItemDetailDialogContent(
                                bean.gift,
                                targetUserId,
                                3,
                                4,
                                targetUserId,
                                bean.begId,
                                bean.count,
                                {
                                    dismiss()
                                },
                                onConfirm = { bean, count ->
                                    if (bean.isBlindboxGift) {
                                        dismiss()
                                        openGiftPanel(GiftPosition(-bean.blindboxId), count)
                                        return@GiftItemDetailDialogContent true
                                    } else {
                                        false
                                    }
                                },
                            )
                        }
                    }
                }
            }
        }

    val lifecycleOwner = LocalLifecycleOwner.current

    // 清理资源
    DisposableEffect(Unit) {
        val observer =
            LifecycleEventObserver { source, event ->
                if (event == Lifecycle.Event.ON_STOP) {
                    MediaPlayerManager.pauseIf {
                        it.tag.startsWith("voiceMsg")
                    }
                } else if (event == Lifecycle.Event.ON_RESUME) {
                    viewModel.refresh()
                }
            }

        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            MediaPlayerManager.releaseIf {
                it.tag.startsWith("voiceMsg")
            }
        }
    }

    EventBusEffect<OpenGiftPanelAction> { action ->
        if (action.userId != user.id) {
            return@EventBusEffect
        }
        openGiftPanel(action.position, 1)
    }

    Box(
        modifier = Modifier.fillMaxSize(),
    ) {
        val paginateState = viewModel.bindListState(listState)
        C2CChatPage(
            targetUserInfo = targetUserInfo,
            selfUser = selfUser,
            messageList = viewModel.messageList,
            paginateState = paginateState,
            listState = listState,
            panelState = panelState,
            previewState = previewState,
            modifier = modifier,
            overlayContent = {
                AudioCenterStatusWidget()
                giftViewModel.GiftEffectView()
                MemberRemainDialog(user.id) { position ->
                    openGiftPanel(position, 1)
//                    dialogController.easyPost(
//                        dialogProperties = AnyPopDialogProperties(useCustomAnimation = true),
//                        content = {
//                            val giftModel = giftViewModel.giftListModelState
//                            C2CBottomGiftPanel(position, giftModel) { gift, params ->
//                                giftViewModel.sendGiftC2C(gift, params)
//                            }
//                        },
//                    )
                }
            },
            safeContent = {
                FloatingLayoutManager {
                    BuddyFloatingItem(targetUserInfo, viewModel.c2cConfig, viewModel.coupleInfo) {
                        onAction.toCp()
                    }

                    CommonPendantBanner(position = 308)
                }
            },
            titleAction = {
                RelationActionButton(uiConfig, targetUserInfo, onAction)
            },
            config = c2cConfig,
            onAction = onAction,
        )

        dialogController.RenderDialogs(Unit)
    }
}

@Composable
fun MemberRemainDialog(
    userId: String,
    onPanelOpen: (pos: GiftPosition) -> Unit,
) {
    var privilegedGiftRemainBean by rememberSaveable(stateSaver = PrivilegedGiftRemindBean.Saver) {
        mutableStateOf<PrivilegedGiftRemindBean?>(null)
    }

    WatchMessageEventEffect(
        object : IMCompatListener {
            override fun onRecvNewCustomMessage(
                message: UCCustomMessage,
                offline: Boolean,
            ) {
                super.onRecvNewCustomMessage(message, offline)
                when (message.cmd) {
                    IMEvent.GIFT_UNUSED_POPUP -> {
                        // 1.0.6 特权礼物赠送提醒
                        val data = message.parseDataJson<PrivilegedGiftRemindBean>() ?: return

                        if (data.target_user.id == userId) {
                            privilegedGiftRemainBean = data
                        }
                    }
                }
            }
        },
    )

    val data = privilegedGiftRemainBean
    if (data != null) {
        Dialog({
            if (privilegedGiftRemainBean != null) {
                onPanelOpen(GiftPosition(data.gift.id))
                privilegedGiftRemainBean = null
            }
        }) {
            FreeGiftRemindContent(data, {
                onPanelOpen(GiftPosition(data.gift.id))
                privilegedGiftRemainBean = null
            })
        }
    }
}

@Composable
fun RelationActionButton(
    uiConfig: UIConfig,
    targetUserInfo: UserProfileInfo,
    onAction: IC2CAction,
) {
    val nav = LocalAppNavController.root
    val self = LocalSelfUserProvider.current
    val info = targetUserInfo.cpRelationInfo
    var onClick: OnAction = {}
    val buttonText =
        if (info == null || self.isJP) {
            ""
        } else if (!uiConfig.partnerHasEscaped && info.isCp) { // my cp
            onClick = {
                nav.push(Route.UserProfile(targetUserInfo.toBasic()))
            }
            "CP空间".localized
        } else if (!uiConfig.partnerHasEscaped &&
            (self.isHQU || info.isFriend || !info.needFriendRelation) &&
            targetUserInfo.gender != self.gender
        ) {
            onClick = {
                onAction.toCp()
            }
            "组CP".localized
        } else if (!info.isFriend && info.needFriendRelation) {
            onClick = {
                onAction.onAddOppositeAsFriend()
            }
            "加好友".localized
        } else {
            ""
        }
    AnimatedVisibility(
        buttonText.isNotEmpty(),
        enter =
            fadeIn() +
                slideIn(initialOffset = {
                    IntOffset(it.width / 2, 0)
                }),
    ) {
        SolidButton(
            text = buttonText,
            backgroundColor = Color(0xFFFE669E),
            textColor = Color.White,
            fontSize = 12.sp,
            modifier =
                Modifier.heightIn(max = 28.dp),
            onClick = onClick,
            paddingValues = PaddingValues(horizontal = 12.dp),
        )
    }
}

@Composable
private fun C2CChatPage(
    targetUserInfo: UserProfileInfo,
    selfUser: SelfUserInfo,
    messageList: List<UIMessageEntry>,
    paginateState: PaginateState<*>,
    listState: LazyListState,
    panelState: KeyboardPanelState,
    previewState: MediaPreviewState,
    modifier: Modifier = Modifier,
    hiddenModules: List<String>? = null,
    overlayContent: @Composable BoxScope.() -> Unit = {},
    safeContent: @Composable BoxScope.() -> Unit = {},
    titleAction: OnContent = {},
    config: ConversationConfig,
    onAction: IC2CAction,
) {
    val textFieldValue =
        rememberSaveable(stateSaver = TextFieldValue.Saver) {
            mutableStateOf(TextFieldValue())
        }

    var expanded by rememberSaveable { mutableStateOf(false) }

    ChatScaffold(
        panelState = panelState,
        modifier = modifier.background(Color.White),
        topBar = {
            WakooTitleBar(title = {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        text = targetUserInfo.name,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        modifier =
                            Modifier
                                .padding(horizontal = 16.dp),
                        textAlign = TextAlign.Center,
                        maxLines = 1,
                    )
                    if (targetUserInfo.onlineStatus == ONLINE_STATUS_ONLINE) {
                        SizeHeight(6.dp)
                        Text(
                            "在线".localized,
                            fontSize = 10.sp,
                            lineHeight = 12.sp,
                            color = Color(0xFF13EC55),
                            textAlign = TextAlign.Center,
                        )
                    }
                }
            }, actions = {
                titleAction()
                Box {
                    WakooTitleBarDefaults.IconButtonAction(
                        imageVector = WakooIcons.More,
                        onClick = {
                            expanded = true
                        },
                    )

                    val bubbleShape =
                        remember {
                            BubbleShape(arrowPositionBias = 0.85f)
                        }

                    // 弹出菜单主体
                    DropdownMenu(
                        expanded = expanded, // 菜单的展开状态
                        onDismissRequest = { expanded = false }, // 点击菜单外部或按返回键时关闭菜单
                        shape = bubbleShape,
                        offset = DpOffset((-6).dp, (-12).dp),
                        tonalElevation = 0.dp,
                        shadowElevation = 1.dp,
                        containerColor = Color.White,
                    ) {
                        // 菜单项
                        Row(
                            modifier =
                                Modifier
                                    .clickable(onClick = {
                                        expanded = false
                                        onAction.onReportUser()
                                    })
                                    .padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                        ) {
                            Icon(
                                imageVector = WakooIcons.Report,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp),
                                tint = Color(0xFF111111),
                            )
                            Text(
                                text = "举报".localized,
                                modifier = Modifier.weight(1f),
                                color = Color(0xFF666666),
                                fontSize = 14.sp,
                                textAlign = TextAlign.Center,
                            )
                        }

                        HorizontalDivider(
                            modifier = Modifier.padding(horizontal = 12.dp),
                            thickness = 0.5.dp,
                            color = Color(0xFFE5E5E5),
                        )

                        Row(
                            modifier =
                                Modifier
                                    .clickable(onClick = {
                                        expanded = false
                                        onAction.onForbidUser()
                                    })
                                    .padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                        ) {
                            Icon(
                                imageVector = WakooIcons.UserForbidLine,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp),
                                tint = Color(0xFF111111),
                            )
                            Text(
                                text = "拉黑".localized,
                                modifier = Modifier.weight(1f),
                                color = Color(0xFF666666),
                                fontSize = 14.sp,
                                textAlign = TextAlign.Center,
                            )
                        }
                    }
                }
            })
        },
        bottomBar = {
            C2CBottomBar(panelState, textFieldValue, hiddenModules, showGiftButton = config.showGiftButton, onAction)
        },
        panelContent = {
            C2CBottomPanel(panelState, textFieldValue, hiddenModules, onAction)
        },
        overlayContent = overlayContent,
        safeContent = safeContent,
    ) {
        val isLoading by remember {
            derivedStateOf {
                paginateState.nextLoadState.isLoading
            }
        }

        val isEnd by remember {
            derivedStateOf {
                paginateState.nextLoadState.isEnd
            }
        }

        Column(modifier = Modifier.background(Color(0xFFF5F7F9))) {
            if (targetUserInfo.isInRoom) {
                AudioStateBar(
                    targetUserInfo,
                    modifier =
                        Modifier
                            .padding(horizontal = 16.dp, vertical = 8.dp)
                            .fillMaxWidth(),
                ) {
                    onAction.onJoinVoiceRoom(it)
                }
            }
            LazyColumn(
                modifier =
                    Modifier
                        .withAutoHidePanel(panelState)
                        .fillMaxSize()
                        .onGloballyPositioned {
                            previewState.overlayClipRect = it.boundsInRoot()
                        }.overScrollVertical(),
                contentPadding = PaddingValues(vertical = 16.dp),
                state = listState,
                reverseLayout = true,
                verticalArrangement = Arrangement.spacedBy(16.dp),
                flingBehavior = rememberOverscrollFlingBehavior { listState },
            ) {
                items(messageList, contentType = { item ->
                    item::class.simpleName
                }) { item ->
                    val uiEntry = item.uiEntry
                    when (uiEntry) {
                        is MsgLayoutContent -> {
                            uiEntry.Render({
                                MessageC2CUserScaffold(
                                    item.message,
                                    onAction,
                                    showReadStatus =
                                        uiEntry.supportReadReceipt && config.showReadMark,
                                ) { it() }
                            }, {
                                MessageThemeBubble(entry = item) { it() }
                            }, onAction)
                        }

                        is ImageMsgEntry -> {
                            uiEntry.C2CContent(previewState, item.message, onAction)
                        }

                        is GiftMsgEntry -> {
                            uiEntry.C2CContent()
                        }

                        is CPInviteME -> {
                            Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                                BuddyCard(
                                    uiEntry.message,
                                    "查看CP邀请".localized,
                                    bgRes = R.drawable.bg_buddy_invite,
                                    spacing = 12.dp,
                                    onClick = {
                                        if (System.currentTimeMillis() > uiEntry.expireTimeStamp * 1000) {
                                            showToast("邀请已过期".localized)
                                            return@BuddyCard
                                        }
                                        val link =
                                            C2CChatViewModel.appendMakeBuddyParams(
                                                uiEntry.link,
                                                targetUserInfo.user,
                                                uiEntry.inviteCode,
                                            )
                                        link.tryToLink()
                                    },
                                )
                            }
                        }

                        is MakCPSuccessME -> {
                            Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                                BuddyCard(uiEntry.digest, "查看CP空间".localized, onClick = {
                                    LocalAppNavController.useRoot?.push(Route.UserProfile(selfUser.toBasic()))
                                })
                            }
                        }

                        is GuideCPSuccessME -> {
                            Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                                Box(modifier = Modifier.width(295.dp)) {
                                    BuddyCard(
                                        uiEntry.digest,
                                        "去组CP".localized,
                                        modifier = Modifier.padding(top = 16.dp),
                                        bgRes = R.drawable.bg_buddy_msg,
                                        height = 170.dp,
                                        spacing = 12.dp,
                                        onClick = {
                                            C2CChatViewModel.appendMakeBuddyParams(uiEntry.link, targetUserInfo).tryToLink()
                                        },
                                    )
                                    Box(
                                        modifier =
                                            Modifier
                                                .size(88.dp, 48.dp)
                                                .align(Alignment.TopCenter),
                                    ) {
                                        AvatarNetworkImage(selfUser, modifier = Modifier.size(48.dp))
                                        AvatarNetworkImage(
                                            targetUserInfo,
                                            modifier =
                                                Modifier
                                                    .size(48.dp)
                                                    .align(Alignment.CenterEnd),
                                        )
                                    }
                                }
                            }
                        }

                        is VoiceCallEventEntry -> {
                            uiEntry.C2CContent()
                        }

                        is PrivateRoomEventEntry -> {
                            uiEntry.C2CContent()
                        }

                        is CronyMessageEntry -> {
                            uiEntry.C2CContent()
                        }

                        is CronyAcceptEntry -> {
                            uiEntry.C2CContent()
                        }

                        is TimeMsgEntry -> {
                            uiEntry.C2CContent()
                        }

                        else -> Spacer(Modifier)
                    }
                }

                if (isLoading) {
                    item(key = "isLoading", contentType = "isLoading") {
                        Box(
                            modifier = Modifier.fillMaxWidth(),
                            contentAlignment = Alignment.Center,
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                strokeWidth = 1.5.dp,
                            )
                        }
                    }
                }

                if (isEnd) {
                    item(key = "userCard", contentType = "userCard") {
                        UserCardItem(
                            targetUserInfo = targetUserInfo,
                            modifier =
                                Modifier
                                    .padding(horizontal = 16.dp)
                                    .fillMaxWidth()
                                    .clip(RoundedCornerShape(12.dp))
                                    .background(Color.White),
                            onCellClick = {
                                onAction.onNavigation(Route.UserProfile(targetUserInfo.toBasic()))
                            },
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun UserCardItem(
    targetUserInfo: UserProfileInfo,
    modifier: Modifier = Modifier,
    onCellClick: () -> Unit = {},
    onButtonClick: () -> Unit = { onCellClick() },
) {
    Row(
        modifier =
            modifier
                .clickable(onClick = onCellClick)
                .padding(horizontal = 12.dp)
                .padding(top = 8.dp, bottom = 12.dp),
    ) {
        Box(modifier = Modifier.size(48.dp)) {
            NetworkImage(
                targetUserInfo.avatar,
                modifier =
                    Modifier
                        .fillMaxSize()
                        .clip(CircleShape),
            )

            if (targetUserInfo.onlineStatus == 0) {
                Spacer(
                    modifier =
                        Modifier
                            .size(10.dp)
                            .clip(CircleShape)
                            .background(Color(0xFF18E046))
                            .border(1.dp, Color.White, CircleShape)
                            .align(Alignment.BottomEnd),
                )
            }
        }
        Column(
            modifier =
                Modifier
                    .animateContentSize()
                    .fillMaxWidth()
                    .padding(start = 8.dp),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Column(
                    modifier =
                        Modifier
                            .animateContentSize()
                            .weight(1f),
                    verticalArrangement = Arrangement.spacedBy(2.dp),
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            modifier = Modifier.weight(1f, false),
                            text = targetUserInfo.name,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                        )
                        Spacer(modifier = Modifier.width(2.dp))
                        GenderAgeTag(user = targetUserInfo)
                    }

                    val tags =
                        remember(targetUserInfo.regionLabel, targetUserInfo.height) {
                            listOf(
                                targetUserInfo.regionLabel,
                                targetUserInfo.formatHeight,
                            ).filter { it.isNotBlank() }
                                .joinToString(separator = " | ")
                        }
                    if (tags.isNotEmpty()) {
                        // 标签
                        Text(text = tags, fontSize = 12.sp, color = Color(0xFF4E5969))
                    }
                }
            }

            Spacer(modifier = Modifier.height(4.dp))
            // 简介
            Text(
                text = targetUserInfo.intro.ifEmpty { "TA很懒, 什么都没有留下".localized },
                fontSize = 12.sp,
                color = Color(0xFF86909C),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
            )

            val images =
                remember(targetUserInfo.albums) {
                    targetUserInfo.albums
                        ?.asReversed()
                        ?.take(4)
                }
            if (!images.isNullOrEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                ) {
                    images.forEach { item ->
                        NetworkImage(
                            item.mediaUrl,
                            modifier =
                                Modifier
                                    .size(60.dp)
                                    .clip(RoundedCornerShape(8.dp)),
                        )
                    }
                }
            }
        }
    }
}
