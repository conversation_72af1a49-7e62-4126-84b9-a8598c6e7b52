package com.buque.wakoo.ui.screens.chatgroup.tasks.models


import com.buque.wakoo.bean.RichItem
import com.buque.wakoo.bean.user.BasicUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class CpRelation(
    @SerialName("id")
    val id: Int = 0,
    @SerialName("user1")
    val user1: BasicUser,
    @SerialName("user2")
    val user2: BasicUser
)

@Serializable
data class TaskBonus(
    @SerialName("box_coin")
    val boxCoin: Int = 0,
    @SerialName("tribe_active_point")
    val tribeActivePoint: Int = 0
)

interface CPTaskCardInfo {
    val title: List<RichItem>

    //任务描述
    val taskTargetRichDesc: List<RichItem>

    //任务进度
    val taskProcessRichDesc: List<RichItem>

    //任务奖励
    val taskBonusRichDesc: List<RichItem>

    //人数|对数
    val count: Int

    val taskType: Int

    companion object {
        const val FIRST_MAKE_CP = 1 //, "首次组CP"
        const val FIRST_PUBLIC_CP = 2//, "首次官宣CP"
    }
}

@Serializable
data class TribeCPTaskInfo(
    @SerialName("make_cp_mission")
    val makeCpTask: MakeCpTask = MakeCpTask(),
    @SerialName("public_cp_mission")
    val publicCpTask: PublicCpTask = PublicCpTask()
) {
    @Serializable
    data class MakeCpTask(
        @SerialName("task_bonus")
        val taskBonus: TaskBonus = TaskBonus(),
        @SerialName("task_bonus_rich_desc")
        override val taskBonusRichDesc: List<RichItem> = listOf(),
        @SerialName("task_process")
        val taskProcess: Int = 0,
        @SerialName("task_process_rich_desc")
        override val taskProcessRichDesc: List<RichItem> = listOf(),
        @SerialName("task_target")
        val taskTarget: Int = 0,
        @SerialName("task_target_rich_desc")
        override val taskTargetRichDesc: List<RichItem> = listOf(),
        @SerialName("task_type")
        override val taskType: Int = 0,
        @SerialName("user_count")
        val userCount: Int = 0,
        @SerialName("users")
        val users: List<BasicUser> = listOf(),
        @SerialName("task_tribe_point_rich_desc")
        override val title: List<RichItem> = emptyList()
    ) : CPTaskCardInfo {

        override val count: Int = userCount

    }

    @Serializable
    data class PublicCpTask(
        @SerialName("task_tribe_point_rich_desc")
        override val title: List<RichItem> = emptyList(),
        @SerialName("cp_relation_count")
        val cpRelationCount: Int = 0,
        @SerialName("cps")
        val cpRelations: List<CpRelation> = listOf(),
        @SerialName("task_bonus")
        val taskBonus: TaskBonus = TaskBonus(),
        @SerialName("task_bonus_rich_desc")
        override val taskBonusRichDesc: List<RichItem> = listOf(),
        @SerialName("task_process")
        val taskProcess: Int = 0,
        @SerialName("task_process_rich_desc")
        override val taskProcessRichDesc: List<RichItem> = listOf(),
        @SerialName("task_target")
        val taskTarget: Int = 0,
        @SerialName("task_target_rich_desc")
        override val taskTargetRichDesc: List<RichItem> = listOf(),
        @SerialName("task_type")
        override val taskType: Int = 0
    ) : CPTaskCardInfo {
        override val count: Int = cpRelationCount
    }
}

@Serializable
data class UserResult(
    val users: List<BasicUser> = emptyList(),
    @SerialName("user_count")
    val userCount: Int = 0,
    @SerialName("cps")
    val cpRelations: List<CpRelation> = listOf(),
    @SerialName("cp_relation_count")
    val cpRelationCount: Int = 0,
    val count: Int = 0,
    @SerialName("has_more")
    val hasMore: Boolean = false
)