package com.buque.wakoo.ui.screens.crony

import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.viewmodel.BasicListPaginateViewModel

class CronyMemberListViewModel() : BasicListPaginateViewModel<Int, CronyItem>() {
    private val repo = GlobalRepository.cronyRepo
    private val userId: String
        get() = SelfUser?.id.orEmpty()

    override suspend fun loadData(
        pageKey: Int,
        pageSize: Int
    ): Result<List<CronyItem>> {
        return repo.getRelations(userId, pageKey)
    }

    override fun getFirstPageKey(dataKey: Any): Int {
        return 0
    }

    override fun getNextPageKey(
        cState: CState<List<CronyItem>>,
        dataKey: Any,
        pageKey: Int?
    ): Int {
        return cState.dataOrNull?.lastOrNull()?.id ?: -1
    }

    override fun getDistinctSelector(): (CronyItem) -> String {
        return {
            it.id.toString()
        }
    }

    suspend fun breakUp(id: Int) = repo.breakUp(id).onSuccess {
        val st = getListState()
        val l = st.dataOrNull ?: return@onSuccess
        l.retainAll { it.id != id }
    }
}