package com.buque.wakoo.ui.screens.chatgroup.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.icons.Fire
import com.buque.wakoo.ui.icons.Switch
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooSecondaryUnSelected
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.AdaptiveScrollableTabRow
import com.buque.wakoo.ui.widget.NoIndicationInteractionSource
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.adaptiveTabIndicatorOffset
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.StateListPaginateLayout
import com.buque.wakoo.viewmodel.BasicListPaginateViewModel
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import retrofit2.http.GET
import retrofit2.http.Query

/**
 * type 1:日榜｜2:周榜
 */
interface GroupContributeRankApi {

    @GET("api/xya/group/v1/group/contribution/fellow/list")
    suspend fun getRankList(@Query("type") type: Int, @Query("page") page: Int, @Query("is_pre") isPre: Boolean): ApiResponse<JsonObject>
}

data class UserContribute(val userResponse: UserResponse, val value: String)

class GroupContributeViewModel(val type: Int, val isPre: Boolean) : BasicListPaginateViewModel<Int, UserContribute>() {
    private val api = ApiClient.createuserApiService<GroupContributeRankApi>()
    private var currentPage = 1

    override suspend fun loadData(
        pageKey: Int,
        pageSize: Int
    ): Result<List<UserContribute>> {
        return executeApiCallExpectingData { api.getRankList(type, pageKey, isPre) }
            .map { obj ->
                obj["user_infos"]?.jsonArray?.map {
                    val userResponse = AppJson.decodeFromJsonElement<UserResponse>(it)
                    val value = it.jsonObject["contribution"]?.jsonPrimitive?.intOrNull ?: 0
                    UserContribute(userResponse, value.toString())
                }.orEmpty()
            }
    }

    override fun getFirstPageKey(dataKey: Any): Int {
        currentPage = 1
        return currentPage
    }

    override fun getNextPageKey(
        cState: CState<List<UserContribute>>,
        dataKey: Any,
        pageKey: Int?
    ): Int {
        return ++currentPage
    }

    override fun getDistinctSelector(): (UserContribute) -> String {
        return {
            it.userResponse.id
        }
    }
}

@Composable
fun GroupContributeRankScreen() {
    val tabs = remember { listOf("日榜".localized, "周榜".localized) }
    TitleScreenScaffold("群组贡献榜".localized, titleContainerColor = Color.White, containerColor = Color(0xFFF5F7F9)) { pd ->
        val pagerState = rememberPagerState(pageCount = { tabs.size })
        val selectedTabIndex = pagerState.currentPage
        val scope = rememberCoroutineScope()
        var isPre by remember { mutableStateOf(false) }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(pd)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(50.dp)
            ) {

                val shape = remember {
                    RoundedCornerShape(topEndPercent = 50, bottomEndPercent = 50)
                }
                Row(
                    modifier = Modifier
                        .background(Color(0xFFFF423D), shape)
                        .clip(shape)
                        .click(onClick = {
                            isPre = !isPre
                        })
                        .padding(8.dp, 4.dp)
                        .align(Alignment.CenterStart),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(if (isPre) "上周榜" else "今日榜".localized, color = Color.White, fontSize = 12.sp)
                    SizeWidth(4.dp)
                    Image(WakooIcons.Switch, contentDescription = "sw", modifier = Modifier.size(12.dp))
                }

                AdaptiveScrollableTabRow(
                    modifier = Modifier.align(Alignment.Center),
                    selectedTabIndex = selectedTabIndex,
                    tabSpacing = 15.dp,
                    indicator = { tabPositions ->
                        if (selectedTabIndex < tabPositions.size) {
                            Box(
                                modifier =
                                    Modifier
                                        .adaptiveTabIndicatorOffset(tabPositions[selectedTabIndex])
                                        .requiredWidth(12.dp)
                                        .height(3.dp)
                                        .background(
                                            WakooSecondarySelected,
                                            CircleShape,
                                        ),
                            )
                        }
                    },
                ) {
                    tabs.forEachIndexed { index, tab ->
                        Tab(
                            selected = selectedTabIndex == index,
                            selectedContentColor = WakooSecondarySelected,
                            unselectedContentColor = WakooSecondaryUnSelected,
                            onClick = {
                                scope.launch {
                                    pagerState.animateScrollToPage(index)
                                }
                            },
                            interactionSource = remember { NoIndicationInteractionSource() },
                            content = {
                                Box(
                                    modifier =
                                        Modifier
                                            .widthIn(min = 80.dp)
                                            .padding(top = 12.dp, bottom = 6.dp),
                                    contentAlignment = Alignment.Center,
                                ) {
                                    Text(tab, color = if (selectedTabIndex == index) WakooText else WakooGrayText)
                                }
                            },
                        )
                    }
                }
            }

            key(isPre) {
                HorizontalPager(pagerState) {
                    GroupRankList(if (it == 0) 1 else 2, isPre)
                }
            }

        }
    }
}

@Composable
fun GroupRankList(
    type: Int, isPre: Boolean, vm: GroupContributeViewModel = viewModel<GroupContributeViewModel>(key = "contribute-$isPre-$type", initializer = {
        GroupContributeViewModel(type, isPre)
    })
) {
    StateListPaginateLayout<Int, UserContribute, GroupContributeViewModel>(modifier = Modifier.fillMaxSize(), viewModel = vm) { state, data ->
        LazyColumn(modifier = Modifier.fillMaxSize(), verticalArrangement = Arrangement.spacedBy(20.dp)) {
            itemsIndexed(data) { index, item ->
                GroupRankItem(item, index)
            }
        }
    }
}

@Composable
private fun GroupRankItem(item: UserContribute, index: Int = 0) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(end = 16.dp), verticalAlignment = Alignment.CenterVertically
    ) {
        val color = when (index) {
            0 -> Color(0xFFFF423D)
            1 -> Color(0xFFFF7D00)
            2 -> Color(0xFFFFC700)
            else -> WakooGrayText
        }
        Text(
            "${index + 1}", modifier = Modifier.widthIn(min = 48.dp), textAlign = TextAlign.Center, color = color,
            fontSize = 16.sp,
            fontWeight = FontWeight.ExtraBold
        )
        val mod = when (index) {
            0, 1, 2 -> Modifier.border(1.dp, color, CircleShape)
            else -> Modifier
        }
        AvatarNetworkImage(item.userResponse.basicUser, modifier = mod, size = 56.dp)
        SizeWidth(8.dp)
        Text(item.userResponse.nickname, fontSize = 16.sp, color = WakooText, modifier = Modifier.weight(1f, true))
        Text(item.value, fontSize = 16.sp, color = WakooGrayText, modifier = Modifier)
        Image(WakooIcons.Fire, contentDescription = null)
    }
}