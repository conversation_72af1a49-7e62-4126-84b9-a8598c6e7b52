package com.buque.wakoo.ui.screens.chatgroup.member.task

import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import retrofit2.http.GET
import retrofit2.http.Query

@Serializable
data class CpRelation(
    @SerialName("id")
    val id: Int = 0,
    @SerialName("user1")
    val user1: UserResponse = UserResponse(),
    @SerialName("user2")
    val user2: UserResponse = UserResponse()
)

@Serializable
data class MemberResult(
    val users: List<UserResponse> = emptyList(),
    @SerialName("user_count")
    val userCount: Int = 0,
    @SerialName("cps")
    val cpRelations: List<CpRelation> = listOf(),
    @SerialName("cp_relation_count")
    val cpRelationCount: Int = 0,
    val count: Int = 0,
    @SerialName("has_more")
    val hasMore: Boolean = false
)

interface TaskMemberApi {

    @GET("api/xya/group/v1/group/cp_mission/users")
    suspend fun getMemberList(@Query("mission_type") type: Int, @Query("last_id") lastId: Int = 0): ApiResponse<MemberResult>
}