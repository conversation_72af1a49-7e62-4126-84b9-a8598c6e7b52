package com.buque.wakoo.ui.screens.liveroom.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.icons.GreenChecked
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.AppPullToRefreshBox
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.viewmodel.liveroom.LiveRoomViewModel

@Composable
fun RoomBackgroundSetScreen(
    viewModel: LiveRoomViewModel,
    modifier: Modifier = Modifier,
) {
    val cState = CState.Success(1)
    var selectedId by rememberSaveable {
        mutableStateOf("")
    }
    TitleScreenScaffold(
        title = "房间背景设置".localized,
    ) {
        AppPullToRefreshBox(
            isRefreshing = cState.isRefreshing,
            onRefresh = {
            },
            modifier =
                Modifier
                    .padding(it)
                    .fillMaxSize(),
        ) {
            CStateLayout(
                state = cState,
                emptyCheckProvider = { false },
                onRetry = {
                },
            ) { data ->
                Box(modifier = Modifier.fillMaxSize()) {
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(3),
                        modifier = Modifier.fillMaxSize(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalArrangement = Arrangement.spacedBy(16.dp),
                        contentPadding = PaddingValues(start = 16.dp, end = 16.dp, bottom = 112.dp),
                    ) {
                        items(10) {
                            Column {
                                val isSelected by remember {
                                    derivedStateOf {
                                        selectedId == "123"
                                    }
                                }

                                Box(
                                    modifier =
                                        modifier
                                            .fillMaxWidth()
                                            .aspectRatio(0.589f)
                                            .clip(RoundedCornerShape(8.dp))
                                            .clickable(onClick = {})
                                            .border(
                                                width = if (isSelected) 1.5.dp else 0.dp,
                                                color = if (isSelected) Color(0xFF66FE6B) else Color.Transparent,
                                                shape = RoundedCornerShape(8.dp),
                                            ),
                                    contentAlignment = Alignment.BottomEnd,
                                ) {
                                    NetworkImage(
                                        data = "",
                                        modifier = Modifier.fillMaxSize(),
                                    )
                                    if (isSelected) {
                                        Image(
                                            imageVector = WakooIcons.GreenChecked,
                                            contentDescription = null,
                                            modifier = Modifier.size(20.dp),
                                        )
                                    }
                                }
                                SizeHeight(8.dp)
                                Text(
                                    text = "123",
                                    style =
                                        MaterialTheme.typography.bodyMedium
                                            .copy(color = WakooText, fontWeight = FontWeight.Medium),
                                )
                            }
                        }
                    }

                    Box(
                        modifier =
                            Modifier
                                .align(Alignment.BottomCenter)
                                .fillMaxWidth()
                                .background(
                                    Brush.verticalGradient(
                                        listOf(
                                            Color(0x00FFFFFF),
                                            Color(0x99FFFFFF),
                                            Color(0xFFFFFFFF),
                                            Color(0xFFFFFFFF),
                                        ),
                                    ),
                                ).padding(horizontal = 28.dp, vertical = 26.dp),
                    ) {
                        GradientButton(
                            text = "确认修改".localized,
                            modifier = Modifier.fillMaxWidth(),
                            onClick = {
                            },
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewRoomBackgroundSetScreen() {
    WakooTheme {
        RoomBackgroundSetScreen(
            viewModel = viewModel<LiveRoomViewModel>(factory = LiveRoomViewModel.Factory(BasicRoomInfo.preview)),
        )
    }
}
