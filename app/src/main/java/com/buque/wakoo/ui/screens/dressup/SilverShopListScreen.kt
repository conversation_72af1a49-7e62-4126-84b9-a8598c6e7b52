package com.buque.wakoo.ui.screens.dressup

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.OnClick
import com.buque.wakoo.bean.ExchangeItem
import com.buque.wakoo.bean.SilverMallResult
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.safeIndexOf
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.manager.localizedFormatWithKey
import com.buque.wakoo.navigation.SilverMallTab
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.Minus
import com.buque.wakoo.ui.icons.Plus
import com.buque.wakoo.ui.icons.QuestionLine
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooSecondaryUnSelected
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AdaptiveScrollableTabRow
import com.buque.wakoo.ui.widget.AppPullToRefreshBox
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.AutoSizeText
import com.buque.wakoo.ui.widget.NoIndicationInteractionSource
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.adaptiveTabIndicatorOffset
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.ui.widget.state.StateComponent
import com.buque.wakoo.ui.widget.state.isRefreshing
import com.buque.wakoo.viewmodel.SilverShopViewModel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch

private val TABS: List<SilverMallTab> =
    listOf(
        SilverMallTab.Gift, // 关注
        SilverMallTab.Prop, // 粉丝
    )

@Composable
fun SilverShopListScreen(
    initialTab: SilverMallTab,
    modifier: Modifier = Modifier,
) {
    val viewModel = viewModel<SilverShopViewModel>()
    val loading = LocalLoadingManager.current
    SilverShopListScreen(
        initialTab = initialTab,
        cState = viewModel.cState,
        modifier = modifier,
        onRetry = {
            viewModel.requestData(false)
        },
        onRefresh = {
            viewModel.requestData(true)
        },
    ) { count, item ->
        coroutineScope {
            loading
                .showWithResult(this) {
                    viewModel.exchangeItem(item, count).await()
                }.await() ?: false
        }
    }
}

@Composable
private fun SilverShopListScreen(
    initialTab: SilverMallTab,
    cState: CState<SilverMallResult>,
    modifier: Modifier = Modifier,
    onRetry: () -> Unit = {},
    onRefresh: () -> Unit = {},
    onBuy: suspend (Int, ExchangeItem) -> Boolean = { _, _ -> true },
) {
    val scope = rememberCoroutineScope()

    val showHowGetSilverCurrency =
        rememberSaveable {
            mutableStateOf(false)
        }

    val pendingExchangeItem =
        rememberSaveable {
            mutableStateOf<ExchangeItem?>(null)
        }

    TitleScreenScaffold(
        title = "银币商城".localized,
        modifier = modifier,
        containerColor = Color(0xFFF5F7F9),
    ) {
        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(180.dp)
                    .background(
                        Brush.verticalGradient(
                            colors =
                                listOf(
                                    Color(0xFF92FCC1),
                                    Color(0x0092FCC1),
                                ),
                        ),
                    ),
        )

        val pagerState =
            rememberPagerState(
                initialPage = TABS.safeIndexOf(initialTab) { 0 },
            ) { TABS.size }
        val selectedTabIndex = pagerState.currentPage

        AppPullToRefreshBox(
            isRefreshing = cState.isRefreshing,
            onRefresh = onRefresh,
            modifier =
                Modifier
                    .padding(it)
                    .fillMaxSize(),
        ) {
            CStateLayout(
                state = cState,
                emptyCheckProvider = { false },
                onRetry = onRetry,
            ) { data ->
                Column {
                    SilverBalanceCard(
                        balance = data.silverBalance,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 20.dp),
                    ) {
                        showHowGetSilverCurrency.value = true
                    }

                    AdaptiveScrollableTabRow(
                        selectedTabIndex = selectedTabIndex,
                        modifier =
                            Modifier
                                .align(Alignment.CenterHorizontally)
                                .padding(vertical = 4.dp),
                        tabSpacing = 48.dp,
                        indicator = { tabPositions ->
                            if (selectedTabIndex < tabPositions.size) {
                                Box(
                                    modifier =
                                        Modifier
                                            .adaptiveTabIndicatorOffset(tabPositions[selectedTabIndex])
                                            .requiredWidth(12.dp)
                                            .height(3.dp)
                                            .background(
                                                color = WakooSecondarySelected,
                                                shape = CircleShape,
                                            ),
                                )
                            }
                        },
                    ) {
                        TABS.forEachIndexed { index, tab ->
                            Tab(
                                selected = selectedTabIndex == index,
                                selectedContentColor = WakooSecondarySelected,
                                unselectedContentColor = WakooSecondaryUnSelected,
                                onClick = { scope.launch { pagerState.animateScrollToPage(index) } },
                                interactionSource = remember { NoIndicationInteractionSource() },
                                content = {
                                    Box(
                                        modifier = Modifier.padding(bottom = 4.dp),
                                    ) {
                                        tab.TabContent(selectedTabIndex == index)
                                    }
                                },
                            )
                        }
                    }

                    HorizontalPager(
                        state = pagerState,
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(horizontal = 16.dp),
                    ) { page ->
                        when (TABS[page]) {
                            SilverMallTab.Gift -> {
                                if (data.giftInfos.isEmpty()) {
                                    Box(
                                        modifier =
                                            Modifier
                                                .fillMaxSize()
                                                .verticalScroll(rememberScrollState()),
                                        contentAlignment = Alignment.Center,
                                    ) {
                                        StateComponent.Empty()
                                    }
                                } else {
                                    LazyVerticalGrid(
                                        columns = GridCells.Fixed(3),
                                        modifier = Modifier.fillMaxSize(),
                                        contentPadding = PaddingValues(vertical = 12.dp),
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        verticalArrangement = Arrangement.spacedBy(8.dp),
                                    ) {
                                        items(data.giftInfos) { item ->
                                            PropItem(item) {
                                                pendingExchangeItem.value = item
                                            }
                                        }
                                    }
                                }
                            }

                            SilverMallTab.Prop -> {
                                if (data.propsList.isEmpty()) {
                                    Box(
                                        modifier =
                                            Modifier
                                                .fillMaxSize()
                                                .verticalScroll(rememberScrollState()),
                                        contentAlignment = Alignment.Center,
                                    ) {
                                        StateComponent.Empty()
                                    }
                                } else {
                                    LazyVerticalGrid(
                                        columns = GridCells.Fixed(3),
                                        modifier = Modifier.fillMaxSize(),
                                        contentPadding = PaddingValues(vertical = 12.dp),
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        verticalArrangement = Arrangement.spacedBy(8.dp),
                                    ) {
                                        items(data.propsList) { item ->
                                            PropItem(item) {
                                                pendingExchangeItem.value = item
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    if (showHowGetSilverCurrency.value) {
        Dialog(onDismissRequest = {
            showHowGetSilverCurrency.value = false
        }) {
            HowGetSilverCurrencyDialogContent {
                showHowGetSilverCurrency.value = false
            }
        }
    }

    val pendingItem = pendingExchangeItem.value
    if (pendingItem != null) {
        Dialog(onDismissRequest = {
            pendingExchangeItem.value = null
        }) {
            ExchangeItemsDialogContent(
                icon = pendingItem.icon,
                name = pendingItem.name,
                price = pendingItem.price,
            ) {
                scope.launch {
                    if (onBuy(it, pendingItem)) {
                        pendingExchangeItem.value = null
                    }
                }
            }
        }
    }
}

@Composable
private fun SilverBalanceCard(
    balance: Long,
    modifier: Modifier = Modifier,
    onShowHowGetSilver: OnClick,
) {
    Row(
        modifier =
            modifier
                .fillMaxWidth()
                .height(120.dp)
                .clip(RoundedCornerShape(24.dp))
                .background(
                    Brush.verticalGradient(
                        colors =
                            listOf(
                                Color(0xFFEFEFEF),
                                Color(0xFFCCCCCC),
                            ),
                    ),
                ).padding(horizontal = 24.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // 余额信息
        Column(
            modifier = Modifier.weight(1f),
        ) {
            Text(
                text = "我的银币余额".localized,
                style =
                    MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.Medium,
                    ),
                color = Color(0xFF666666),
            )

            SizeHeight(6.dp)

            Text(
                text = balance.toString(),
                style =
                    MaterialTheme.typography.titleLarge.copy(
                        fontFamily = FontFamily.MI_SANS,
                    ),
                color = Color(0xFF666666),
            )

            SizeHeight(6.dp)

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.noEffectClick(onClick = onShowHowGetSilver),
            ) {
                Text(
                    text = "了解如何获得银币".localized,
                    style = MaterialTheme.typography.labelLarge,
                    color = WakooGrayText,
                )

                Icon(
                    imageVector = WakooIcons.QuestionLine,
                    contentDescription = null,
                    tint = WakooGrayText,
                    modifier = Modifier.size(16.dp),
                )
            }
        }

        // 大钻石图标
        Image(
            painter = painterResource(id = R.drawable.ic_large_silver_currency),
            contentDescription = null,
            modifier = Modifier.size(72.dp),
        )
    }
}

@Composable
private fun PropItem(
    item: ExchangeItem,
    onClick: OnClick,
) {
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .background(WakooWhite, RoundedCornerShape(12.dp))
                .padding(vertical = 8.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        NetworkImage(
            data = item.icon,
            modifier =
                Modifier
                    .fillMaxWidth(0.72f)
                    .aspectRatio(1f),
            contentScale = ContentScale.Fit,
            alignment = Alignment.Center,
        )

        AutoSizeText(
            text = item.name,
            modifier = Modifier.padding(horizontal = 5.dp),
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.bodyMedium,
            color = WakooText,
        )

        if (item.days > 0) {
            AutoSizeText(
                text = "%s天".localizedFormat(item.days),
                modifier = Modifier.padding(horizontal = 5.dp),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.labelLarge,
                color = WakooGrayText,
            )
        }

        Row(
            modifier =
                Modifier
                    .fillMaxWidth(0.72f)
                    .height(24.dp)
                    .clip(CircleShape)
                    .background(
                        Brush.horizontalGradient(
                            colors =
                                listOf(
                                    Color(0xFFA3FF2C),
                                    Color(0xFF31FFA1),
                                ),
                        ),
                    ).clickable(onClick = onClick),
            horizontalArrangement = Arrangement.spacedBy(2.dp, Alignment.CenterHorizontally),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                painter = painterResource(R.drawable.ic_silver_currency),
                contentDescription = null,
                modifier = Modifier.size(12.dp),
            )
            Text(
                text = item.price.toString(),
                style = MaterialTheme.typography.labelLarge,
                color = WakooText,
            )
        }
    }
}

@Composable
private fun HowGetSilverCurrencyDialogContent(onClick: OnClick) {
    Column(
        modifier =
            Modifier
                .padding(horizontal = 15.dp)
                .fillMaxWidth()
                .background(WakooWhite, RoundedCornerShape(8.dp))
                .padding(vertical = 20.dp, horizontal = 22.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Image(
            painter = painterResource(R.drawable.ic_silver_lucky_bag),
            contentDescription = null,
            modifier = Modifier.size(100.dp),
            contentScale = ContentScale.Fit,
            alignment = Alignment.Center,
        )

        SizeHeight(20.dp)

        Text(
            text = "赠送银色福袋礼物可获得银币奖励".localized,
            style = MaterialTheme.typography.bodyLarge,
            color = WakooGrayText,
        )

        SizeHeight(20.dp)

        SolidButton(
            text = "我知道了".localized,
            fontSize = 16.sp,
            paddingValues = PaddingValues(horizontal = 28.dp),
            onClick = onClick,
        )
    }
}

@Composable
private fun ExchangeItemsDialogContent(
    icon: String,
    name: String,
    price: Int,
    onBuy: (Int) -> Unit,
) {
    var countValue by rememberSaveable(stateSaver = TextFieldValue.Saver) {
        mutableStateOf(
            TextFieldValue(
                text = "1",
                selection = TextRange("1".length), // 光标初始在末尾
            ),
        )
    }
    val count by remember {
        derivedStateOf {
            countValue.text.toIntOrNull()?.takeIf { it > 0 } ?: 1
        }
    }

    Column(
        modifier =
            Modifier
                .padding(horizontal = 15.dp)
                .fillMaxWidth()
                .background(WakooWhite, RoundedCornerShape(8.dp))
                .padding(vertical = 20.dp, horizontal = 22.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(12.dp),
    ) {
        NetworkImage(
            data = icon,
            modifier = Modifier.size(100.dp),
            contentScale = ContentScale.Fit,
            alignment = Alignment.Center,
        )

        Text(
            text = name,
            style = MaterialTheme.typography.bodyLarge,
            color = WakooText,
        )

        Row(verticalAlignment = Alignment.CenterVertically) {
            Box(
                modifier =
                    Modifier
                        .size(20.dp)
                        .clip(CircleShape)
                        .noEffectClick {
                            if (count > 1) {
                                val newCountText = count.minus(1).toString()
                                countValue = countValue.copy(text = newCountText)
                            }
                        }.background(Color(0xFFE9EAEF)),
                contentAlignment = Alignment.Center,
            ) {
                Icon(
                    imageVector = WakooIcons.Minus,
                    contentDescription = null,
                    modifier = Modifier.size(14.dp),
                    tint = WakooText,
                )
            }

            Box(
                modifier =
                    Modifier
                        .padding(horizontal = 8.dp)
                        .size(100.dp, 28.dp)
                        .background(Color(0xFFE9EAEF), RoundedCornerShape(4.dp)),
                contentAlignment = Alignment.Center,
            ) {
                AppTextField(
                    value = countValue,
                    onValueChange = { newCountValue ->
                        val newCountText = newCountValue.text
                        if (newCountText.isEmpty()) {
                            countValue = TextFieldValue(text = "")
                            return@AppTextField
                        }
                        if (newCountText.length > 4) {
                            return@AppTextField
                        }
                        val digitCountText =
                            newCountText
                                .filter { it.isDigit() }
                                .toIntOrNull()
                                ?.takeIf { it > 0 }
                                ?.toString() ?: "1"
                        countValue =
                            TextFieldValue(
                                text = digitCountText,
                                selection = TextRange(digitCountText.length),
                            )
                    },
                    textStyle = MaterialTheme.typography.bodyLarge.copy(color = WakooText, textAlign = TextAlign.Center),
                    keyboardOptions =
                        KeyboardOptions(
                            keyboardType = KeyboardType.Number,
                            imeAction = ImeAction.Next,
                        ),
                    keyboardActions =
                        KeyboardActions(
                            onNext = {
                                onBuy(count)
                            },
                        ),
                    singleLine = true,
                    placeholder = {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center,
                        ) {
                            Text(
                                text = "1",
                                style =
                                    MaterialTheme.typography.bodyLarge.copy(color = WakooGrayText, textAlign = TextAlign.Center),
                            )
                        }
                    },
                    contentPadding = PaddingValues.Zero,
                    colors =
                        TextFieldDefaults.colors(
                            focusedContainerColor = Color.Transparent,
                            unfocusedContainerColor = Color.Transparent,
                            disabledContainerColor = Color.Transparent,
                            focusedIndicatorColor = Color.Transparent,
                            unfocusedIndicatorColor = Color.Transparent,
                        ),
                )
            }

            Box(
                modifier =
                    Modifier
                        .size(20.dp)
                        .clip(CircleShape)
                        .noEffectClick {
                            if (count < 9999) {
                                val newCountText = count.plus(1).toString()
                                countValue = countValue.copy(text = newCountText)
                            }
                        }.background(Color(0xFFE9EAEF)),
                contentAlignment = Alignment.Center,
            ) {
                Icon(
                    imageVector = WakooIcons.Plus,
                    contentDescription = null,
                    modifier = Modifier.size(14.dp),
                    tint = WakooText,
                )
            }
        }
        val totalPrice by remember {
            derivedStateOf {
                price.toLong().times(count)
            }
        }
        Text(
            text = "总计需要消耗银币：%s\n兑换成功后将发放至礼物背包".localizedFormatWithKey("总计需要消耗银币", totalPrice),
            style = MaterialTheme.typography.bodySmall,
            color = WakooGrayText,
            textAlign = TextAlign.Center,
        )

        SizeHeight(8.dp)

        SolidButton(
            text = "确认兑换".localized,
            fontSize = 16.sp,
            paddingValues = PaddingValues(horizontal = 28.dp),
            onClick = {
                onBuy(count)
            },
        )
    }
}

@Preview
@Composable
private fun PreviewSilverShopListScreen() {
    SilverShopListScreen(SilverMallTab.Prop, CState.Success(SilverMallResult()))
}

@Preview
@Composable
private fun PreviewHowGetSilverCurrencyDialogContent() {
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(vertical = 30.dp),
    ) {
        Dialog({}) {
            HowGetSilverCurrencyDialogContent {}
        }
    }
}

@Preview
@Composable
private fun PreviewExchangeItemsDialogContent() {
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(vertical = 30.dp),
    ) {
        Dialog({}) {
            ExchangeItemsDialogContent("", "基督教", 100) {}
        }
    }
}
