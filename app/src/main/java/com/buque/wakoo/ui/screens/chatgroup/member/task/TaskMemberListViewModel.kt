package com.buque.wakoo.ui.screens.chatgroup.member.task

import androidx.compose.runtime.IntState
import androidx.compose.runtime.mutableIntStateOf
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.viewmodel.BasicListPaginateViewModel

class TaskMemberListViewModel() : BasicListPaginateViewModel<Int, UserResponse>() {
    private val api = ApiClient.createuserApiService<TaskMemberApi>()
    private val stateCount = mutableIntStateOf(0)
    val stateOfCount: IntState = stateCount
    override suspend fun loadData(
        pageKey: Int,
        pageSize: Int
    ): Result<List<UserResponse>> = executeApiCallExpectingData {
        api.getMemberList(1, pageKey)
    }.map {
        stateCount.intValue = it.count
        it.users
    }

    override fun getFirstPageKey(dataKey: Any): Int {
        return 0
    }

    override fun getNextPageKey(
        cState: CState<List<UserResponse>>,
        dataKey: Any,
        pageKey: Int?
    ): Int {
        return cState.dataOrNull?.lastOrNull()?.id?.toIntOrNull() ?: -1
    }

    override fun getDistinctSelector(): (UserResponse) -> String {
        return {
            it.id
        }
    }

}

class TaskCPListViewModel() : BasicListPaginateViewModel<Int, CpRelation>() {
    private val api = ApiClient.createuserApiService<TaskMemberApi>()
    private val stateCount = mutableIntStateOf(0)
    val stateOfCount: IntState = stateCount
    override suspend fun loadData(
        pageKey: Int,
        pageSize: Int
    ): Result<List<CpRelation>> = executeApiCallExpectingData {
        api.getMemberList(2, pageKey)
    }.map {
        stateCount.intValue = it.count
        it.cpRelations
    }

    override fun getFirstPageKey(dataKey: Any): Int {
        return 0
    }

    override fun getNextPageKey(
        cState: CState<List<CpRelation>>,
        dataKey: Any,
        pageKey: Int?
    ): Int {
        return cState.dataOrNull?.lastOrNull()?.id ?: -1
    }

    override fun getDistinctSelector(): (CpRelation) -> String {
        return {
            it.id.toString()
        }
    }

}