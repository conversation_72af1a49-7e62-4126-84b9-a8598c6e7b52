package com.buque.wakoo.ui.widget

import android.os.SystemClock
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import kotlinx.coroutines.NonCancellable.isActive
import kotlinx.coroutines.delay

@Composable
inline fun CallTimer(
    isCountdown: Boolean,
    elapsedRealtime: Long,
    onTimer: @Composable (Long) -> Unit,
) {
    val calculate = {
        if (isCountdown) {
            elapsedRealtime.minus(SystemClock.elapsedRealtime())
        } else {
            SystemClock.elapsedRealtime().minus(elapsedRealtime)
        }.coerceAtLeast(0)
    }

    var time by remember(isCountdown, elapsedRealtime) {
        mutableLongStateOf(calculate())
    }
    LaunchedEffect(key1 = isCountdown, key2 = elapsedRealtime) {
        while (isActive) {
            if (isCountdown && time < 800) {
                delay(time)
                time = 0
                break
            } else {
                delay(1000)
            }
            time = calculate()
        }
    }
    onTimer(time)
}
