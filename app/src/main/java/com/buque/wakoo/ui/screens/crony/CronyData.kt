package com.buque.wakoo.ui.screens.crony


import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CronyMessage(
    val inviter: UserResponse,
    val invitee: UserResponse,
    @SerialName("invite_id")
    val id: Int = 0,
    val tag: CronyTag,
    @SerialName("expire_timestamp")
    val expireTimeStamp: Int = 0,
)


@Serializable
data class CronyInfo(
    @SerialName("intimacy_value")
    val intimacyValue: String = "",
    @SerialName("level")
    val level: String = ""
)

interface ICronyItem

object LoadingCronyItem: ICronyItem

object EmptyCronyItem : ICronyItem

@Serializable
data class CronyItem(
    val id: Int,
    val tag: CronyTag,
    @SerialName("relative_info")
    val user: UserResponse,
    @SerialName("intimacy_info")
    val info: CronyInfo
) : ICronyItem