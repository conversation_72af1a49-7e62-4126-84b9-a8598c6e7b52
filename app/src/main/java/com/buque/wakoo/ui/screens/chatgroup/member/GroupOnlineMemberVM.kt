package com.buque.wakoo.ui.screens.chatgroup.member

import androidx.compose.runtime.IntState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.viewmodel.BasicListPaginateViewModel
import retrofit2.http.GET
import retrofit2.http.Query

interface OnlineMemberApi {
    companion object {
        const val NONE = 0//, "无"
        const val ORDER_BY_WEEK_ACTIVE_POINT_DESC = 1//, "周活跃度降序"
        const val ORDER_BY_WEEK_ACTIVE_POINT_ASC = 2//, "周活跃度升序"
        const val ORDER_BY_TOTAL_ACTIVE_POINT_DESC = 3//, "总活跃度降序"
        const val ORDER_BY_TOTAL_ACTIVE_POINT_ASC = 4//, "总活跃度升序"
    }

    @GET("api/xya/group/v2/fellow/list")
    suspend fun getOnlineMemberList(
        @retrofit2.http.Query("sort_type") sortType: Int,
        @Query("last_fellow_id") lastId: Int = 0
    ): ApiResponse<OnlineMemberContainer>
}


class GroupOnlineMemberVM : BasicListPaginateViewModel<Int, OnlineMember>() {

    private val api = ApiClient.createuserApiService<OnlineMemberApi>()

    private val stSortType = mutableIntStateOf(0)
    val stateSortType: IntState = stSortType

    private val stMemberCount = mutableStateOf("0/0")
    val memberCountState: State<String> = stMemberCount


    private val sortType: Int
        get() = stSortType.intValue

    fun setSortType(sortType: Int) = run {
        stSortType.intValue = sortType
    }

    override suspend fun loadData(
        pageKey: Int,
        pageSize: Int
    ): Result<List<OnlineMember>> = executeApiCallExpectingData {
        api.getOnlineMemberList(sortType, pageKey)
    }.onSuccess {
        stMemberCount.value = "${it.onlineCount}/${it.totalCount}"
    }.map {
        it.fellows
    }

    override fun getFirstPageKey(dataKey: Any): Int {
        return 0
    }

    override fun getNextPageKey(
        cState: CState<List<OnlineMember>>,
        dataKey: Any,
        pageKey: Int?
    ): Int {
        return cState.dataOrNull?.lastOrNull()?.memberId ?: -1
    }

    override fun getDistinctSelector(): (OnlineMember) -> String {
        return {
            it.memberId.toString()
        }
    }
}