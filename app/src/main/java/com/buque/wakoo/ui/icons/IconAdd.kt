package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.IconAdd: ImageVector
    get() {
        if (_IconAdd != null) {
            return _IconAdd!!
        }
        _IconAdd =
            ImageVector
                .Builder(
                    name = "IconAdd",
                    defaultWidth = 20.dp,
                    defaultHeight = 20.dp,
                    viewportWidth = 20f,
                    viewportHeight = 20f,
                ).apply {
//            path(fill = SolidColor(Color(0xFFE9EAEF))) {
//                moveTo(10f, 10f)
//                moveToRelative(-10f, 0f)
//                arcToRelative(10f, 10f, 0f, isMoreThanHalf = true, isPositiveArc = true, 20f, 0f)
//                arcToRelative(10f, 10f, 0f, isMoreThanHalf = true, isPositiveArc = true, -20f, 0f)
//            }
                    path(fill = SolidColor(Color(0xFF111111))) {
                        moveTo(10f, 5f)
                        curveTo(10.233f, 5f, 10.349f, 5f, 10.441f, 5.038f)
                        curveTo(10.564f, 5.089f, 10.661f, 5.186f, 10.712f, 5.309f)
                        curveTo(10.75f, 5.4f, 10.75f, 5.517f, 10.75f, 5.75f)
                        verticalLineTo(9.25f)
                        horizontalLineTo(14.25f)
                        curveTo(14.483f, 9.25f, 14.599f, 9.25f, 14.691f, 9.288f)
                        curveTo(14.814f, 9.339f, 14.911f, 9.436f, 14.962f, 9.559f)
                        curveTo(15f, 9.65f, 15f, 9.767f, 15f, 10f)
                        curveTo(15f, 10.233f, 15f, 10.349f, 14.962f, 10.441f)
                        curveTo(14.911f, 10.564f, 14.814f, 10.661f, 14.691f, 10.712f)
                        curveTo(14.599f, 10.75f, 14.483f, 10.75f, 14.25f, 10.75f)
                        horizontalLineTo(10.75f)
                        verticalLineTo(14.25f)
                        curveTo(10.75f, 14.483f, 10.75f, 14.599f, 10.712f, 14.691f)
                        curveTo(10.661f, 14.814f, 10.564f, 14.911f, 10.441f, 14.962f)
                        curveTo(10.349f, 15f, 10.233f, 15f, 10f, 15f)
                        curveTo(9.767f, 15f, 9.65f, 15f, 9.559f, 14.962f)
                        curveTo(9.436f, 14.911f, 9.339f, 14.814f, 9.288f, 14.691f)
                        curveTo(9.25f, 14.599f, 9.25f, 14.483f, 9.25f, 14.25f)
                        verticalLineTo(10.75f)
                        horizontalLineTo(5.75f)
                        curveTo(5.517f, 10.75f, 5.4f, 10.75f, 5.309f, 10.712f)
                        curveTo(5.186f, 10.661f, 5.089f, 10.564f, 5.038f, 10.441f)
                        curveTo(5f, 10.349f, 5f, 10.233f, 5f, 10f)
                        curveTo(5f, 9.767f, 5f, 9.65f, 5.038f, 9.559f)
                        curveTo(5.089f, 9.436f, 5.186f, 9.339f, 5.309f, 9.288f)
                        curveTo(5.4f, 9.25f, 5.517f, 9.25f, 5.75f, 9.25f)
                        horizontalLineTo(9.25f)
                        verticalLineTo(5.75f)
                        curveTo(9.25f, 5.517f, 9.25f, 5.4f, 9.288f, 5.309f)
                        curveTo(9.339f, 5.186f, 9.436f, 5.089f, 9.559f, 5.038f)
                        curveTo(9.65f, 5f, 9.767f, 5f, 10f, 5f)
                        close()
                    }
                }.build()

        return _IconAdd!!
    }

@Suppress("ObjectPropertyName")
private var _IconAdd: ImageVector? = null
