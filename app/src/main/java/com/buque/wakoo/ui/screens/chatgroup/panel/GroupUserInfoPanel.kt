package com.buque.wakoo.ui.screens.chatgroup.panel

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.emptyAction
import com.buque.wakoo.bean.IconLabel
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.icons.AddRelations
import com.buque.wakoo.ui.icons.At
import com.buque.wakoo.ui.icons.ChatC2c
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.messages.chat.TargetUserCPCard
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.ExpLevelWidget
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.VipCrownTag
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.viewmodel.UserProfileViewModel

// @TA
private const val MENU_TYPE_AT_USER = 1

// 私信
private const val MENU_TYPE_C2C_MESSAGE = 2

// 关注
private const val MENU_TYPE_FOLLOWED = 3

// 更多
private const val MENU_TYPE_MORE = 4

@Composable
fun DialogScope.ChatGroupUserInfoPanel(
    targetUser: User,
    modifier: Modifier = Modifier,
    vm: UserProfileViewModel =
        viewModel<UserProfileViewModel>(
            key = "profile-${targetUser.id}",
            initializer = { UserProfileViewModel(targetUser) },
        ),
    onSendGift: OnAction = {},
    onAtClick: OnAction = {},
    onChat: OnAction = emptyAction,
) {
    val rootNavController = LocalAppNavController.root

    val state by vm.userProfileInfoFlow.collectAsStateWithLifecycle()
    val user = state.user
    val uiConfig by AppConfigManager.uiConfigFlow.collectAsStateWithLifecycle()

    Box(
        modifier = modifier.fillMaxWidth(),
    ) {
        Spacer(
            modifier =
                Modifier
                    .padding(top = 28.dp)
                    .matchParentSize()
                    .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                    .background(WakooWhite)
                    .background(
                        brush =
                            Brush.verticalGradient(
                                0f to Color(0xFFD6FFD7),
                                0.3f to Color(0x00D6FFD7),
                            ),
                    ),
        )
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
            ) {
                Box(modifier = Modifier.align(Alignment.Center).padding(bottom = 4.dp)) {
                    if (state.isTopClassUser) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                        ) {
                            Image(
                                painter = painterResource(R.drawable.ic_user_panel_topclass_l),
                                contentDescription = null,
                                modifier = Modifier.weight(1f),
                                contentScale = ContentScale.FillWidth,
                            )
                            Spacer(modifier = Modifier.size(70.dp))
                            Image(
                                painter = painterResource(R.drawable.ic_user_panel_topclass_r),
                                contentDescription = null,
                                modifier = Modifier.weight(1f),
                                contentScale = ContentScale.FillWidth,
                            )
                        }
                    }
                    AvatarNetworkImage(
                        user = state,
                        size = 80.dp,
                        modifier =
                            Modifier
                                .align(Alignment.Center),
                        border = BorderStroke(1.5.dp, WakooWhite),
                    )
                    if (!uiConfig.partnerHasEscaped && state.cpRelationInfo?.publicCp != null &&
                        !LocalSelfUserProvider.isJP
                    ) {
                        NetworkImage(
                            data = state.cpRelationInfo?.publicCpMedalNormalUrl,
                            modifier =
                                Modifier
                                    .align(Alignment.BottomCenter)
                                    .size(64.dp, 20.dp),
                        )
                    }
                }

                if (!user.isSelf) {
                    Text(
                        text = "举报".localized,
                        style = MaterialTheme.typography.labelLarge,
                        color = WakooSecondarySelected,
                        modifier =
                            Modifier
                                .align(Alignment.CenterStart)
                                .padding(top = 10.dp)
                                .clickable {
                                    rootNavController.push(Route.Report(1, user.id))
                                },
                    )
                }

                Text(
                    text = "查看主页".localized,
                    style = MaterialTheme.typography.labelLarge,
                    color = WakooSecondarySelected,
                    modifier =
                        Modifier
                            .align(Alignment.CenterEnd)
                            .padding(top = 10.dp)
                            .clickable {
                                dismiss()
                                rootNavController.push(Route.UserProfile(user.toBasic()))
                            },
                )
            }

            SizeHeight(16.dp)

            Row(
                modifier = Modifier.padding(horizontal = 20.dp),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = user.name,
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color(0xFF111111),
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    textAlign = TextAlign.Center,
                )

                SizeWidth(4.dp)

                GenderAgeTag(user = user)
            }

            SizeHeight(8.dp)
            FlowRow(
                modifier = Modifier.padding(horizontal = 20.dp),
                horizontalArrangement = Arrangement.spacedBy(2.dp, Alignment.CenterHorizontally),
                verticalArrangement = Arrangement.spacedBy(2.dp, Alignment.CenterVertically),
                itemVerticalAlignment = Alignment.CenterVertically,
            ) {
                if (user.isVip) {
                    VipCrownTag()
                }
                ExpLevelWidget(state)
            }
            if (user.isSelf) {
                GradientButton(
                    text = "查看我的主页".localized,
                    onClick = {
                        rootNavController.push(Route.UserProfile(user.toBasic()))
                    },
                    modifier = Modifier.padding(bottom = 20.dp),
                    fontSize = 14.sp,
                    height = 40.dp,
                )
                return
            }

            if (!uiConfig.partnerHasEscaped && state.cpRelationInfo?.publicCp != null) {
                TargetUserCPCard(user, state.cpRelationInfo!!, modifier = Modifier.padding(16.dp))
            } else {
                SizeHeight(30.dp)
            }

            GradientButton(
                text = "送礼物".localized,
                onClick = onSendGift,
                minWidth = 215.dp,
                height = 40.dp,
            )

            SizeHeight(15.dp)

            HorizontalDivider(thickness = 0.5.dp, color = Color(0xFFE5E5E5))

            val buttonItems by remember(state) {
                derivedStateOf {
                    buildList {
                        add(IconLabel(MENU_TYPE_AT_USER, WakooIcons.At, "TA"))
                        if (state.isFollowed) {
                            add(IconLabel(MENU_TYPE_FOLLOWED, WakooIcons.AddRelations, "已关注".localized, null, true))
                        } else {
                            add(IconLabel(MENU_TYPE_FOLLOWED, WakooIcons.AddRelations, "关注".localized))
                        }
                        add(IconLabel(MENU_TYPE_C2C_MESSAGE, WakooIcons.ChatC2c, "私信".localized))
                    }
                }
            }

            val onClick = { item: IconLabel<*> ->
                when (item.id) {
                    MENU_TYPE_AT_USER -> {
                        onAtClick()
                        dismiss()
                    }

                    MENU_TYPE_FOLLOWED -> {
                        vm.toggleFollowState()
                    }

                    MENU_TYPE_C2C_MESSAGE -> {
                        onChat()
                        dismiss()
                    }

                    MENU_TYPE_MORE -> {
                        dismiss()
                    }
                }
            }

            Row(
                modifier =
                    Modifier
                        .padding(horizontal = 8.dp)
                        .height(IntrinsicSize.Min),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                buttonItems.forEachIndexed { index: Int, item: IconLabel<ImageVector> ->
                    if (index > 0) {
                        VerticalDivider(
                            thickness = 0.5.dp,
                            color = Color(0xFFE5E5E5),
                            modifier = Modifier.fillMaxHeight(0.6f),
                        )
                    }
                    Row(
                        modifier =
                            Modifier
                                .weight(1f)
                                .noEffectClick(onClick = {
                                    if (item.iconVisible) {
                                        onClick(item)
                                    }
                                })
                                .padding(vertical = 16.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center,
                    ) {
                        if (item.iconVisible) {
                            Icon(
                                imageVector = item.icon,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp),
                                tint = item.iconTint ?: WakooSecondarySelected,
                            )
                            SizeWidth(5.dp)
                        }
                        Text(
                            text = item.label,
                            style = MaterialTheme.typography.labelLarge,
                            color = if (item.iconVisible) WakooSecondarySelected else WakooGrayText,
                            fontWeight = FontWeight.Medium,
                        )
                    }
                }
            }

            SizeHeight(25.dp)
        }
    }
}
