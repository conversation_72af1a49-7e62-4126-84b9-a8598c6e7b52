package com.buque.wakoo.ui.screens.chatgroup.tasks.models


import com.buque.wakoo.bean.RichItem
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 部落活跃任务列表
 * ```
 * class TribeMemberTaskTypeEnum(DescribedEnum):
 *     CHECK_IN = 1, "签到"
 *     GIVE_GIFT = 2, "送礼"
 *     SEND_MESSAGE = 3, "发言"
 *     FIRST_GIVE_GIFT = 4, "首次送礼"
 *     SEND_MESSAGE_WITH_CP = 5, "与 CP一起发言"
 *
 * class TribeCpTaskEnum(DescribedEnum):
 *     FIRST_MAKE_CP = 1, "首次组CP"
 *     FIRST_PUBLIC_CP = 2, "首次官宣CP"
 * ```
 */
@Serializable
data class ActiveTaskInfo(
    @SerialName("id")
    val id: Int = 0,
    @SerialName("name")
    val name: String = "",
    @SerialName("missions")
    val tasks: List<Task> = listOf(),
    @SerialName("double_return_hint")
    val awardHint: String = "",
    @SerialName("double_return_left_duration")
    val leftDuration: Int = 0,
) {
    @Serializable
    data class Task(
        @SerialName("task_bonus_rich_desc")
        val taskBonusRichDesc: List<RichItem> = listOf(),
        @SerialName("task_btn_label")
        val taskBtnLabel: String = "",
        @SerialName("task_id")
        val taskId: Int = 0,
        @SerialName("task_is_collected")
        val taskIsCollected: Boolean = false,
        @SerialName("task_is_finished")
        val taskIsFinished: Boolean = false,
        @SerialName("task_name")
        val taskName: String = "",
        @SerialName("task_icon")
        val taskIcon:String="",
        @SerialName("task_type")
        val taskType: Int = 0
    ) {
        companion object {
            const val CHECK_IN = 1
            const val GIVE_GIFT = 2
            const val SEND_MESSAGE = 3
            const val FIRST_GIVE_GIFT = 4
            const val SEND_MESSAGE_WITH_CP = 5
        }
    }
}