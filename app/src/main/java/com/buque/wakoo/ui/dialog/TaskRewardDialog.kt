package com.buque.wakoo.ui.dialog

import android.R.attr.onClick
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.bean.AudioRoomAudienceBean
import com.buque.wakoo.ext.conditional
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.qyqy.cupid.data.AudioRoomAudiencePopup
import kotlin.String

/**
 * 任务奖励弹窗
 *
 * 目前只有两种在这个文件里
 */

/**
 * 运营者任务奖励弹窗
 * @param title 标题
 * @param icon 图标
 * @param content 文字内容
 * @param buttonText 按钮文字
 * @param onButtonClick 点击事件
 */
@Composable
fun OperationRewardDialog(
    title: String?,
    icon: String,
    content: String?,
    buttonText: String,
    onButtonClick: () -> Unit = {},
) {
    Surface(
        modifier =
            Modifier
                .width(270.dp)
                .wrapContentHeight(),
        shape = RoundedCornerShape(8.dp),
        color = Color.White,
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            if (icon.isNotBlank()) {
                NetworkImage(icon, modifier = Modifier.size(96.dp), contentScale = ContentScale.Inside)
                SizeHeight(6.dp)
            }
            // 标题（可选）
            if (title != null) {
                Text(
                    text = title,
                    style =
                        MaterialTheme.typography.titleMedium.copy(
                            fontSize = 17.sp,
                            lineHeight = 24.sp,
                        ),
                    color = Color(0xFF111111),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth(),
                )

                SizeHeight(4.dp)
            }

            // 内容
            if (content != null) {
                Text(
                    text = content,
                    style =
                        MaterialTheme.typography.bodyMedium.copy(
                            fontSize = 15.sp,
                            lineHeight = 22.sp,
                        ),
                    color = Color(0xFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth(),
                )
            }

            SizeHeight(20.dp)

            GradientButton(
                buttonText,
                modifier =
                    Modifier
                        .padding(vertical = 32.dp)
                        .fillMaxWidth()
                        .heightIn(32.dp),
                textColor = WakooText,
                onClick = onButtonClick,
            )
        }
    }
}

@Composable
fun AudioRoomAudienceRewardDialog(
    bean: AudioRoomAudiencePopup,
    modifier: Modifier = Modifier,
    onButtonClick: (AudioRoomAudiencePopup.Button) -> Unit = {},
) {
    Box(
        modifier =
            Modifier
                .background(
                    color = Color.White,
                ).clip(RoundedCornerShape(34.dp)),
    ) {
        Image(
            painter = painterResource(R.drawable.ic_audience_reward_bg_lt),
            contentDescription = null,
            modifier = Modifier.align(Alignment.TopStart),
        )
        Image(
            painter = painterResource(R.drawable.ic_audience_reward_bg_br),
            contentDescription = null,
            modifier = Modifier.align(Alignment.BottomEnd),
        )
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(top = 40.dp, start = 16.dp, end = 16.dp, bottom = 20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                bean.title,
                style =
                    TextStyle(
                        fontSize = 18.sp,
                        lineHeight = 20.sp,
                        fontFamily = FontFamily.MI_SANS,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF111111),
                        textAlign = TextAlign.Center,
                    ),
            )
            Row(
                modifier =
                    Modifier
                        .padding(vertical = 24.dp)
                        .fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(24.dp, Alignment.CenterHorizontally),
            ) {
                bean.bonus.forEach { bonu ->
                    Column(
                        modifier =
                            Modifier
                                .border(width = 4.dp, color = Color(0xFFCFFFEC), shape = RoundedCornerShape(size = 8.dp))
                                .size(80.dp)
                                .background(color = Color(0xFFFFFFFF), shape = RoundedCornerShape(size = 8.dp)),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        NetworkImage(
                            bonu.icon,
                            modifier =
                                Modifier
                                    .weight(1f)
                                    .aspectRatio(1f),
                            contentScale = ContentScale.FillHeight,
                        )
                        if (bonu.text.isNotBlank()) {
                            Text(
                                bonu.text,
                                modifier = Modifier.padding(top = 4.dp),
                                color = Color(0xFF999999),
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium,
                                lineHeight = 18.sp,
                            )
                        }
                    }
                }
            }
            Text(
                text = bean.desc,
                style =
                    TextStyle(
                        fontSize = 13.sp,
                        lineHeight = 21.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF666666),
                        textAlign = TextAlign.Center,
                    ),
            )
            Row(
                modifier =
                    Modifier
                        .padding(top = 24.dp)
                        .fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
            ) {
                bean.buttons.forEach { button ->
                    GradientButton(
                        text = button.label,
                        gradientColors =
                            if (button.t == 0) {
                                listOf(
                                    WakooText,
                                    WakooText,
                                )
                            } else {
                                listOf(
                                    Color(0xFFA3FF2C),
                                    Color(0xFF31FFA1),
                                )
                            },
                        modifier =
                            Modifier
                                .padding(horizontal = 10.dp)
                                .then(
                                    if (bean.buttons.size > 1) {
                                        Modifier.weight(1f)
                                    } else {
                                        Modifier.fillMaxWidth(0.64f)
                                    },
                                ).height(36.dp),
                        textColor =
                            if (button.t == 0) {
                                Color(0xFFA3FF2C)
                            } else {
                                WakooText
                            },
                        onClick = {
                            onButtonClick(button)
                        },
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun DailyTaskWidgetPreview() {
    AudioRoomAudienceRewardDialog(
        AudioRoomAudiencePopup(
            title = "这个是标题",
            bonus =
                listOf(
                    AudioRoomAudiencePopup.Bonu(
                        icon = "",
                        text = "xxx",
                    ),
                ),
            desc = "这是测试的内容这是测试的内容这是测试的内容这是测试的内容这是测试的内容",
            buttons =
                listOf(
                    AudioRoomAudiencePopup.Button(
                        t = 0,
                        label = "わかった",
                        actionLink = "ucoo://xxx",
                    ),
                    AudioRoomAudiencePopup.Button(
                        t = 1,
                        label = "わかった",
                        actionLink = "ucoo://xxx",
                    ),
                ),
        ),
    ) {
    }
}
