package com.buque.wakoo.ui.floating

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation3.ui.LocalNavAnimatedContentScope
import com.buque.wakoo.R
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.bean.ConversationConfig
import com.buque.wakoo.bean.CoupleInfo
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.icons.Close
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AutoSizeText
import com.buque.wakoo.ui.widget.drag.FloatingLayoutScope
import com.buque.wakoo.ui.widget.drag.rememberDraggableFloatingState
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import kotlinx.coroutines.flow.collectLatest

@Composable
fun FloatingLayoutScope.BuddyFloatingItem(
    targetUser: User,
    c2cConfig: State<ConversationConfig>,
    coupleInfo: State<CoupleInfo?>,
    onToCp: OnAction,
) {
    val buddyFloatingState =
        rememberDraggableFloatingState(
            initialAlignment = Alignment.BottomEnd,
            initialOffsetDp = DpOffset(x = 0.dp, y = (-84).dp),
            initialIsVisible = false,
            initialIsStickyToEdge = true,
            allowDragOutOfBounds = true,
        )

    val selfUser = LocalSelfUserProvider.current

    var visible by rememberSaveable {
        mutableStateOf(true)
    }

    LaunchedEffect(buddyFloatingState, selfUser.isJP) {
        snapshotFlow {
            visible &&
                if (selfUser.isJP) {
                    coupleInfo.value?.visible == true
                } else {
                    val privateSpace = c2cConfig.value.privateSpace
                    privateSpace != null && privateSpace.type in 1..3
                }
        }.collectLatest {
            buddyFloatingState.isVisible = it
        }
    }

    // nav过渡结束后再显示悬浮框会更流畅，位置也不会出错
    if (LocalNavAnimatedContentScope.current.transition.isRunning) {
        return
    }

    DraggableItem(state = buddyFloatingState) {
        val selfUser = LocalSelfUserProvider.current
        if (selfUser.isJP) {
            BuddyFloatingContentForJp(
                content = if (coupleInfo.value?.isCp == true) "情侣小屋".localized else "和TA成为情侣".localized,
                leftUser = selfUser,
                rightUser = targetUser,
            ) {
                val privateRoomId = coupleInfo.value?.privateRoomId
                if (coupleInfo.value?.isCp == true && privateRoomId != null) {
                    LiveRoomManager.joinRoom(roomId = privateRoomId.toString(), isPrivateRoom = true)
                } else {
                    onToCp()
                }
            }
        } else {
            BuddyFloatingContent(
                leftUser = selfUser,
                rightUser = targetUser,
                isCp = c2cConfig.value.privateSpace?.type == 3,
                onClose = {
                    visible = false
                },
            ) {
                c2cConfig.value.privateSpace?.id?.also {
                    LiveRoomManager.joinRoom(roomId = it, isPrivateRoom = true)
                }
            }
        }
    }
}

@Composable
fun BuddyFloatingContent(
    leftUser: User,
    rightUser: User,
    isCp: Boolean,
    modifier: Modifier = Modifier,
    onClose: OnAction = {},
    onClick: OnAction = {},
) {
    Box(
        modifier =
            modifier
                .size(80.dp, 103.dp)
                .noEffectClick(onClick = onClick),
    ) {
        Image(
            painter = painterResource(if (isCp) R.drawable.bg_cp_room_float else R.drawable.bg_buddy_room_float),
            contentDescription = null,
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.FillWidth,
        )
        Row(
            horizontalArrangement = Arrangement.spacedBy((-8).dp),
            modifier =
                Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = 40.dp),
        ) {
            AvatarNetworkImage(
                user = leftUser,
                size = 36.dp,
                enabled = false,
                border = BorderStroke(1.dp, WakooWhite),
            )

            AvatarNetworkImage(
                user = rightUser,
                size = 36.dp,
                enabled = false,
                border = BorderStroke(1.dp, WakooWhite),
            )
        }
        Box(
            modifier =
                Modifier
                    .align(Alignment.TopEnd)
                    .noEffectClick(onClick = onClose)
                    .padding(start = 5.dp, bottom = 5.dp)
                    .size(16.dp)
                    .background(Color(0x99000000), CircleShape),
            contentAlignment = Alignment.Center,
        ) {
            Icon(
                imageVector = WakooIcons.Close,
                contentDescription = null,
                modifier = Modifier.size(8.dp),
                tint = WakooWhite,
            )
        }
        Box(
            modifier =
                Modifier
                    .align(Alignment.BottomCenter)
                    .size(64.dp, 20.dp)
                    .paint(
                        painter = painterResource(R.drawable.bg_btn_join),
                        contentScale = ContentScale.FillBounds,
                    ).padding(horizontal = 3.dp),
        ) {
            BasicText(
                text = "点击进入".localized,
                style = TextStyle(color = WakooWhite, fontSize = 10.sp),
                modifier = Modifier.align(Alignment.Center),
                autoSize = TextAutoSize.StepBased(minFontSize = 8.sp, maxFontSize = 10.sp),
                maxLines = 1,
            )
        }
    }
}

@Preview
@Composable
private fun PreviewBuddyFloatingContent() {
    WakooTheme {
        BuddyFloatingContent(
            BasicUser.sampleBoy,
            BasicUser.sampleBoy,
            true,
        )
    }
}

@Composable
fun BuddyFloatingContentForJp(
    content: String,
    leftUser: User,
    rightUser: User,
    modifier: Modifier = Modifier,
    onClick: OnAction = {},
) {
    Column(
        modifier =
            modifier
                .size(80.dp, 90.dp)
                .paint(painterResource(id = R.drawable.bg_cp_room_float_for_jp))
                .noEffectClick(onClick = onClick),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Box(modifier = Modifier.padding(top = 10.dp)) {
            Row(horizontalArrangement = Arrangement.spacedBy((-8).dp)) {
                AvatarNetworkImage(
                    user = leftUser,
                    size = 36.dp,
                    enabled = false,
                    border = BorderStroke(1.dp, WakooWhite),
                )

                AvatarNetworkImage(
                    user = rightUser,
                    size = 36.dp,
                    enabled = false,
                    border = BorderStroke(1.dp, WakooWhite),
                )
            }

            Image(
                painter = painterResource(id = R.drawable.ic_cp_heart_for_jp),
                contentDescription = null,
                modifier =
                    Modifier
                        .align(Alignment.BottomCenter)
                        .size(24.dp),
            )
        }

        Spacer(modifier = Modifier.weight(1f))

        AutoSizeText(
            text = content,
            modifier =
                Modifier
                    .padding(horizontal = 4.dp)
                    .heightIn(max = 28.dp),
            fontSize = 14.sp,
            color = Color(0xFFB40020),
            maxLines = 2,
            textAlign = TextAlign.Center,
        )

        Spacer(modifier = Modifier.weight(2f))
    }
}

@Preview
@Composable
private fun PreviewBuddyFloatingContentForJp() {
    WakooTheme {
        BuddyFloatingContentForJp(
            "哈哈哈",
            BasicUser.sampleBoy,
            BasicUser.sampleBoy,
        )
    }
}
