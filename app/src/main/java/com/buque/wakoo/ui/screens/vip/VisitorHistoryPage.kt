package com.buque.wakoo.ui.screens.vip

import android.R.attr.radius
import android.R.attr.rotationY
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Shapes
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.paint
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil3.request.transformations
import com.buque.wakoo.R
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.ext.click
import com.buque.wakoo.im_business.UIMessageUtils
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.easyPostBottomPanel
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.ui.icons.Location
import com.buque.wakoo.ui.icons.LocationHollow
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.image.BlurTransformation
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.VisitorViewModel

@Composable
fun VisitorHistoryPage(
    modifier: Modifier = Modifier,
    toNavigate: (AppNavKey) -> Unit = {},
) {
    val listState = rememberLazyGridState()
    val viewModel = viewModel<VisitorViewModel>()

    val dc = rememberDialogController(false, "visitor-history-page")

    SegColorTitleScreenScaffold("访客记录".localized) {
        CStateListPaginateLayout<Any, Int, UserResponse, VisitorViewModel>(
            "",
            emptyText = "暂无访客记录".localized,
            viewModel = viewModel,
            listState = listState,
            emptyId = R.drawable.ic_visitor_empty,
            modifier =
                Modifier
                    .background(color = Color.White)
                    .padding(it)
                    .fillMaxSize(),
        ) { paginateState, list ->
            LazyVerticalGrid(
                columns = GridCells.Fixed(2),
                modifier =
                    Modifier
                        .fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp),
            ) {
                items(list) {
                    VisitorItem(
                        it.avatarUrl,
                        it.lastVisitTs ?: 0L,
                        it.locationLabel,
                        it.onlineStatus == 1,
                        !viewModel.isVip,
                    ) {
                        if (viewModel.isVip) {
                            toNavigate(Route.UserProfile(BasicUser.fromResponse(it)))
                        } else {
                            dc.easyPostBottomPanel {
                                JoinDialogContent {
                                    dismiss()
                                    toNavigate(Route.Member)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    dc.RenderDialogs(Unit)
}

@Preview
@Composable
private fun JoinDialogContent(
    memberCount: Int = 999,
    onClick: () -> Unit = {},
) {
    val d = LocalDensity.current
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .background(Color(0xFF1C1D1E), RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .background(
                        brush =
                            Brush.verticalGradient(
                                listOf(Color(0xffFFEBD4), Color(0xffFFD99E)),
                            ),
                        RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp),
                    ).padding(horizontal = 36.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            Column {
                Text(
                    "开通wakoo会员".localized,
                    fontSize = 16.sp,
                    lineHeight = 20.sp,
                    color = Color(0xFFB57514),
                )
                SizeHeight(12.dp)
                Text(
                    "享受专属多种权益".localized,
                    fontSize = 16.sp,
                    lineHeight = 20.sp,
                    color = Color(0xFFB57514),
                )
            }
            Image(
                painter = painterResource(R.drawable.ic_member_crown),
                contentDescription = null,
                modifier = Modifier.size(72.dp),
                contentScale = ContentScale.FillWidth,
            )
        }
        SizeHeight(48.dp)
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(text = "对方很主动，不要错过机会哦".localized, color = Color(0xFFFFE9CE), fontSize = 16.sp)
            Image(
                painter = painterResource(id = R.drawable.ic_heart),
                contentDescription = "",
                modifier =
                    Modifier
                        .size(16.dp)
                        .graphicsLayer {
                            rotationY = 30f
                        },
            )
        }
        Spacer(modifier = Modifier.height(44.dp))
        GradientButton(
            "开通会员,查看谁喜欢我".localized,
            textColor = Color(0xFF593A0C),
            onClick = onClick,
            gradientColors = listOf(Color(0xFFFFf799), Color(0xFFffe072)),
            modifier =
                Modifier
                    .widthIn(min = 260.dp, max = 345.dp)
                    .heightIn(44.dp),
        )
        Spacer(modifier = Modifier.height(12.dp))
    }
}

@Composable
private fun VisitorItem(
    avatar: String,
    time: Long,
    location: String,
    isOnline: Boolean,
    blur: Boolean,
    onClick: () -> Unit = {},
) {
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .aspectRatio(165f / 220)
                .clip(RoundedCornerShape(8.dp))
                .click(onClick = onClick),
    ) {
        val density = LocalDensity.current

        NetworkImage(
            avatar,
            modifier = Modifier.fillMaxSize(),
            applyBuilder = {
                if (blur) {
                    transformations(BlurTransformation(12))
                }
            },
        )
        val colorGreen = Color(0xFF18E046)

        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(8.dp),
        ) {
            // online
            if (isOnline) {
                Row(
                    modifier =
                        Modifier
                            .background(Color(0x80000000), RoundedCornerShape(12.dp))
                            .padding(horizontal = 6.dp)
                            .heightIn(min = 18.dp)
                            .align(Alignment.End),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Box(
                        modifier =
                            Modifier
                                .size(6.dp)
                                .background(colorGreen, CircleShape),
                    )
                    Spacer(modifier = Modifier.width(2.dp))
                    Text(text = "在线".localized, fontSize = 10.sp, color = colorGreen)
                }
            }
            Spacer(modifier = Modifier.weight(1f))
            val style =
                with(LocalDensity.current) {
                    remember {
                        TextStyle(shadow = Shadow(Color.Gray, offset = Offset(1.dp.toPx(), 1.dp.toPx()), blurRadius = 2.dp.toPx()))
                    }
                }
            // location
            AnimatedVisibility(location.isNotEmpty()) {
                Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                    Icon(
                        imageVector = WakooIcons.Location,
                        contentDescription = "location",
                        modifier = Modifier.size(12.dp),
                        tint = Color.White,
                    )
                    Spacer(modifier = Modifier.width(1.5.dp))
                    Text(
                        text = location,
                        fontSize = 14.sp,
                        color = Color.White,
                        modifier = Modifier.weight(1f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        style = style,
                    )
                }
                Spacer(modifier = Modifier.height(8.dp))
            }
            // time
            Text(
                text = UIMessageUtils.getMessageTimeFormatText(time * 1000),
                fontSize = 12.sp,
                color = Color.White,
                style = style,
            )
        }
    }
}
