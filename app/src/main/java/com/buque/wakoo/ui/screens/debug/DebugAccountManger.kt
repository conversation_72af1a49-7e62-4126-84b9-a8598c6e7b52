package com.buque.wakoo.ui.screens.debug

import android.content.ClipData
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Done
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalClipboard
import androidx.compose.ui.platform.toClipEntry
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.DevicesKV
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.ui.theme.WakooLightGrayBg
import com.buque.wakoo.ui.theme.WakooRed
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SpaceListItemScaffold
import com.buque.wakoo.utils.eventBus.EventBus
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

@Serializable
private data class Item(
    val account: String,
    val ps: String,
)

@Composable
fun DebugAccountMangerDialog(onDismiss: (Boolean) -> Unit) {
    val list =
        remember {
            mutableStateListOf<Item>().apply {
                addAll(
                    AppJson.decodeFromString<List<Item>>(
                        DevicesKV
                            .decodeString(
                                "_debug_account_list",
                                "[]",
                            ).orEmpty(),
                    ),
                )
            }
        }
    Dialog(
        onDismissRequest = {
            onDismiss(false)
        },
        properties =
            DialogProperties(
                usePlatformDefaultWidth = false,
            ),
    ) {
        Card(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 5.dp, vertical = 10.dp),
            shape = RoundedCornerShape(16.dp),
            colors =
                CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface,
                ),
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .padding(12.dp),
            ) {
                // 标题栏
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Text(
                        text = "账号管理",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                    )

                    IconButton(onClick = {
                        onDismiss(false)
                    }) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                        )
                    }
                }

                SizeHeight(6.dp)

                Text(text = "新增账号", style = MaterialTheme.typography.labelLarge)

                SizeHeight(2.dp)

                Row(verticalAlignment = Alignment.CenterVertically) {
                    val input =
                        remember {
                            mutableStateOf("")
                        }

                    val ps =
                        remember {
                            mutableStateOf("")
                        }

                    Column(modifier = Modifier.weight(1f)) {
                        AppTextField(
                            input.value,
                            {
                                input.value = it
                            },
                            placeholder = "手机号",
                            backgroundColor = WakooLightGrayBg,
                            contentPadding = PaddingValues(5.dp),
                            singleLine = true,
                            minHeight = 30.dp,
                        )

                        SizeHeight(2.dp)

                        AppTextField(
                            ps.value,
                            {
                                ps.value = it
                            },
                            placeholder = "备注",
                            backgroundColor = WakooLightGrayBg,
                            contentPadding = PaddingValues(5.dp),
                            singleLine = true,
                            minHeight = 30.dp,
                        )
                    }

                    IconButton(onClick = {
                        if (input.value.length != 11) {
                            showToast("手机号必须是11位")
                            return@IconButton
                        }
                        if (list.any { it.account == input.value }) {
                            showToast("账号已存在")
                            return@IconButton
                        }
                        list.add(0, Item(input.value, ps.value))
                        DevicesKV.putString("_debug_account_list", AppJson.encodeToString(list.toList()))
                        input.value = ""
                        ps.value = ""
                        showToast("已保存")
                    }) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "新增",
                        )
                    }
                }

                SizeHeight(8.dp)

                Text(text = "自动注册号前缀和后缀（自动随机中间数字补齐11位）", style = MaterialTheme.typography.labelLarge)

                SizeHeight(2.dp)

                Row(verticalAlignment = Alignment.CenterVertically) {
                    var selectedOptionMale by remember { mutableStateOf(true) }

                    val prefix =
                        remember(selectedOptionMale) {
                            mutableStateOf(
                                DevicesKV
                                    .decodeString(
                                        "_debug_account_prefix_mask_$selectedOptionMale",
                                        "000",
                                    ).orEmpty(),
                            )
                        }

                    val suffix =
                        remember(selectedOptionMale) {
                            mutableStateOf(
                                DevicesKV
                                    .decodeString(
                                        "_debug_account_suffix_mask_$selectedOptionMale",
                                        "",
                                    ).orEmpty(),
                            )
                        }
                    Column(modifier = Modifier.weight(1f)) {
                        AppTextField(
                            prefix.value,
                            {
                                prefix.value = it
                            },
                            backgroundColor = WakooLightGrayBg,
                            contentPadding = PaddingValues(5.dp),
                            placeholder = "注册号段前缀，默认是000开头",
                            singleLine = true,
                            minHeight = 30.dp,
                        )

                        SizeHeight(2.dp)

                        AppTextField(
                            suffix.value,
                            {
                                suffix.value = it
                            },
                            backgroundColor = WakooLightGrayBg,
                            contentPadding = PaddingValues(5.dp),
                            placeholder = "注册号段后缀，默认是空结尾",
                            singleLine = true,
                            minHeight = 30.dp,
                        )
                    }

                    Column(horizontalAlignment = Alignment.End) {
                        var expanded by remember { mutableStateOf(false) }

                        // 作为下拉菜单的触发器
                        TextButton(
                            onClick = { expanded = true },
                            colors = ButtonDefaults.textButtonColors(contentColor = WakooText),
                        ) {
                            Text(
                                text = if (selectedOptionMale) "男号配置" else "女号配置",
                                style = MaterialTheme.typography.labelLarge,
                            )
                            Icon(Icons.Default.ArrowDropDown, contentDescription = null)

                            DropdownMenu(
                                expanded = expanded,
                                onDismissRequest = { expanded = false },
                            ) {
                                repeat(2) {
                                    DropdownMenuItem(
                                        text = { Text(if (it == 0) "男号配置" else "女号配置") },
                                        onClick = {
                                            selectedOptionMale = it == 0
                                            expanded = false
                                        },
                                    )
                                }
                            }
                        }

                        IconButton(onClick = {
                            if (prefix.value.length > 4 || suffix.value.length > 4) {
                                showToast("前缀或后缀长度不能大于4")
                                return@IconButton
                            }
                            DevicesKV.putString("_debug_account_prefix_mask_$selectedOptionMale", prefix.value)
                            DevicesKV.putString("_debug_account_suffix_mask_$selectedOptionMale", suffix.value)
                            showToast("已保存")
                        }) {
                            Icon(
                                imageVector = Icons.Default.Done,
                                contentDescription = "保存",
                            )
                        }
                    }
                }

                SizeHeight(8.dp)

                Text(text = "账号列表", style = MaterialTheme.typography.labelLarge)

                SizeHeight(2.dp)

                val current =
                    remember {
                        DevicesKV.decodeString("_debug_current_phone").orEmpty()
                    }

                val clipboardManager = LocalClipboard.current
                val scope = rememberCoroutineScope()

                LazyColumn(modifier = Modifier.weight(1f)) {
                    items(list) {
                        SpaceListItemScaffold(
                            startContent = {
                                Column(
                                    modifier =
                                        Modifier.noEffectClick {
                                            scope.launch {
                                                clipboardManager.setClipEntry(
                                                    ClipData.newPlainText("plain text", it.account).toClipEntry(),
                                                )
                                                showToast("已复制")
                                            }
                                        },
                                ) {
                                    Text(
                                        if (AccountManager.isLoggedIn && it.account == current) {
                                            "${it.account}(当前账号)"
                                        } else {
                                            it.account
                                        },
                                        style = MaterialTheme.typography.bodyMedium,
                                    )
                                    Text(it.ps, style = MaterialTheme.typography.labelMedium)
                                }
                            },
                            centerContent = {},
                            endContent = {
                                TextButton(onClick = {
                                    list.remove(it)
                                    DevicesKV.putString("_debug_account_list", AppJson.encodeToString(list.toList()))
                                }, colors = ButtonDefaults.textButtonColors(contentColor = WakooRed)) {
                                    Text(text = "删除", style = MaterialTheme.typography.labelLarge)
                                }

                                TextButton(enabled = !AccountManager.isLoggedIn || it.account != current, onClick = {
                                    appCoroutineScope.launch {
                                        onDismiss(true)
                                        AccountManager.logout()
                                        delay(500)
                                        EventBus.trySend(
                                            DebugLogin(it.account),
                                        )
                                    }
                                }, colors = ButtonDefaults.textButtonColors(contentColor = WakooText)) {
                                    Text(text = "切换", style = MaterialTheme.typography.labelLarge)
                                }
                            },
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 关闭按钮
                Button(
                    onClick = {
                        onDismiss(false)
                    },
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    Text("关闭")
                }
            }
        }
    }
}
