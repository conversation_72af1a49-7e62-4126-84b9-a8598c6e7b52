package com.buque.wakoo.ui.screens.chatgroup.chat

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.bean.message.GroupNotice
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.im_business.interf.IC2CAction
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.MessageStatus
import com.buque.wakoo.im_business.message.UIMessageEntry
import com.buque.wakoo.im_business.message.types.UCTextMessage
import com.buque.wakoo.im_business.message.ui.entry.ChatGroupMsgEntry
import com.buque.wakoo.im_business.tim.TIMMessage
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.VipCrownTag
import com.buque.wakoo.ui.widget.appendUserLevel
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.richtext.RichText
import com.buque.wakoo.ui.widget.richtext.RichTextScope
import com.buque.wakoo.utils.color
import com.buque.wakoo.utils.convertHtmlToAnnotatedString
import com.tencent.imsdk.v2.V2TIMMessage

data class ChatGroupRoles(
    val ownerUid: String,
    val adminUidList: List<String>,
)

@Composable
fun ChatGroupOwner(modifier: Modifier = Modifier) {
    Text(
        text = "群主".localized,
        color = Color.White,
        fontSize = 10.sp,
        lineHeight = 18.sp,
        textAlign = TextAlign.Center,
        modifier =
            modifier
                .widthIn(min = 40.dp)
                .background(Color(0xFFFF385C), RoundedCornerShape(50)),
    )
}

@Composable
fun ChatGroupAdmin(modifier: Modifier = Modifier) {
    Text(
        text = "管理员".localized,
        color = Color.White,
        fontSize = 10.sp,
        lineHeight = 18.sp,
        textAlign = TextAlign.Center,
        modifier =
            modifier
                .widthIn(min = 40.dp)
                .background(Color(0xFF15ABFF), RoundedCornerShape(50)),
    )
}

@Composable
fun ChatGroupItemWrapper(
    entry: UIMessageEntry,
    onAction: IIMAction,
    roleState: State<ChatGroupRoles>,
    onAvatarClick: OnAction = {},
    content: @Composable () -> Unit = {},
) {
    val user = entry.user
    val isJP = LocalSelfUserProvider.isJP

    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
    ) {
        Row(modifier = Modifier.fillMaxWidth()) {
            AvatarNetworkImage(user, modifier = Modifier.size(40.dp), onClick = {
                onAvatarClick.invoke()
            })
            SizeWidth(8.dp)
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp),
                verticalArrangement = Arrangement.SpaceBetween,
            ) {
                val roles by roleState
                RichText(
                    fontSize = 12.sp,
                    color = Color(0xFF111111),
                    lineHeight = 26.sp,
                ) {
                    append(user.name)
                    if (roles.ownerUid == user.id) {
                        append("  ")
                        InlineSizedContent(40.dp, 18.dp) {
                            ChatGroupOwner()
                        }
                    }
                    if (roles.adminUidList.contains(user.id)) {
                        append("  ")
                        InlineSizedContent(40.dp, 18.dp) {
                            ChatGroupAdmin()
                        }
                    }

                    if (user.isVip) {
                        append("  ")
                        InlineSizedContent(48.dp, 18.dp) {
                            VipCrownTag()
                        }
                    }

                    append(" ")
                    appendUserLevel(isCN = !isJP, user = user)

                    user.medalList?.forEach {
                        append(" ")
                        InlineSizedContent(it.width.dp, it.height.dp) {
                            NetworkImage(
                                data = it.icon,
                                modifier = Modifier.size(it.width.dp, it.height.dp),
                            )
                        }
                    }
                }

                Row(modifier = Modifier.padding(top = 8.dp), verticalAlignment = Alignment.Bottom) {
                    content()
                    MessageStatus(
                        message = entry.message,
                        showReadStatus = false,
                        onAction = onAction,
                        modifier =
                            Modifier
                                .align(Alignment.Bottom)
                                .padding(start = 4.dp, bottom = 4.dp),
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun ChatGroupItemWrapperPreview() {
    val st =
        remember {
            mutableStateOf(ChatGroupRoles("1", listOf("2")))
        }
    WakooTheme {
        Column {
            val msg = V2TIMMessage()

            val density = LocalDensity.current
            val ucInstanceMessage = UCTextMessage(TIMMessage(V2TIMMessage()), "")
            val entry = UIMessageEntry(ucInstanceMessage, ChatGroupMsgEntry.from(ucInstanceMessage, density))
            val onAction =
                object : IC2CAction {
                    override fun onShowGiftPanel() {
                    }
                }
            ChatGroupItemWrapper(entry, onAction, st)
            SizeHeight(10.dp)
            ChatGroupItemWrapper(entry, onAction, st)
        }
    }
}

@Composable
fun ChatGroupSystemTip(
    modifier: Modifier = Modifier,
    avatar: String? = null,
    content: RichTextScope.() -> Unit = {},
) {
    Box(
        modifier =
            modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp),
        contentAlignment = Alignment.Center,
    ) {
        Row(
            modifier =
                Modifier
                    .background(Color(0xFFEBEEF4), RoundedCornerShape(8.dp))
                    .padding(8.dp, 6.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            if (avatar != null) {
                NetworkImage(
                    avatar,
                    modifier =
                        Modifier
                            .size(28.dp)
                            .clip(CircleShape),
                )
                SizeWidth(8.dp)
            }
            RichText(fontSize = 12.sp, lineHeight = 18.sp, color = WakooGrayText, content = content)
        }
    }
}

@Preview
@Composable
private fun SystemTipPReview() {
    ChatGroupSystemTip {
        withBuilder {
            append("恭喜新人")
            pushStyle(SpanStyle(fontWeight = FontWeight.Bold, color = Color(0xFF15ABFF)))
            append("hahaha")
            pop()
            append("加入群组")
        }
    }
}

@Composable
fun GroupNoticeCard(
    groupNotice: GroupNotice,
    onAction: OnAction = {},
) {
    val string =
        remember(groupNotice.notice) {
            val allTexts = groupNotice.notice.joinToString(separator = "") { it.text }
            convertHtmlToAnnotatedString(allTexts)
        }
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(horizontal = 36.dp),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .background(Color.White, RoundedCornerShape(12.dp))
                    .border(0.5.dp, Color(0xFFF0F0F0), RoundedCornerShape(12.dp))
                    .padding(horizontal = 12.dp),
        ) {
            Text(string, lineHeight = 20.sp, modifier = Modifier.padding(vertical = 12.dp))
            if (groupNotice.actionText.isNotEmpty()) {
                HorizontalDivider(thickness = 0.5.dp, color = Color(0xFFE5E5E5))
                Row(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .clickable(onClick = onAction)
                            .padding(vertical = 12.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Text(
                        groupNotice.actionText,
                        textAlign = TextAlign.Center,
                    )
                    Icon(Icons.AutoMirrored.Filled.KeyboardArrowRight, "", tint = WakooSecondarySelected)
                }
            }
        }
    }
}

@Composable
fun ChatGroupGiftContent(
    icon: String,
    messageReceiver: AnnotatedString,
    messageGift: AnnotatedString,
    senderIsMe: Boolean,
    isMultiReceivers: Boolean,
) {
    val shape = RoundedCornerShape(topEnd = 12.dp, bottomStart = 12.dp, bottomEnd = 12.dp)
    Row(
        modifier =
            Modifier
                .width(245.dp)
                .background(if (senderIsMe) WakooGreen else Color.White, shape)
                .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        NetworkImage(icon, modifier = Modifier.size(48.dp))
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(start = 12.dp),
        ) {
            Text(text = messageReceiver, fontSize = if (isMultiReceivers) 12.sp else 16.sp, color = WakooText)
            Spacer(modifier = Modifier.height(10.dp))
            Text(
                text = messageGift,
                color = Color(0xFF86909C),
                fontSize = 12.sp,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
            )
        }
    }
}

@Preview
@Composable
private fun FamilyGiftContentPreview() {
    WakooTheme {
        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .aspectRatio(2f),
            contentAlignment = Alignment.Center,
        ) {
            ChatGroupGiftContent(
                icon = "",
                messageReceiver =
                    buildAnnotatedString {
                        color(Color(0xFF1D2129)) {
                            append("送给")
                        }
                        color(Color(0xFFFF5E8B)) {
                            append("Sakura、itoko332、kaz")
                        }
                    },
                messageGift = buildAnnotatedString { append("梦幻城包x1") },
                senderIsMe = true,
                isMultiReceivers = true,
            )
        }
    }
}
