package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Minus: ImageVector
    get() {
        if (_minus != null) {
            return _minus!!
        }
        _minus =
            ImageVector
                .Builder(
                    name = "Minus",
                    defaultWidth = 16.dp,
                    defaultHeight = 16.dp,
                    viewportWidth = 16f,
                    viewportHeight = 16f,
                ).apply {
                    // 减号就是一个简单的水平矩形
                    path(
                        fill = SolidColor(Color.Black),
                        stroke = null,
                        strokeLineWidth = 0f,
                        strokeLineCap = StrokeCap.Butt,
                        strokeLineJoin = StrokeJoin.Miter,
                        strokeLineMiter = 4f,
                        pathFillType = PathFillType.NonZero,
                    ) {
                        // 矩形区域：x从2到14，y从7到9
                        moveTo(2f, 7f) // 移动到左上角
                        horizontalLineTo(14f) // 绘制顶边
                        verticalLineTo(9f) // 绘制右边
                        horizontalLineTo(2f) // 绘制底边
                        close() // 闭合路径
                    }
                }.build()
        return _minus!!
    }

@Suppress("ktlint:standard:backing-property-naming")
// 使用 backing property 来延迟初始化，确保只在第一次访问时创建
private var _minus: ImageVector? = null
