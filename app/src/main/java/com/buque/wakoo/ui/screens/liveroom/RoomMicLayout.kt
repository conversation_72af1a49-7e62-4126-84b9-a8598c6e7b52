package com.buque.wakoo.ui.screens.liveroom

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.buque.wakoo.R
import com.buque.wakoo.bean.UIConfig
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.network.api.bean.PkInfo
import com.buque.wakoo.ui.dialog.PKSettingDialogContent
import com.buque.wakoo.ui.icons.MicSeat
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.liveroom.panel.LiveRoomUserInfoPanel
import com.buque.wakoo.ui.screens.messages.chat.PublishCpMedal
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.VolumeRippleEffect
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.voice.PKCheersGroupWidget
import com.buque.wakoo.ui.widget.voice.PKCountdownTimer
import com.buque.wakoo.ui.widget.voice.PKParallelogramCard
import com.buque.wakoo.ui.widget.voice.PKProgressBar
import com.buque.wakoo.utils.rememberCustomGridCells
import com.google.common.collect.Multimaps.index

@Composable
fun LiveMicLayout(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    val onClickUser = { item: MicSeatsInfo ->
        if (item is MicSeatsInfo.User) {
            roomInfoState.sendEvent(
                RoomEvent.PanelDialog {
                    LiveRoomUserInfoPanel(item.user, roomInfoState)
                },
            )
        } else {
            roomInfoState.sendEvent(RoomEvent.UpMic(item.index))
        }
    }
    val config by AppConfigManager.uiConfigFlow.collectAsStateWithLifecycle()

    when (roomInfoState.basicInfo.roomMode) {
        LiveRoomMode.Normal -> {
            NormalModeMicLayout(roomInfoState, config, modifier, onClickUser)
        }

        LiveRoomMode.Radio -> {
            RadioModeMicLayout(roomInfoState, config, modifier, onClickUser)
        }

        LiveRoomMode.Private -> {
            PrivateModeMicLayout(roomInfoState, config, modifier, onClickUser)
        }

        LiveRoomMode.PKMode -> {
            PKModeMicLayout(roomInfoState, config, modifier, onClickUser)
        }

        else -> Unit
    }
}

@Composable
private fun NormalModeMicLayout(
    roomInfoState: LiveRoomInfoState,
    config: UIConfig,
    modifier: Modifier = Modifier,
    onClickUser: (MicSeatsInfo) -> Unit,
) {
    // 使用 LazyVerticalGrid
    LazyVerticalGrid(
        columns = GridCells.Fixed(4),
        modifier = modifier.fillMaxWidth(),
        contentPadding = PaddingValues(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(6.dp),
    ) {
        val micList = roomInfoState.micList

        itemsIndexed(micList) { index, item ->
            MicSeatItem(
                roomInfoState = roomInfoState,
                item = item,
                modifier = Modifier.widthIn(max = 80.dp),
                onClick = {
                    onClickUser(it)
                },
                config = config,
            )
        }
    }
}

@Composable
private fun RadioModeMicLayout(
    roomInfoState: LiveRoomInfoState,
    config: UIConfig,
    modifier: Modifier = Modifier,
    onClickUser: (MicSeatsInfo) -> Unit,
) {
    // 使用 LazyVerticalGrid
    LazyVerticalGrid(
        columns = GridCells.Fixed(4),
        modifier =
            modifier
                .animateContentSize()
                .fillMaxWidth(),
        contentPadding = PaddingValues(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(6.dp),
    ) {
        val micList = roomInfoState.micList

        itemsIndexed(micList, span = { index, item ->
            if (index == 0) {
                GridItemSpan(4)
            } else {
                GridItemSpan(1)
            }
        }) { index, item ->
            MicSeatItem(
                roomInfoState = roomInfoState,
                item = item,
                modifier = Modifier.widthIn(max = if (index == 0) 92.dp else 80.dp),
                onClick = {
                    onClickUser(it)
                },
                config = config,
            )
        }
    }
}

@Composable
private fun PrivateModeMicLayout(
    roomInfoState: LiveRoomInfoState,
    config: UIConfig,
    modifier: Modifier = Modifier,
    onClickUser: (MicSeatsInfo) -> Unit,
) {
    // 使用 LazyVerticalGrid
    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        modifier =
            modifier
                .animateContentSize()
                .fillMaxWidth(),
        contentPadding = PaddingValues(horizontal = 16.dp),
        horizontalArrangement = Arrangement.SpaceAround,
    ) {
        val micList = roomInfoState.micList

        itemsIndexed(micList) { index, item ->
            MicSeatItem(
                roomInfoState = roomInfoState,
                item = item,
                modifier = Modifier.widthIn(max = 92.dp),
                onClick = {
                    onClickUser(it)
                },
                config = config,
            )
        }
    }
}

//region pk模式
@Composable
private fun PKModeMicLayout(
    roomInfoState: LiveRoomInfoState,
    config: UIConfig,
    modifier: Modifier = Modifier,
    onClickUser: (MicSeatsInfo) -> Unit,
) {
    val pkInfo = roomInfoState.extraInfo.pkRoomInfo ?: PkInfo()
    Column {
        // 主持人
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.Bottom,
            horizontalArrangement = Arrangement.Center,
        ) {
            Text(
                "主持".localized,
                modifier =
                    Modifier
                        .background(color = Color(0x59ffffff), shape = CircleShape)
                        .padding(horizontal = 4.dp, vertical = 1.dp),
                fontSize = 10.sp,
                lineHeight = 10.sp,
                color = Color.White,
            )
            Box(
                modifier = Modifier.width(80.dp),
            ) {
                roomInfoState.micList.firstOrNull()?.let {
                    MicSeatItem(
                        roomInfoState = roomInfoState,
                        item = it,
                        onClick = {
                            onClickUser(it)
                        },
                        config = config,
                    )
                }
            }
            // 这里也放一个,让其左右对称
            Text(
                "主持".localized,
                modifier = Modifier.padding(horizontal = 4.dp, vertical = 1.dp),
                color = Color.Transparent,
                fontSize = 10.sp,
                lineHeight = 10.sp,
            )
        }
        SizeHeight(8.dp)
        // 惩罚 / pk设置 / pk状态
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(28.dp)
                    .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 惩罚
            PKParallelogramCard(
                text = "惩罚: %s".localizedFormat(pkInfo.title.ifBlank { "等待房管设置".localized }),
                modifier =
                    Modifier
                        .widthIn(max = 128.dp)
                        .fillMaxHeight(),
            )

            Weight()

            val role = roomInfoState.getRoomRole(LocalSelfUserProvider.currentId)
            // 右侧的PunishmentCard，不设置weight，靠右显示
            if (role != RoomRole.Member) {
                PKParallelogramCard(
                    text =
                        if (pkInfo.pkStatus ==
                            PkInfo.STATE_FINISHED
                        ) {
                            "重新开始".localized
                        } else {
                            "PK设置".localized
                        },
                    backgroundColor = Color(0x859A0CA4),
                    borderColor = Color(0xFFFF4BC6),
                    modifier =
                        Modifier
                            .fillMaxHeight()
                            .click {
                                if (pkInfo.pkStatus != PkInfo.STATE_FINISHED) {
                                    roomInfoState.sendEvent(
                                        RoomEvent.PanelDialog { roomInfoState ->
                                            PKSettingDialogContent(roomInfoState)
                                        },
                                    )
                                } else {
                                    roomInfoState.sendEvent(RoomEvent.OnPkSettingsEvent(3))
                                }
                            },
                )
            }

            PKParallelogramCard(
                backgroundColor = Color(0x859A0CA4),
                borderColor = Color(0xFFFF4BC6),
                modifier =
                    Modifier
                        .widthIn(min = 72.dp)
                        .fillMaxHeight(),
            ) {
                when (pkInfo.pkStatus) {
                    PkInfo.STATE_RUNNING -> {
                        PKCountdownTimer(pkInfo.endTime - (System.currentTimeMillis() / 1000L).toInt())
                    }

                    PkInfo.STATE_FINISHED -> {
                        Text(
                            text = "PK已结束".localized,
                            color = Color.White,
                            fontSize = 13.sp,
                            fontWeight = FontWeight.W500,
                            textAlign = TextAlign.Center,
                            maxLines = 1,
                        )
                    }

                    else -> {
                        Text(
                            text = "PK未开始".localized,
                            color = Color.White,
                            fontSize = 13.sp,
                            fontWeight = FontWeight.W500,
                            textAlign = TextAlign.Center,
                            maxLines = 1,
                        )
                    }
                }
            }
        }
        SizeHeight(6.dp)
        Box {
            LazyVerticalGrid(
                columns = rememberCustomGridCells(4, 20.dp),
                modifier =
                    modifier
                        .animateContentSize()
                        .fillMaxWidth()
                        .paint(painter = painterResource(R.drawable.ic_pkmode_mic_layout_bg)),
                contentPadding = PaddingValues(horizontal = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(6.dp, alignment = Alignment.CenterVertically),
            ) {
                val micList =
                    if (roomInfoState.micList.size >= 2) {
                        roomInfoState.micList.slice(1 until roomInfoState.micList.size)
                    } else {
                        roomInfoState.micList
                    }

                itemsIndexed(micList) { index, item ->
                    // 15左偏移 26右偏移
                    MicSeatItem(
                        roomInfoState = roomInfoState,
                        item = item,
                        onClick = {
                            onClickUser(it)
                        },
                        config = config,
                        modifier =
                            Modifier.padding(
                                end = if (index % 4 == 1) 20.dp else 0.dp,
                                start = if (index % 4 == 2) 20.dp else 0.dp,
                            ),
                    )
                }
            }

            Image(
                painter = painterResource(R.drawable.ic_pkmode_mic_layout_pk),
                modifier =
                    Modifier
                        .align(Alignment.Center)
                        .size(36.dp),
                contentDescription = null,
            )
        }
        PKProgressBar(
            pkInfo.blueSideValue,
            pkInfo.redSideValue,
            pkStatus = pkInfo.pkStatus,
            winnerSide = pkInfo.winnerSide ?: -2,
            modifier =
                Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth(),
        )
        SizeHeight(8.dp)
        PKCheersGroupWidget(roomInfoState)
    }
}

//endregion

@Composable
private fun MicSeatItem(
    roomInfoState: LiveRoomInfoState,
    item: MicSeatsInfo,
    modifier: Modifier = Modifier,
    emptySeatText: () -> String = {
        "点击加入".localized
    },
    config: UIConfig,
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    horizontalAlignment: Alignment.Horizontal = Alignment.CenterHorizontally,
    onClick: (MicSeatsInfo) -> Unit,
) {
    val context = LocalContext.current
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            verticalArrangement = verticalArrangement,
            horizontalAlignment = horizontalAlignment,
            modifier = modifier.fillMaxWidth(),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .aspectRatio(1f)
                        .noEffectClick {
                            onClick(item)
                        },
                contentAlignment = Alignment.Center,
            ) {
                when (item) {
                    is MicSeatsInfo.Empty -> {
                        Box(
                            modifier =
                                Modifier
                                    .fillMaxSize(0.7f)
                                    .background(Color(0x26FFFFFF), CircleShape),
                            contentAlignment = Alignment.Center,
                        ) {
                            Image(
                                imageVector = WakooIcons.MicSeat,
                                contentDescription = null,
                                modifier = Modifier.fillMaxSize(0.46f),
                            )
                        }
                    }

                    is MicSeatsInfo.User -> {
                        val volume by roomInfoState.rememberVolumeState(item.user.id)
                        val roomUser by remember(item.user.id) {
                            derivedStateOf {
                                roomInfoState.requireRoomUser(item.user)
                            }
                        }
                        VolumeRippleEffect(
                            volume = volume,
                            color =
                                when {
                                    !roomUser.genderIsSet -> Color(0xFFFFFFFF)
                                    roomUser.isBoy -> Color(0xFF5AA1EA)
                                    else -> Color(0xFFFF5E8B)
                                },
                            modifier = Modifier.fillMaxSize(),
                            minRadiusPercent = 0.65f,
                            maxRadiusPercent = 0.95f,
                            initialAlpha = 0.6f,
                            triggerThreshold = 0.05f,
                            animationDurationMillis = 3000,
                        ) {
                            AvatarNetworkImage(
                                user = roomUser,
                                modifier = Modifier.fillMaxSize(0.7f),
                                enabled = false,
                                size = Dp.Unspecified,
                            )

                            val avatarFrame = roomUser.avatarFrame
                            if (!avatarFrame.isNullOrBlank()) {
                                AsyncImage(
                                    model =
                                        ImageRequest
                                            .Builder(context)
                                            .data(avatarFrame)
                                            .crossfade(false)
                                            .build(),
                                    contentDescription = null,
                                    modifier = Modifier.fillMaxSize(),
                                    placeholder = null,
                                    error = null,
                                )
                            }

                            val publicCp = roomUser.cpRelationInfo?.publicCp
                            if (!config.partnerHasEscaped && publicCp != null && !LocalSelfUserProvider.isJP) {
                                PublishCpMedal(
                                    publicCp = publicCp,
                                    publicCpMedalUrl = roomUser.cpRelationInfo?.publicCpMedalSmallUrl.orEmpty(),
                                    modifier =
                                        Modifier
                                            .padding(bottom = 5.dp)
                                            .align(Alignment.BottomCenter)
                                            .fillMaxWidth()
                                            .aspectRatio(64f / 18),
                                )
                            }
                        }
                    }
                }
            }

            when (item) {
                is MicSeatsInfo.Empty -> {
                    Text(
                        text = emptySeatText(),
                        color = Color.White.copy(alpha = 0.6f),
                        fontSize = 12.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                }

                is MicSeatsInfo.User -> {
                    val user by remember(item.user.id) {
                        derivedStateOf {
                            roomInfoState.requireRoomUser(item.user)
                        }
                    }
                    Text(
                        text = user.name,
                        color = Color.White,
                        fontSize = 12.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                    if (roomInfoState.basicInfo.roomMode == LiveRoomMode.Normal ||
                        roomInfoState.basicInfo.roomMode == LiveRoomMode.PKMode
                    ) {
                        SizeHeight(4.dp)
                        Row(
                            modifier =
                                Modifier
                                    .height(12.dp)
                                    .background(Color(0x26FFFFFF), CircleShape)
                                    .padding(horizontal = 6.dp),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_mic_score),
                                contentDescription = null,
                                modifier = Modifier.height(8.dp),
                            )
                            SizeWidth(1.dp)
                            Text(
                                text = item.score.toString(),
                                color = WakooWhite,
                                fontSize = 9.sp,
                                lineHeight = 9.sp,
                            )
                        }
                    }
                }
            }
        }
    }
}
