package com.buque.wakoo.ui.screens.chatgroup

import androidx.compose.foundation.layout.Column
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.AppNavController
import com.buque.wakoo.navigation.AppNavDisplay
import com.buque.wakoo.navigation.ChatGroupRoute
import com.buque.wakoo.navigation.CtrlKey
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.appEntry
import com.buque.wakoo.navigation.appEntryProvider
import com.buque.wakoo.navigation.dialog.easyShow
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.navigation.rememberAppNavController
import com.buque.wakoo.ui.dialog.AnyPopDialogProperties
import com.buque.wakoo.ui.screens.chatgroup.member.task.CPMemberNotPublicScreen
import com.buque.wakoo.ui.screens.chatgroup.member.task.MemberNoCPScreen
import com.buque.wakoo.ui.screens.chatgroup.panel.ChatGroupTipDialog
import com.buque.wakoo.ui.screens.chatgroup.screen.AddChatGroupAdminScreen
import com.buque.wakoo.ui.screens.chatgroup.screen.ApplyJoinChatGroupScreen
import com.buque.wakoo.ui.screens.chatgroup.screen.ChatGroupAdminSettingScreen
import com.buque.wakoo.ui.screens.chatgroup.screen.ChatGroupDetailScreen
import com.buque.wakoo.ui.screens.chatgroup.screen.ChatGroupHome
import com.buque.wakoo.ui.screens.chatgroup.screen.ChatGroupMemberListScreen
import com.buque.wakoo.ui.screens.chatgroup.screen.GroupContributeRankScreen
import com.buque.wakoo.ui.screens.chatgroup.screen.GroupSettingsScreen
import com.buque.wakoo.ui.screens.chatgroup.screen.KickOutMemberScreen
import com.buque.wakoo.ui.widget.WakooTitleBar
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.ui.widget.state.hasError
import com.buque.wakoo.utils.eventBus.AppEvent
import com.buque.wakoo.utils.eventBus.EventBusEffect
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupOnlineMemberListViewModel
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupSettingsViewModel
import kotlinx.coroutines.flow.collectLatest

object ChatGroupNavCtrlKey : CtrlKey<AppNavController>

@Composable
fun ChatGroupHostScreen(groupId: String, detail: Boolean = true) {
    val controller =
        rememberAppNavController(
            key = ChatGroupNavCtrlKey,
            if (detail) ChatGroupRoute.ChatGroupDetail(groupId) else ChatGroupRoute.Home,
        )

    val rootNav = LocalAppNavController.root

    val dc = rememberDialogController()
    val settingViewModel =
        viewModel<ChatGroupSettingsViewModel>(
            factory =
                viewModelFactory {
                    initializer {
                        ChatGroupSettingsViewModel(groupId)
                    }
                },
        )
    EventBusEffect<AppEvent.Action> {
        if (it.actionName == "show_group_home") {
            controller.popUntil { r -> r == ChatGroupRoute.Home }
        }
    }

    LaunchedEffect(Unit) {
        settingViewModel.eventFlow.collectLatest { event ->
            when (event) {
                IMEvent.TRIBE_DESTROYED -> {
                    dc.easyShow(dialogProperties = AnyPopDialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false)) {
                        ChatGroupTipDialog {
                            rootNav.pop()
                        }
                    }
                }

                IMEvent.MEMBER_KICKED_OUT -> {
                    showToast("你已被踢出该群组".localized)
                    rootNav.pop()
                }
            }
        }
    }

    LaunchedEffect(settingViewModel) {
        if (settingViewModel.state.value is CState.Idle) {
            settingViewModel.refreshState()
        }
    }
    val state by settingViewModel.state

    Column {
        if (state.hasError) {
            WakooTitleBar("")
        }
        CStateLayout(state, onRetry = { settingViewModel.refreshState() }) {
            LocalAppNavController.ProvideController(controller) {
                AppNavDisplay(
                    backStack = controller.backStack,
                    entryProvider =
                        appEntryProvider {
                            appEntry<ChatGroupRoute.Home> {
                                ChatGroupHome(settingViewModel)
                            }

                            appEntry<ChatGroupRoute.ChatGroupDetail> {
                                ChatGroupDetailScreen(groupId, settingViewModel)
                            }
                            appEntry<ChatGroupRoute.ChatGroupApplyJoinList> {
                                ApplyJoinChatGroupScreen(it.chatGroupId) {
                                    settingViewModel.refreshState()
                                }
                            }
                            appEntry<ChatGroupRoute.GroupSettings> {
                                GroupSettingsScreen(it.chatGroupId, settingViewModel)
                            }
                            appEntry<ChatGroupRoute.ChatGroupAdminSetting> {
                                ChatGroupAdminSettingScreen(it.groupId)
                            }
                            appEntry<ChatGroupRoute.ChatGroupAdminIncrease> {
                                AddChatGroupAdminScreen(it.groupId, it.maxCount)
                            }
                            appEntry<ChatGroupRoute.KickOutMember> {
                                KickOutMemberScreen(it.groupId, it.targetRoles)
                            }
                            appEntry<ChatGroupRoute.MemberNoCP> {
                                MemberNoCPScreen()
                            }
                            appEntry<ChatGroupRoute.MemberWithCPNoPublic> {
                                CPMemberNotPublicScreen()
                            }
                            appEntry<ChatGroupRoute.ChatGroupMemberList> {
                                ChatGroupMemberListScreen(viewModel = viewModel(initializer = {
                                    ChatGroupOnlineMemberListViewModel(it.groupId,it.firstPageMembers)
                                }))
                            }
                            appEntry<ChatGroupRoute.GroupContributeRank>{
                                GroupContributeRankScreen()
                            }
                        },
                )
            }
        }
    }
}
