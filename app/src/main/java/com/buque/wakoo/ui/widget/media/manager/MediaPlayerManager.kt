package com.buque.wakoo.ui.widget.media.manager

import android.annotation.SuppressLint
import android.content.Context
import androidx.annotation.OptIn
import androidx.compose.runtime.Stable
import androidx.media3.common.C
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.database.StandaloneDatabaseProvider
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.exoplayer.source.preload.DefaultPreloadManager
import androidx.media3.exoplayer.source.preload.TargetPreloadStatusControl
import com.buque.wakoo.WakooApplication
import java.io.File
import kotlin.math.abs

@OptIn(UnstableApi::class)
@SuppressLint("StaticFieldLeak")
@Stable
object MediaPlayerManager : Player.Listener {
    private const val LOAD_CONTROL_MIN_BUFFER_MS = 5_000
    private const val LOAD_CONTROL_MAX_BUFFER_MS = 20_000
    private const val LOAD_CONTROL_BUFFER_FOR_PLAYBACK_MS = 500

    private val context: Context = WakooApplication.instance

    private val preloadManagerBuilder: DefaultPreloadManager.Builder

    private val preloadManager: DefaultPreloadManager

    private val preloadControl: DefaultPreloadControl

    private val singleMediaPlayerController: MediaPlayerController

    private val pool =
        MediaPlayerControllerPool(7) {
            MediaPlayerController(context, preloadManager, preloadManagerBuilder)
        }

    init {
        val upstreamDataSourceFactory = DefaultDataSource.Factory(context)

        val cacheDir = File(context.cacheDir, "media_cache")
        val databaseProvider = StandaloneDatabaseProvider(context)

        // 创建带缓存的数据源工厂
        val cacheDataSourceFactory =
            CacheDataSource
                .Factory()
                .setCache(
                    SimpleCache(
                        cacheDir,
                        LeastRecentlyUsedCacheEvictor(1024 * 1024 * 1024),
                        databaseProvider,
                    ),
                ).setUpstreamDataSourceFactory(upstreamDataSourceFactory)
                .setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)

        val loadControl =
            DefaultLoadControl
                .Builder()
                .setBufferDurationsMs(
                    // minBufferMs =
                    LOAD_CONTROL_MIN_BUFFER_MS,
                    // maxBufferMs =
                    LOAD_CONTROL_MAX_BUFFER_MS,
                    // bufferForPlaybackMs =
                    LOAD_CONTROL_BUFFER_FOR_PLAYBACK_MS,
                    // bufferForPlaybackAfterRebufferMs =
                    DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS,
                ).setPrioritizeTimeOverSizeThresholds(true)
                .build()

        preloadControl = DefaultPreloadControl()

        preloadManagerBuilder =
            DefaultPreloadManager
                .Builder(context, preloadControl)
                .setMediaSourceFactory(DefaultMediaSourceFactory(cacheDataSourceFactory))
                .setLoadControl(loadControl)

        preloadManager = preloadManagerBuilder.build()

        singleMediaPlayerController = pool.acquire()
    }

    fun acquireController(): MediaPlayerController = pool.acquire()

    fun releaseController(controller: MediaPlayerController) {
        pool.release(controller)
    }

    val currentPlayingTag = singleMediaPlayerController.currentPlayingTag

    val currentBufferingTag = singleMediaPlayerController.currentBufferingTag

    val currentPlayItem: PlayMediaItem?
        get() = singleMediaPlayerController.currentPlayItem

    fun play(playItem: PlayMediaItem) {
        singleMediaPlayerController.play(playItem)
    }

    fun pause(playItem: PlayMediaItem) {
        singleMediaPlayerController.pause(playItem)
    }

    fun release(playItem: PlayMediaItem) {
        singleMediaPlayerController.release(playItem)
    }

    fun pauseIf(predicate: (PlayMediaItem) -> Boolean) {
        singleMediaPlayerController.pauseIf(predicate)
    }

    fun releaseIf(predicate: (PlayMediaItem) -> Boolean) {
        singleMediaPlayerController.releaseIf(predicate)
    }

    fun pause(tag: String) {
        singleMediaPlayerController.pause(tag)
    }

    fun release(tag: String) {
        singleMediaPlayerController.release(tag)
    }

    fun getPlayerPosition(playItem: PlayMediaItem): Long = singleMediaPlayerController.getPlayerPosition(playItem.tag)

    fun getPlayerDuration(
        playItem: PlayMediaItem,
        default: Long = -1,
    ): Long = singleMediaPlayerController.getPlayerDuration(playItem.tag, default)

    fun getPlayerPosition(tag: String): Long = singleMediaPlayerController.getPlayerPosition(tag)

    fun getPlayerDuration(
        tag: String,
        default: Long = -1,
    ): Long = singleMediaPlayerController.getPlayerDuration(tag, default)

    fun toggle(playItem: PlayMediaItem) {
        singleMediaPlayerController.toggle(playItem)
    }

    fun addPreload(
        playItem: PlayMediaItem,
        rankingData: Int,
    ): Boolean {
        if (playItem.isLive) {
            return false
        }
        preloadManager.add(playItem.exoPlayerMediaItem, rankingData)
        return true
    }

    fun removePreload(playItem: PlayMediaItem): Boolean {
        if (playItem.isLive) {
            return false
        }
        preloadManager.remove(playItem.exoPlayerMediaItem)
        return true
    }

    fun onPageSelected(position: Int) {
        preloadControl.currentPlayingIndex = position
        preloadManager.setCurrentPlayingIndex(position)
        preloadManager.invalidate()
    }

    @androidx.annotation.OptIn(UnstableApi::class)
    private class DefaultPreloadControl(
        var currentPlayingIndex: Int = C.INDEX_UNSET,
    ) : TargetPreloadStatusControl<Int, DefaultPreloadManager.PreloadStatus> {
        @OptIn(UnstableApi::class)
        override fun getTargetPreloadStatus(rankingData: Int): DefaultPreloadManager.PreloadStatus? {
            if (abs(rankingData - currentPlayingIndex) == 2) {
                return DefaultPreloadManager.PreloadStatus.specifiedRangeLoaded(1500L)
            } else if (abs(rankingData - currentPlayingIndex) == 1) {
                return DefaultPreloadManager.PreloadStatus.specifiedRangeLoaded(2000L)
            }
            return null
        }
    }

    class DefaultMediaPlayerController : MediaPlayerController.Factory {
        override fun create(): MediaPlayerController = MediaPlayerController(context, preloadManager, preloadManagerBuilder)
    }
}
