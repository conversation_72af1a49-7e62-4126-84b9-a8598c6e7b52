package com.buque.wakoo.ui.widget.voice

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.buque.wakoo.R
import com.buque.wakoo.bean.LiveRoomCardItem
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.icons.MicSeat
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import com.buque.wakoo.ui.screens.liveroom.MicSeatsInfo
import com.buque.wakoo.ui.screens.messages.chat.PublishCpMedal
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.utils.rememberCustomGridCells

@Composable
fun LiveMicLayout(
    item: LiveRoomCardItem,
    modifier: Modifier = Modifier,
) {
    val config by AppConfigManager.uiConfigFlow.collectAsStateWithLifecycle()
    val showPublishCp = !config.partnerHasEscaped
    when (item.liveRoomMode) {
        LiveRoomMode.Normal -> {
            NormalModeMicLayout(item, showPublishCp, modifier)
        }

        LiveRoomMode.Radio -> {
            RadioModeMicLayout(item, showPublishCp, modifier)
        }

        LiveRoomMode.PKMode -> {
            PKModeMicLayout(item, showPublishCp, modifier)
        }

        else -> Unit
    }
}

@Composable
private fun NormalModeMicLayout(
    cardItem: LiveRoomCardItem,
    showPublishCp: Boolean,
    modifier: Modifier = Modifier,
) {
    // 使用 LazyVerticalGrid
    LazyVerticalGrid(
        columns = GridCells.Fixed(4),
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(6.dp),
    ) {
        itemsIndexed(cardItem.micList) { index, item ->
            MicSeatItem(
                item = item,
                roomMode = cardItem.liveRoomMode,
                showPublishCp = showPublishCp,
                modifier = Modifier.widthIn(max = 80.dp),
            )
        }
    }
}

@Composable
private fun RadioModeMicLayout(
    cardItem: LiveRoomCardItem,
    showPublishCp: Boolean,
    modifier: Modifier = Modifier,
) {
    // 使用 LazyVerticalGrid
    LazyVerticalGrid(
        columns = GridCells.Fixed(4),
        modifier =
            modifier
                .animateContentSize()
                .fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(6.dp),
    ) {
        itemsIndexed(cardItem.micList, span = { index, item ->
            if (index == 0) {
                GridItemSpan(4)
            } else {
                GridItemSpan(1)
            }
        }) { index, item ->
            MicSeatItem(
                item = item,
                roomMode = cardItem.liveRoomMode,
                showPublishCp = showPublishCp,
                modifier = Modifier.widthIn(max = if (index == 0) 92.dp else 80.dp),
            )
        }
    }
}

@Composable
private fun PKModeMicLayout(
    cardItem: LiveRoomCardItem,
    showPublishCp: Boolean,
    modifier: Modifier = Modifier,
) {
    Box {
        LazyVerticalGrid(
            columns = rememberCustomGridCells(4, 10.dp),
            modifier =
                modifier
                    .animateContentSize()
                    .fillMaxWidth()
                    .paint(painter = painterResource(R.drawable.ic_pkmode_mic_layout_bg)),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(6.dp, alignment = Alignment.CenterVertically),
        ) {
            val micList =
                if (cardItem.micList.size >= 2) cardItem.micList.slice(1 until cardItem.micList.size) else cardItem.micList

            itemsIndexed(micList) { index, item ->
                // 15左偏移 26右偏移
                MicSeatItem(
                    item = item,
                    roomMode = LiveRoomMode.PKMode,
                    showPublishCp = showPublishCp,
                    modifier =
                        Modifier.padding(
                            end = if (index % 4 == 1) 10.dp else 0.dp,
                            start = if (index % 4 == 2) 10.dp else 0.dp,
                        ),
                )
            }
        }

        Image(
            painter = painterResource(R.drawable.ic_pkmode_mic_layout_pk),
            modifier =
                Modifier
                    .align(Alignment.Center)
                    .size(36.dp),
            contentDescription = null,
        )
    }
}

@Composable
private fun MicSeatItem(
    item: MicSeatsInfo,
    roomMode: LiveRoomMode,
    showPublishCp: Boolean,
    modifier: Modifier = Modifier,
    emptySeatText: () -> String = {
        "观众".localized
    },
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    horizontalAlignment: Alignment.Horizontal = Alignment.CenterHorizontally,
) {
    val context = LocalContext.current
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            verticalArrangement = verticalArrangement,
            horizontalAlignment = horizontalAlignment,
            modifier = modifier.fillMaxWidth(),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .aspectRatio(1f),
                contentAlignment = Alignment.Center,
            ) {
                when (item) {
                    is MicSeatsInfo.Empty -> {
                        Box(
                            modifier =
                                Modifier
                                    .fillMaxSize(0.7f)
                                    .background(Color(0x26FFFFFF), CircleShape),
                            contentAlignment = Alignment.Center,
                        ) {
                            Image(
                                imageVector = WakooIcons.MicSeat,
                                contentDescription = null,
                                modifier = Modifier.fillMaxSize(0.46f),
                            )
                        }
                    }

                    is MicSeatsInfo.User -> {
                        AvatarNetworkImage(
                            user = item.user,
                            modifier = Modifier.fillMaxSize(0.7f),
                            size = Dp.Unspecified,
                        )
                        val avatarFrame = item.user.avatarFrame
                        if (!avatarFrame.isNullOrBlank()) {
                            AsyncImage(
                                model =
                                    ImageRequest
                                        .Builder(context)
                                        .data(avatarFrame)
                                        .crossfade(false)
                                        .build(),
                                contentDescription = null,
                                modifier = Modifier.fillMaxSize(),
                                placeholder = null,
                                error = null,
                            )
                        }

                        val publicCp = item.user.cpRelationInfo?.publicCp
                        if (showPublishCp && publicCp != null && !LocalSelfUserProvider.isJP) {
                            PublishCpMedal(
                                publicCp = publicCp,
                                publicCpMedalUrl =
                                    item.user.cpRelationInfo.publicCpMedalSmallUrl,
                                modifier =
                                    Modifier
                                        .padding(bottom = 5.dp)
                                        .align(Alignment.BottomCenter)
                                        .fillMaxWidth()
                                        .aspectRatio(64f / 18),
                            )
                        }
                    }
                }
            }

            when (item) {
                is MicSeatsInfo.Empty -> {
                    Text(
                        text = emptySeatText(),
                        color = Color.White.copy(alpha = 0.6f),
                        fontSize = 12.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                }

                is MicSeatsInfo.User -> {
                    Text(
                        text = item.user.name,
                        color = Color.White,
                        fontSize = 12.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                    if (roomMode == LiveRoomMode.Normal) {
                        SizeHeight(4.dp)
                        Row(
                            modifier =
                                Modifier
                                    .height(12.dp)
                                    .background(Color(0x26FFFFFF), CircleShape)
                                    .padding(horizontal = 6.dp),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_mic_score),
                                contentDescription = null,
                                modifier = Modifier.height(8.dp),
                            )
                            SizeWidth(1.dp)
                            Text(
                                text = item.score.toString(),
                                color = WakooWhite,
                                fontSize = 9.sp,
                                lineHeight = 9.sp,
                            )
                        }
                    }
                }
            }
        }
    }
}
