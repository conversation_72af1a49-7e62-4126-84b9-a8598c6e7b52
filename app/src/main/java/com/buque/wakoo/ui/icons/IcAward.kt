package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.IcAward: ImageVector
    get() {
        if (_IcAward != null) {
            return _IcAward!!
        }
        _IcAward = ImageVector.Builder(
            name = "IcA<PERSON>",
            defaultWidth = 20.dp,
            defaultHeight = 20.dp,
            viewportWidth = 20f,
            viewportHeight = 20f
        ).apply {
            path(fill = SolidColor(Color(0xFF111111))) {
                moveTo(12.504f, 1.669f)
                curveTo(14.344f, 1.669f, 15.837f, 3.161f, 15.837f, 5.002f)
                curveTo(15.837f, 5.61f, 15.674f, 6.179f, 15.39f, 6.67f)
                lineTo(19.17f, 6.669f)
                verticalLineTo(8.336f)
                horizontalLineTo(17.504f)
                verticalLineTo(16.669f)
                curveTo(17.504f, 17.129f, 17.131f, 17.502f, 16.67f, 17.502f)
                horizontalLineTo(3.337f)
                curveTo(2.877f, 17.502f, 2.504f, 17.129f, 2.504f, 16.669f)
                verticalLineTo(8.336f)
                horizontalLineTo(0.837f)
                verticalLineTo(6.669f)
                lineTo(4.617f, 6.67f)
                curveTo(4.333f, 6.179f, 4.17f, 5.61f, 4.17f, 5.002f)
                curveTo(4.17f, 3.161f, 5.663f, 1.669f, 7.504f, 1.669f)
                curveTo(8.5f, 1.669f, 9.394f, 2.106f, 10.004f, 2.798f)
                curveTo(10.614f, 2.106f, 11.508f, 1.669f, 12.504f, 1.669f)
                close()
                moveTo(9.17f, 8.336f)
                horizontalLineTo(4.17f)
                verticalLineTo(15.836f)
                horizontalLineTo(9.17f)
                verticalLineTo(8.336f)
                close()
                moveTo(15.837f, 8.336f)
                horizontalLineTo(10.837f)
                verticalLineTo(15.836f)
                horizontalLineTo(15.837f)
                verticalLineTo(8.336f)
                close()
                moveTo(7.504f, 3.336f)
                curveTo(6.583f, 3.336f, 5.837f, 4.082f, 5.837f, 5.002f)
                curveTo(5.837f, 5.881f, 6.517f, 6.601f, 7.379f, 6.664f)
                lineTo(7.504f, 6.669f)
                horizontalLineTo(9.17f)
                verticalLineTo(5.002f)
                curveTo(9.17f, 4.168f, 8.557f, 3.476f, 7.756f, 3.355f)
                lineTo(7.628f, 3.34f)
                lineTo(7.504f, 3.336f)
                close()
                moveTo(12.504f, 3.336f)
                curveTo(11.625f, 3.336f, 10.905f, 4.016f, 10.842f, 4.878f)
                lineTo(10.837f, 5.002f)
                verticalLineTo(6.669f)
                horizontalLineTo(12.504f)
                curveTo(13.382f, 6.669f, 14.102f, 5.989f, 14.166f, 5.127f)
                lineTo(14.17f, 5.002f)
                curveTo(14.17f, 4.082f, 13.424f, 3.336f, 12.504f, 3.336f)
                close()
            }
        }.build()

        return _IcAward!!
    }

@Suppress("ObjectPropertyName")
private var _IcAward: ImageVector? = null
