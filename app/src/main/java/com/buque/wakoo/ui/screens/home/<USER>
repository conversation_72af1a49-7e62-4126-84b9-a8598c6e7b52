package com.buque.wakoo.ui.screens.home

import androidx.annotation.DrawableRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect
import androidx.lifecycle.compose.LifecycleResumeEffect
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.TopClassBean
import com.buque.wakoo.bean.user.IUserDecorations
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.consts.Contracts
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.manager.UserManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.ui.dialog.DynamicPublishPanel
import com.buque.wakoo.ui.screens.WebViewContent
import com.buque.wakoo.ui.screens.crony.CronySettingsViewModel
import com.buque.wakoo.ui.screens.profile.FollowInfoSection
import com.buque.wakoo.ui.screens.profile.TopAppBar
import com.buque.wakoo.ui.screens.profile.UserProfilePageForMine
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.ExpLevelWidget
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.VipCrownTag
import com.buque.wakoo.ui.widget.coordinatorlayout.CustomCollapsibleHeader
import com.buque.wakoo.ui.widget.coordinatorlayout.rememberCustomCollapsibleHeaderState
import com.buque.wakoo.ui.widget.coordinatorlayout.rememberCustomCollapsibleScrollBehavior
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.utils.eventBus.AppEvent
import com.buque.wakoo.utils.eventBus.EventBusEffect
import com.buque.wakoo.utils.eventBus.tryToLink
import com.buque.wakoo.viewmodel.FirstClassViewModel
import java.text.SimpleDateFormat

private const val PAGE_TAG_PREFIX = "mine-tab-"

enum class MineAbility {
    DIAMOND,
    MEMBER,
    DRESSUP_SHOP,
    DRESSUP_CENTER,
}

/**
 * 个人中心页面 - 已开通会员版本
 */
@Composable
fun MineTabPage(
    toEditUserInfo: () -> Unit = {},
    toSettings: () -> Unit = {},
    toUserRelations: (Int) -> Unit = {},
    onAbilityClick: (value: MineAbility) -> Unit = {},
    onNavigateTo: (AppNavKey) -> Unit = {},
) {
    val headerState = rememberCustomCollapsibleHeaderState()
    val scrollBehavior =
        rememberCustomCollapsibleScrollBehavior(
            state = headerState.topAppBarState,
            enableSnap = true,
        )
    val dc = rememberDialogController()
    val nav = LocalAppNavController.root

    val selfUserInfo = LocalSelfUserProvider.current

    val refreshEnable by remember(headerState) {
        derivedStateOf {
            headerState.collapsedFraction <= 0.0f
        }
    }

    LifecycleEventEffect(Lifecycle.Event.ON_START) {
        UserManager.refreshSelfUserInfo()
    }
    val config by AppConfigManager.uiConfigFlow.collectAsStateWithLifecycle()
    val cronyViewModel: CronySettingsViewModel =
        viewModel(key = "crony-settings-${selfUserInfo.id}", initializer = { CronySettingsViewModel(selfUserInfo.id) })
    LaunchedEffect(Unit) {
        cronyViewModel.refreshState()
    }
    EventBusEffect<AppEvent.Refresh> { event ->
        if (event.flag == "crony") {
            cronyViewModel.refreshState()
        }
    }
    Box(
        modifier =
            Modifier
                .fillMaxSize()
                .background(Color(0xfff7f7f7)),
    ) {
        Image(
            painter = painterResource(R.drawable.bg_common_top),
            contentDescription = null,
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.FillWidth,
            alignment = Alignment.TopCenter,
        )
        Scaffold(
            modifier =
                Modifier
                    .fillMaxSize()
                    .nestedScroll(scrollBehavior.nestedScrollConnection),
            containerColor = Color.Transparent,
            topBar = {
                Column {
                    TopAppBar(
                        user = selfUserInfo,
                        toSettings = toSettings,
                        toEditUserInfo = toEditUserInfo,
                    )
                    CustomCollapsibleHeader(
                        scrollBehavior = scrollBehavior,
                        state = headerState,
                        expandedContent = {
                            // --- 完全自定义的展开状态内容 ---
                            Column {
                                SizeHeight(5.dp)

                                // 个人资料区域
                                UserProfileSection(user = selfUserInfo)

                                SizeHeight(14.dp)

                                // 粉丝关注信息
                                FollowInfoSection(socialInfo = selfUserInfo.socialInfo, toUserRelations = toUserRelations)

                                SizeHeight(14.dp)

                                var resumeKey by remember { mutableIntStateOf(0) }
                                LifecycleResumeEffect(Unit) {
                                    resumeKey += 1
                                    onPauseOrDispose { }
                                }

                                // 主播视角(我的收益)
                                if (resumeKey > 0 && selfUserInfo.isHQU && selfUserInfo.isCN) {
                                    key(resumeKey) {
                                        Box(
                                            modifier =
                                                Modifier
                                                    .fillMaxWidth()
                                                    .padding(horizontal = 16.dp)
                                                    .aspectRatio(343 / 84f),
                                        ) {
                                            WebViewContent(
                                                url = Contracts.bonusBanner,
                                                modifier =
                                                    Modifier
                                                        .fillMaxSize()
                                                        .clip(RoundedCornerShape(16.dp)),
                                                onOpenPage = {
                                                    nav.push(it)
                                                },
                                                onFinish = {},
                                            )
                                        }
                                    }
                                    SizeHeight(14.dp)
                                }
                                FirstClassWidget(modifier = Modifier.fillMaxWidth())

                                // 功能tab
                                Row(
                                    modifier =
                                        Modifier
                                            .padding(horizontal = 16.dp)
                                            .fillMaxWidth()
                                            .background(
                                                color = Color.White,
                                                shape = RoundedCornerShape(16.dp),
                                            ).padding(vertical = 18.dp),
                                    horizontalArrangement = Arrangement.SpaceAround,
                                ) {
                                    MineAbilityTab("我的钻石".localized, R.drawable.ic_mine_diamond, {
                                        onAbilityClick(MineAbility.DIAMOND)
                                    })
                                    MineAbilityTab("我的会员".localized, R.drawable.ic_mine_vip, {
                                        onAbilityClick(MineAbility.MEMBER)
                                    })
                                    MineAbilityTab("装扮中心".localized, R.drawable.ic_mine_dressup_shop, {
                                        onAbilityClick(MineAbility.DRESSUP_SHOP)
                                    })
                                    if (config.userLevelEntry) {
                                        MineAbilityTab("我的等级".localized, R.drawable.ic_mine_level, {
                                            if (selfUserInfo.isJP) {
                                                onNavigateTo(
                                                    Route.Web("${EnvironmentManager.current.apiUrl}h5/mine_level_ja"),
                                                )
                                            } else {
                                                onNavigateTo(
                                                    Route.Web("${EnvironmentManager.current.apiUrl}h5/mine_level"),
                                                )
                                            }
                                        })
                                    }
                                    if (selfUserInfo.isJP) {
                                        MineAbilityTab("我的等级".localized, R.drawable.ic_mine_visitor, {
                                            onNavigateTo(Route.VisitorHistory)
                                        })
                                    }
                                }

                                SizeHeight(14.dp)
                            }
                        },
                    )
                }
            },
            contentWindowInsets = WindowInsets.statusBars,
        ) { paddingValues ->
            UserProfilePageForMine(
                selfUserInfo = selfUserInfo,
                tagPrefix = PAGE_TAG_PREFIX,
                cronyViewModel = cronyViewModel,
                modifier = Modifier.padding(paddingValues),
                refreshEnable = refreshEnable,
                onNavigateTo = onNavigateTo,
                onPublishDynamic = {
                    dc.easyPost {
                        DynamicPublishPanel(
                            onVoiceContent = {
                                dismiss()
                                onNavigateTo(Route.VoicePublish)
                            },
                            onMomentContent = {
                                dismiss()
                                onNavigateTo(Route.MomentPublish)
                            },
                        )
                    }
                },
            )
        }
        dc.RenderDialogs(Unit)
    }
}

@Composable
fun FirstClassWidget(modifier: Modifier = Modifier) {
    val bean =
        if (LocalInspectionMode.current) {
            TopClassBean(
                showFirstClassInfo = true,
                firstClassInfo =
                    TopClassBean.TopClassInfo(
                        isFirstClassUser = true,
                    ),
            )
        } else {
            val viewModel =
                viewModel<FirstClassViewModel>(key = "mine_topclass_${SelfUser?.id}", initializer = {
                    FirstClassViewModel(SelfUser?.id ?: "0")
                })

            LaunchedEffect(key1 = Unit) {
                viewModel.refresh()
            }
            val firstClassBean by viewModel.firstClassBean.collectAsStateWithLifecycle()
            firstClassBean
        }

    AnimatedVisibility(visible = bean.showFirstClassInfo) {
        Box(modifier, contentAlignment = Alignment.BottomCenter) {
            val ctx = LocalContext.current
            Row(
                modifier =
                    Modifier
                        .size(311.dp, 64.dp)
                        .paint(
                            painter = painterResource(id = R.drawable.ic_firstclass_bg),
                            contentScale = ContentScale.FillWidth,
                        ).padding(vertical = 8.dp, horizontal = 12.dp)
                        .click {
                            "${EnvironmentManager.current.apiUrl}h5/zh/firstclass".tryToLink()
                        },
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                Column(
                    verticalArrangement = Arrangement.SpaceBetween,
                    modifier =
                        Modifier
                            .fillMaxHeight()
                            .weight(1f),
                ) {
                    Text(
                        buildAnnotatedString {
                            append("头等舱俱乐部".localized)
                            if (bean.firstClassInfo.isFirstClassUser) {
                                withStyle(
                                    SpanStyle(
                                        fontSize = 12.sp,
                                    ),
                                ) {
                                    append(
                                        "  已加入%s天".localizedFormat(bean.firstClassInfo.joinDays),
                                    )
                                }
                            }
                        },
                        color = Color(0xFFFFF3CB),
                        fontSize = 14.sp,
                        lineHeight = 14.sp,
                    )
                    if (!bean.firstClassInfo.isFirstClassUser) {
                        Box(
                            modifier =
                                Modifier
                                    .fillMaxWidth(0.69f)
                                    .height(6.dp)
                                    .background(
                                        color = Color(0xFF38332e),
                                        shape = RoundedCornerShape(24.dp),
                                    ),
                        ) {
                            Spacer(
                                modifier =
                                    Modifier
                                        .fillMaxHeight()
                                        .fillMaxWidth(bean.firstClassInfo.paidCoins / bean.firstClassInfo.totalCoin.toFloat())
                                        .background(
                                            color = Color(0xFFFFF3CB),
                                            shape = RoundedCornerShape(24.dp),
                                        ),
                            )
                        }

                        Text(
                            buildAnnotatedString {
                                val remainCoin = (bean.firstClassInfo.totalCoin - bean.firstClassInfo.paidCoins).toString()
                                val content = "再充 %s 金币可加入 >".localizedFormat(remainCoin)
                                append(content)
                                val start = content.indexOf(remainCoin)
                                val end = start + remainCoin.length
                                addStyle(SpanStyle(color = Color(0xFFF2BC65)), start, end)

//                                append("再充".localized)
//                                withStyle(
//                                    SpanStyle(
//                                        color = Color(0xFFF2BC65),
//                                    ),
//                                ) {
//                                    append(" ${bean.firstClassInfo.totalCoin - bean.firstClassInfo.paidCoins} ")
//                                }
//                                append("金币可加入")
                            },
                            style =
                                androidx.compose.ui.text.TextStyle(
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp,
                                    color = Color(0xFFFFF3CB),
                                ),
                        )
                    } else {
                        val formater =
                            remember {
                                SimpleDateFormat("YYYY-MM-dd")
                            }
                        Text(
                            text = "有效期至%s".localizedFormat(formater.format(bean.firstClassInfo.endTime * 1000L)),
                            style =
                                androidx.compose.ui.text.TextStyle(
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp,
                                    color = Color(0xFF645B4C),
                                ),
                        )
                    }
                }

                NetworkImage(
                    if (bean.firstClassInfo.isFirstClassUser) R.drawable.ic_firstclass_actived else R.drawable.ic_firstclass_inactived,
                    modifier =
                        Modifier
                            .padding(horizontal = 4.dp)
                            .size(54.dp),
                )
            }
        }
    }
}

/**
 * 用户个人资料区域
 */
@Composable
private fun UserProfileSection(
    user: User,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier =
            modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // 用户头像
        AvatarNetworkImage(
            user = user,
            size = 72.dp,
        )

        SizeWidth(12.dp)

        // 用户信息
        Column(
            modifier = Modifier.padding(vertical = 12.dp),
        ) {
            // 用户名
            Text(
                text = user.name,
                style = MaterialTheme.typography.titleSmall,
            )

            SizeHeight(8.dp)

            Text("ID:${user.publishId}", color = Color(0xff999999), fontSize = 12.sp, lineHeight = 12.sp)

            SizeHeight(8.dp)

            // 性别年龄和VIP标签
            Row(
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // 性别年龄标签
                GenderAgeTag(user = user)

                // VIP标签
                if (user.isVip) {
                    VipCrownTag()
                }

                if (user is IUserDecorations) {
                    ExpLevelWidget(user)
                }
            }
        }
    }
}

/**
 * 钻石卡片
 */
@Composable
private fun DiamondCard(
    count: String,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
) {
    Box(
        modifier =
            modifier
                .clip(RoundedCornerShape(24.dp))
                .clickable(onClick = onClick)
                .background(color = Color(0xFFD3FFD5))
                .padding(
                    start = 16.dp,
                    end = 10.dp,
                ),
    ) {
        // 钻石图标
        Icon(
            painter = painterResource(R.drawable.ic_green_diamond_tilted),
            contentDescription = "钻石",
            tint = Color.Unspecified,
            modifier =
                Modifier
                    .size(40.dp)
                    .align(Alignment.CenterEnd),
        )

        // 文字内容
        Column(
            modifier = Modifier.align(Alignment.CenterStart),
        ) {
            Text(
                text = "充值钻石".localized,
                style = MaterialTheme.typography.labelLarge,
                color = Color(0xFF0B570E),
            )
            SizeHeight(6.dp)
            Text(
                text = count,
                color = Color(0xFF0B570E),
                style = MaterialTheme.typography.titleSmall,
                fontFamily = FontFamily.MI_SANS,
            )
        }
    }
}

/**
 * VIP会员卡片
 */
@Composable
private fun VipCard(
    desc: String,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
) {
    Box(
        modifier =
            modifier
                .clip(RoundedCornerShape(24.dp))
                .clickable(onClick = onClick)
                .background(Color(0xFFFFFBD3))
                .padding(
                    start = 16.dp,
                    end = 5.dp,
                ),
    ) {
        // 皇冠图标
        Icon(
            painter = painterResource(R.drawable.ic_crown),
            contentDescription = "VIP",
            tint = Color.Unspecified,
            modifier =
                Modifier
                    .size(40.dp)
                    .align(Alignment.CenterEnd),
        )

        // 文字内容
        Column(
            modifier = Modifier.align(Alignment.CenterStart),
        ) {
            Text(
                text = "WakooVIP",
                style = MaterialTheme.typography.labelLarge,
                color = Color(0xFF674E0F),
            )
            SizeHeight(6.dp)
            Text(
                text = desc,
                style = MaterialTheme.typography.labelLarge,
                color = Color(0xFF674E0F),
                fontWeight = FontWeight.Normal,
            )
        }
    }
}

@Composable
private fun MineAbilityTab(
    title: String,
    @DrawableRes icon: Int,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.click(noEffect = true, onClick = onClick),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Image(painter = painterResource(icon), modifier = Modifier.size(36.dp), contentDescription = title)
        SizeHeight(4.dp)
        Text(title, fontWeight = FontWeight.Medium, fontSize = 11.sp, lineHeight = 11.sp, color = Color(0xff111111))
    }
}

@Preview(showBackground = true)
@Composable
private fun MineTabPagePreview() {
    WakooTheme {
        MineTabPage()
    }
}
