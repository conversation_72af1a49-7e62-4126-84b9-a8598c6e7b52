package com.buque.wakoo.ui.screens.home

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.ContentTransform
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.union
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LifecycleStartEffect
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.bean.PageInfo
import com.buque.wakoo.bean.UIConfig
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.ext.LaunchOnceEffect
import com.buque.wakoo.ext.backToHome
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.parseValue
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.UserManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.AppNavDisplay
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.BottomNavController
import com.buque.wakoo.navigation.CtrlKey
import com.buque.wakoo.navigation.HomeBottomNavKey
import com.buque.wakoo.navigation.HomeTabRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.appEntry
import com.buque.wakoo.navigation.appEntryProvider
import com.buque.wakoo.navigation.dialog.easyPostBottomPanel
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.navigation.rememberBottomNavController
import com.buque.wakoo.network.api.bean.VoiceRoomCreateReq
import com.buque.wakoo.network.api.service.VoiceRoomApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.repository.UserRepository
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.screens.japan.boost.BoostTabPage
import com.buque.wakoo.ui.screens.japan.boost.ShowTodaySignDialog
import com.buque.wakoo.ui.screens.liveroom.LiveMicMode
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import com.buque.wakoo.ui.screens.liveroom.panel.EditRoomInfoPanel
import com.buque.wakoo.ui.screens.liveroom.toRoomUser
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.CommonPendantBanner
import com.buque.wakoo.ui.widget.drag.FloatingLayoutManager
import com.buque.wakoo.utils.eventBus.EventBusEffect
import com.buque.wakoo.utils.eventBus.tryToLink

object HomeBottomNavCtrlKey : CtrlKey<BottomNavController<HomeBottomNavKey>>

private val TOP_LEVEL_ROUTES: List<HomeBottomNavKey> =
    listOf(
        HomeTabRoute.Home,
        HomeTabRoute.Discover,
        HomeTabRoute.Message,
        HomeTabRoute.Mine,
    )

private val JP_TOP_LEVEL_ROUTES: List<HomeBottomNavKey> =
    listOf(
        HomeTabRoute.Home,
        HomeTabRoute.Task,
        HomeTabRoute.Discover,
        HomeTabRoute.Message,
        HomeTabRoute.Mine,
    )

private val fadeTransitionSpec: AnimatedContentTransitionScope<*>.() -> ContentTransform = {
    ContentTransform(
        fadeIn(animationSpec = tween(300)),
        fadeOut(animationSpec = tween(300)),
    )
}

@SuppressLint("UnusedMaterial3ScaffoldPaddingParameter")
@Composable
fun HomeHostScreen(
    toEditUserInfo: () -> Unit = {},
    toSettings: () -> Unit = {},
    toUserRelations: (Int) -> Unit = {},
    toPublishVoice: () -> Unit = {},
    toLiveRoom: (BasicRoomInfo) -> Unit = {},
    onRechargeClick: () -> Unit = {},
    onMemberClick: () -> Unit = {},
    toDressUpShop: () -> Unit = { },
    toDressUpCenter: () -> Unit = { },
    toSearchScreen: () -> Unit = {},
    toC2CChat: (User) -> Unit = {},
    onTabChanged: (HomeBottomNavKey) -> Unit = {},
    toChatGroup: (id: String?) -> Unit = {},
    onNavigateTo: (AppNavKey) -> Unit = {},
) {
    val config by AppConfigManager.uiConfigFlow.collectAsState()
    val cachedConfig = remember(Unit) {
        AppConfigManager.getCachedUserConfig()
    }
    val controller =
        rememberBottomNavController<HomeBottomNavKey>(
            HomeBottomNavCtrlKey,
            if (cachedConfig?.landingPage?.pageType == PageInfo.DISCOVER) HomeTabRoute.Discover else HomeTabRoute.Home,
        )
    val context = LocalContext.current
    var tabChangedByUser by rememberSaveable { mutableStateOf(false) }
    LifecycleStartEffect(Unit) {
        onStopOrDispose {
            //离开首页不再切tab
            tabChangedByUser = true
        }
    }
    val landed by AppConfigManager.landState
    if (!tabChangedByUser && !landed) {
        LaunchOnceEffect(config.landingPage) {
            if (!config.isRemoteData || config == UIConfig.EMPTY) {
                return@LaunchOnceEffect
            }
            val lp = config.landingPage
            val route = when (lp.pageType) {
                PageInfo.DISCOVER -> HomeTabRoute.Discover
                else -> HomeTabRoute.Home
            }
            if (route != controller.topNavKey) {
                controller.pushToTop(route)
            }
            AppConfigManager.landState.value = true
        }
    }

    val repo = remember { UserRepository() }
    val selfUserInfo = LocalSelfUserProvider.current
    val isCN = selfUserInfo.isCN
    LaunchedEffect(repo, isCN) {
        if (!isCN) return@LaunchedEffect
        repo
            .getNextAction()
            .onSuccess { obj ->
                obj.parseValue<String>("toast")?.let {
                    showToast(it)
                }
                obj.parseValue<String>("next_action")?.tryToLink()
            }
    }

    ShowTodaySignDialog()

    val dialogController = rememberDialogController()
    val scope = rememberCoroutineScope()
    val selfUser = LocalSelfUserProvider.current
    val loading = LocalLoadingManager.current

    EventBusEffect<HomeBottomNavKey> {
        controller.pushToTop(it)
    }
    BackHandler {
        context.backToHome()
    }
    LocalAppNavController.ProvideController(controller) {
        Scaffold(
            modifier = Modifier.fillMaxSize(),
            bottomBar = {
                BottomNavBar(
                    currentNavKey = controller.topNavKey,
                ) {
                    tabChangedByUser = true
                    onTabChanged(it)
                    controller.pushToTop(it)
                }
            },
        ) { paddingValues ->
            FloatingLayoutManager(modifier = Modifier.padding(bottom = paddingValues.calculateBottomPadding())) {
                AppNavDisplay(
                    backStack = controller.backStack,
                    onBack = {},
                    entryProvider =
                        appEntryProvider {
                            appEntry<HomeTabRoute.Home> {
                                SquareTabPage(
                                    toSearchScreen = toSearchScreen,
                                    toPublishVoice = toPublishVoice,
                                )

                                CommonPendantBanner(position = 302)
                            }
                            appEntry<HomeTabRoute.Task> {
                                BoostTabPage()

                                CommonPendantBanner(position = 306)
                            }
                            appEntry<HomeTabRoute.Discover> {
                                DiscoverPage(
                                    onCreateLiveRoom = {
                                        dialogController.easyPostBottomPanel {
                                            EditRoomInfoPanel(
                                                title = "创建电台直播间".localized,
                                                buttonText = "创建直播".localized,
                                            ) { edit ->
                                                loading.show(scope) {
                                                    executeApiCallExpectingData {
                                                        VoiceRoomApiService.instance.createVoiceRoom(
                                                            VoiceRoomCreateReq(
                                                                title = edit.title,
                                                                desc = edit.desc,
                                                                tag_ids = edit.tagIds.joinToString(","),
                                                            ),
                                                        )
                                                    }.onSuccess {
                                                        UserManager.refreshSelfUserInfo().join()
                                                        dismiss()

                                                        toLiveRoom(
                                                            BasicRoomInfo(
                                                                id = it.roomId,
                                                                publicId = it.publicId,
                                                                title = edit.title,
                                                                owner = selfUser.toRoomUser(),
                                                                roomMode = LiveRoomMode.UnKnown(-1),
                                                                micMode = LiveMicMode.UnKnown(-1),
                                                                notice = null,
                                                                desc = edit.desc,
                                                                background = null,
                                                                tagIds = null,
                                                            ),
                                                        )
                                                    }
                                                }
                                            }
                                        }
                                    },
                                    onJoinRoom = toLiveRoom,
                                    toNavigate = onNavigateTo,
                                )

                                CommonPendantBanner(position = 303)
                            }
                            appEntry<HomeTabRoute.Message> {
                                MessagePage(
                                    toC2CChat = toC2CChat,
                                    toChatGroup = toChatGroup,
                                    toNavigate = onNavigateTo,
                                )

                                CommonPendantBanner(position = 304)
                            }
                            appEntry<HomeTabRoute.Mine> {
                                MineTabPage(
                                    toEditUserInfo = toEditUserInfo,
                                    toSettings = toSettings,
                                    toUserRelations = toUserRelations,
                                    onAbilityClick = {
                                        when (it) {
                                            MineAbility.DIAMOND -> {
                                                onRechargeClick()
                                            }

                                            MineAbility.MEMBER -> {
                                                onMemberClick()
                                            }

                                            MineAbility.DRESSUP_SHOP -> {
                                                toDressUpShop()
                                            }

                                            MineAbility.DRESSUP_CENTER -> {
                                                toDressUpCenter()
                                            }
                                        }
                                    },
                                    onNavigateTo = onNavigateTo,
                                )

                                CommonPendantBanner(position = 305)
                            }
                        },
                    transitionSpec = fadeTransitionSpec,
                    popTransitionSpec = fadeTransitionSpec,
                )
            }
        }
    }
}

@Composable
private fun BottomNavBar(
    currentNavKey: HomeBottomNavKey,
    onTabSelected: (HomeBottomNavKey) -> Unit,
) {
    val scope = rememberCoroutineScope()
    Row(
        modifier =
            Modifier
                .fillMaxWidth()
                .background(color = Color.White)
                .padding(start = 10.dp, bottom = 5.dp, end = 10.dp)
                .windowInsetsPadding(
                    WindowInsets.navigationBars
                        .union(WindowInsets(0.dp, 0.dp, 0.dp, 10.dp))
                        .only(WindowInsetsSides.Bottom),
                ),
        verticalAlignment = Alignment.Bottom,
        horizontalArrangement = Arrangement.SpaceAround,
    ) {
        val u = LocalSelfUserProvider.current
        val tabs = if (u.isJP) JP_TOP_LEVEL_ROUTES else TOP_LEVEL_ROUTES
        tabs.forEachIndexed { index, route ->
            route.tab.TabContent(
                selected = currentNavKey.tab == route.tab,
                modifier =
                    Modifier
                        .noEffectClick {
                            onTabSelected(route)
                        }.padding(vertical = 8.dp, horizontal = 5.dp),
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun HomeScreenPreview() {
    WakooTheme {
        HomeHostScreen()
    }
}
