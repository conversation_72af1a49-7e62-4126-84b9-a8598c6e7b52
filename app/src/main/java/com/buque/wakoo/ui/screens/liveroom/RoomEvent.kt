package com.buque.wakoo.ui.screens.liveroom

import androidx.compose.runtime.Composable
import com.buque.wakoo.bean.GiftBean
import com.buque.wakoo.bean.LiveRoomEditInfo
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.navigation.dialog.DialogDestination
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.dialog.AnyPopDialogProperties
import com.buque.wakoo.ui.dialog.DirectionState
import com.buque.wakoo.ui.widget.gift.GiftPosition
import com.buque.wakoo.ui.widget.gift.GiftSendParams
import kotlinx.coroutines.CompletableDeferred

sealed interface RoomEvent {
    sealed interface Dialog : RoomEvent {
        val dialogProperties: AnyPopDialogProperties
        val name: String
        val content: @Composable DialogScope.(LiveRoomInfoState) -> Unit
    }

    data class CustomDialog(
        override val dialogProperties: AnyPopDialogProperties =
            AnyPopDialogProperties(useSystemDialog = false, direction = DirectionState.CENTER),
        override val name: String = "",
        override val content: @Composable DialogScope.(LiveRoomInfoState) -> Unit,
    ) : Dialog

    data class PanelDialog(
        override val dialogProperties: AnyPopDialogProperties =
            AnyPopDialogProperties(
                useSystemDialog = false,
                useCustomAnimation = true,
            ),
        override val name: String = "",
        override val content: @Composable DialogScope.(LiveRoomInfoState) -> Unit,
    ) : Dialog

    data class DestinationDialog(
        val destination: DialogDestination,
    ) : RoomEvent

    data object CollapseRoom : RoomEvent

    data object ExitRoom : RoomEvent

    data class At(
        val user: User,
    ) : RoomEvent

    data class FollowUser(
        val userId: String,
    ) : RoomEvent

    data class AddFriend(
        val userId: String,
        val sceneType: Int = -1,
        val sceneId: Int = -1,
    ) : RoomEvent

    data class EditRoomInfo(
        val edit: LiveRoomEditInfo,
    ) : RoomEvent

    data class UpdateMicMode(
        val value: String,
    ) : RoomEvent

    data class UpdateRoomMode(
        val value: String,
    ) : RoomEvent

    data class UpMic(
        val index: Int,
    ) : RoomEvent

    data object DownMic : RoomEvent

    data object ToggleMic : RoomEvent

    data class KickMic(
        val userId: String,
    ) : RoomEvent

    data class InviteUpMic(
        val userId: String,
    ) : RoomEvent

    /**
     * 管理员处理申请上麦
     */
    data class HandleMicReq(
        val userId: String,
        val agree: Boolean,
    ) : RoomEvent

    /**
     * 取消上麦申请
     */
    data object CancelMicReq : RoomEvent

    /**
     * 清空心动值
     */
    data object ClearLoveValue : RoomEvent

    data class AgreeUpMic(
        val from: Int, // 1=来自邀请上麦，2=来自申请上麦，管理员同意后，接受上麦
    ) : RoomEvent

    /**
     * 设置黑名单
     */
    data class SetBlackEvent(
        val userId: String,
        val black: Boolean,
    ) : RoomEvent

    /**
     * 设置管理员
     */
    data class SetAdminEvent(
        val userId: String,
        val admin: Boolean,
    ) : RoomEvent

    data class OpenGiftPanel(
        val position: GiftPosition? = null,
        val onlyUser: User? = null,
    ) : RoomEvent

    data class SendGift(
        val targets: List<String>,
        val gift: GiftBean,
        val params: GiftSendParams,
    ) : RoomEvent

    /**
     * type = 1 开始
     * type = 2 加时
     * type = 3 重开
     */
    data class OnPkSettingsEvent(
        val type: Int,
        val title: String? = null,
        val duration: Int? = null,
    ) : RoomEvent

    data object EditHongBao : RoomEvent

    data class ShowHongBao(
        val id: String,
    ) : RoomEvent

    /**
     * 关闭指定弹窗
     */
    data class CloseDialog(
        val id: Int,
    ) : RoomEvent

    /**
     * 设置或取消房间密码
     */
    data class SetRoomPassword(
        val dialogId: Int,
        val password: String? = null, // null表示取消密码
    ) : RoomEvent

    /**
     * 验证房间密码
     */
    data class VerifyRoomPassword(
        val dialogId: Int,
        val password: String,
        val validateDeferred: CompletableDeferred<Boolean>,
    ) : RoomEvent
}
