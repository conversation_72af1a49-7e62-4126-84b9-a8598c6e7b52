package com.buque.wakoo.ui.theme

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

object WakooUI {

    data object  Shapes{
        val extraSmall = RoundedCornerShape(8.dp)
        val small = RoundedCornerShape(10.dp)
        val corner12 = RoundedCornerShape(12.dp)
        val chip = RoundedCornerShape(50)
    }

    data object Colors{
        val colorWhite50Alpha = Color.White.copy(0.5f)
    }
}
