package com.buque.wakoo.ui.screens.liveroom

import android.os.SystemClock
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.CompactJson
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.AudioRoomAudienceBean
import com.buque.wakoo.core.webview.WebFrameInfo
import com.buque.wakoo.ext.LaunchOnceEffect
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.formatSecondsDuration
import com.buque.wakoo.ext.hasRanAtToday
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.dialog.NamedDialogDestination
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.easyPostBottomPanel
import com.buque.wakoo.navigation.dialog.rememberDialogControllerWithParamsScope
import com.buque.wakoo.ui.icons.AudienceTaskAlarm
import com.buque.wakoo.ui.icons.AudienceTaskGift
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.WebViewModel
import com.buque.wakoo.ui.screens.messages.chat.BlindBoxTaskPendant
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.widget.AutoSizeText
import com.buque.wakoo.ui.widget.CallTimer
import com.buque.wakoo.ui.widget.ComposeSwiperData
import com.buque.wakoo.ui.widget.DraggableSwiperItem
import com.buque.wakoo.ui.widget.drag.FloatingLayoutManager
import com.buque.wakoo.ui.widget.drag.FloatingLayoutScope
import com.buque.wakoo.ui.widget.drag.rememberDraggableFloatingState
import com.buque.wakoo.ui.widget.gift.ChatroomGiftPanel
import com.buque.wakoo.ui.widget.gift.GiftScene
import com.buque.wakoo.ui.widget.gift.GiftViewModel
import com.buque.wakoo.utils.eventBus.tryToLink
import com.buque.wakoo.viewmodel.AudioRoomDailyViewModel
import com.buque.wakoo.viewmodel.HongBaoViewModel
import com.buque.wakoo.viewmodel.liveroom.LiveRoomViewModel
import com.buque.webview.AppBridgeWebView
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import java.net.URLEncoder

/**
 * 任务挂件
 */
@Composable
fun FloatingLayoutScope.TaskBannerWidgets(
    roomInfoState: LiveRoomInfoState,
    audienceViewModel: AudioRoomDailyViewModel,
    webViewModel: WebViewModel,
) {
    //region 进入时显示popup
    val showAudienceTask = roomInfoState.extraInfo.showAudienceTaskPopup
    val openTaskDialog = {
        val targetUrl = "${EnvironmentManager.current.apiUrl}h5/japan/room_task?room_id=${roomInfoState.id}"
        val json =
            CompactJson.encodeToString(
                WebFrameInfo(
                    targetUrl = targetUrl,
                    height = 620,
                    gravity = "bottom",
                ),
            )
        "wakoo://page/web_frame?info=${URLEncoder.encode(json)}".tryToLink()
    }
    LaunchOnceEffect(showAudienceTask) {
        if (showAudienceTask && !hasRanAtToday("${roomInfoState.id}-last_room_popup_opened")) {
            openTaskDialog()
        }
    }
    //endregion

    val position = 307
    val dataState = AppConfigManager.flowPendantList.collectAsStateWithLifecycle()
    val audienceTaskBean by audienceViewModel.dailyTask.collectAsStateWithLifecycle()
    val bannerList by remember(dataState, audienceTaskBean) {
        derivedStateOf {
            buildList {
                if (SelfUser?.isJP == true && audienceTaskBean != null) {
                    add(
                        ComposeSwiperData {
                            DailyBannerWidget(
                                modifier =
                                    Modifier
                                        .fillMaxSize()
                                        .click {
                                            openTaskDialog()
                                        },
                                audienceTaskBean!!,
                            )
                        },
                    )
                }
                addAll(dataState.value.filter { it.isH5Pic && it.support(position) })
            }
        }
    }

    val initialOffsetDp = DpOffset(x = 0.dp, y = (-84).dp)

    if (bannerList.isNotEmpty()) {
        DraggableSwiperItem(
            bannerList,
            initialOffsetDp,
            webViewModel = webViewModel,
            initialAlignment = Alignment.BottomEnd,
            closable = false,
            position = position,
            useLoopPageState = false,
        )
    }
}

@Composable
fun LiveRoomOverlayLayout(
    roomInfoState: LiveRoomInfoState,
    viewModel: LiveRoomViewModel,
    giftViewModel: GiftViewModel,
    hongBaoViewModel: HongBaoViewModel,
) {
    val blindBoxState by roomInfoState.blindBoxState
    val blindBoxVisible by AppConfigManager.bboxVisibleState

    FloatingLayoutManager {
        val position = 307
        val initialOffsetDp = DpOffset(x = 0.dp, y = (-84).dp)
        val dataState = AppConfigManager.flowPendantList.collectAsStateWithLifecycle()

        val context = LocalContext.current
        val web =
            remember {
                AppBridgeWebView(context).also {
                    it.distinctLoadUrl = true
                }
            }

        val webViewModel =
            viewModel<WebViewModel>(initializer = {
                WebViewModel(store = { web })
            })

        val audienceViewModel =
            viewModel<AudioRoomDailyViewModel>(initializer = {
                AudioRoomDailyViewModel()
            })

        TaskBannerWidgets(roomInfoState, audienceViewModel, webViewModel)

        val nativeList by remember(
            dataState,
        ) {
            derivedStateOf {
                buildList {
                    addAll(dataState.value.filter { !it.isH5Pic && it.support(position) })

                    if (blindBoxVisible && blindBoxState != null && blindBoxState!!.showBlindboxTask) {
                        add(
                            ComposeSwiperData {
                                // 盲盒任务挂件
                                BlindBoxTaskPendant(blindBoxState!!)
                            },
                        )
                    }
                }
            }
        }

        if (nativeList.isNotEmpty()) {
            DraggableSwiperItem(
                nativeList,
                initialOffsetDp.minus(DpOffset(x = 0.dp, y = 100.dp)),
                webViewModel = webViewModel,
                useLoopPageState = false,
                initialAlignment = Alignment.BottomEnd,
                position = position,
            )
        }

        val config by AppConfigManager.uiConfigFlow.collectAsState()
        if (config.packetEnabled) {
            val hbList by hongBaoViewModel.hongBaoListState
            val floatingState =
                rememberDraggableFloatingState(
                    initialAlignment = Alignment.BottomEnd,
                    initialOffsetDp = initialOffsetDp.minus(DpOffset(x = 0.dp, y = 200.dp)),
                    initialIsVisible = true,
                    initialIsStickyToEdge = true,
                    allowDragOutOfBounds = true,
                )
            DraggableItem(floatingState) {
                HongBaoPendantContainer(
                    hbList,
                    modifier =
                        Modifier
                            .width(50.dp)
                            .navigationBarsPadding()
                            .padding(end = 8.dp),
                ) {
                    roomInfoState.sendEvent(RoomEvent.ShowHongBao(it.id))
                }
            }
        }
    }

    val dialogController = rememberDialogControllerWithParamsScope(roomInfoState)

    LaunchedEffect(dialogController, roomInfoState) {
        roomInfoState.events
            .onEach { event ->
                when (event) {
                    is RoomEvent.Dialog -> {
                        dialogController.easyPost(
                            name = event.name,
                            dialogProperties = event.dialogProperties,
                            content = {
                                event.content(this, it as LiveRoomInfoState)
                            },
                        )
                    }

                    is RoomEvent.DestinationDialog -> {
                        dialogController.post(event.destination)
                    }

                    is RoomEvent.At -> {
                        viewModel.setInputTextState(InputTextState.Visible("@${event.user.name} "))
                    }

                    is RoomEvent.OpenGiftPanel -> {
                        giftViewModel.fetchGiftData()
                        dialogController.easyPostBottomPanel(useSystemDialog = false) {
                            ChatroomGiftPanel(
                                position = null,
                                onlyUser = event.onlyUser,
                                giftListModelState = giftViewModel.giftListModelState,
                                roomInfoState = roomInfoState,
                            ) { gift, param, users ->
                                giftViewModel.sendGiftAt(
                                    giftScene = if (roomInfoState.isPrivateRoom) GiftScene.Private else GiftScene.ROOM,
                                    targets = users.map { it.id },
                                    gift = gift,
                                    params = param,
                                )
                            }
                        }
                    }

                    is RoomEvent.SendGift -> {
                        giftViewModel.sendGiftAt(
                            giftScene = if (roomInfoState.isPrivateRoom) GiftScene.Private else GiftScene.ROOM,
                            targets = event.targets,
                            gift = event.gift,
                            params = event.params,
                        )
                    }

                    is RoomEvent.EditHongBao -> {
                        viewModel.sendEvent(
                            RoomEvent.PanelDialog(name = "edit_hb") { roomInfoState ->
                                HongBaoEditPanel(hbVm = hongBaoViewModel, onDismiss = {
                                    dismiss()
                                })
                            },
                        )
                    }

                    is RoomEvent.ShowHongBao -> {
                        dialogController.dismiss { dest ->
                            dest is NamedDialogDestination && dest.name == "hongbao"
                        }
                        viewModel.sendEvent(
                            RoomEvent.CustomDialog(name = "hongbao") {
                                HongBaoDialogContent(event.id, hongBaoViewModel, onDismiss = {
                                    dismiss()
                                })
                            },
                        )
                    }

                    is RoomEvent.CloseDialog -> {
                        dialogController.dismiss(id = event.id, null)
                    }

                    else -> Unit
                }
            }.launchIn(this)
    }

    giftViewModel.GiftEffectView()
}

//region 语音房听众任务(每日任务)
@Composable
private fun DailyBannerWidget(
    modifier: Modifier = Modifier,
    dailyTask: AudioRoomAudienceBean,
) {
    Box(
        modifier =
            Modifier.background(
                brush =
                    Brush.verticalGradient(
                        listOf(
                            Color(0xff85FFD6),
                            Color(0xff54F47F),
                        ),
                    ),
                shape = RoundedCornerShape(12.dp),
            ),
    ) {
        Image(
            painter = painterResource(R.drawable.ic_room_audience_rili),
            modifier =
                Modifier
                    .height(36.dp)
                    .aspectRatio(49 / 81f),
            contentDescription = null,
        )
        Column(
            modifier =
                modifier
                    .size(68.dp, 90.dp)
                    .padding(vertical = 8.dp),
        ) {
            AutoSizeText(
                "语音房\n每日任务".localized,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .weight(1f),
                fontWeight = FontWeight(900),
                fontFamily = FontFamily.MI_SANS,
                color = Color(0xFF047767),
                textAlign = TextAlign.Center,
                minTextSize = 10.sp,
                maxTextSize = 12.sp,
                lineHeight = 13.sp,
                maxLines = 2,
            )
            Row(
                modifier =
                    Modifier
                        .padding(horizontal = 6.dp)
                        .fillMaxWidth()
                        .border(width = 1.dp, color = Color(0xFF29E9BF), shape = RoundedCornerShape(size = 2.dp))
                        .background(color = Color.White, shape = RoundedCornerShape(2.dp))
                        .padding(2.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                Image(WakooIcons.AudienceTaskAlarm, contentDescription = null)
                val startTime =
                    remember(dailyTask) {
                        SystemClock.elapsedRealtime().minus(dailyTask.interactTask.interactSeconds.times(1000))
                    }
                if (dailyTask.interactTask.interactStatus == 1) {
                    CallTimer(isCountdown = false, elapsedRealtime = startTime) {
                        AutoSizeText(
                            text = it.formatSecondsDuration,
                            color = Color(0xff047767),
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Bold,
                            minTextSize = 10.sp,
                            maxTextSize = 12.sp,
                        )
                    }
                } else {
                    AutoSizeText(
                        text =
                            dailyTask.interactTask.interactSeconds
                                .times(1000)
                                .formatSecondsDuration,
                        color = Color(0xff047767),
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Bold,
                        minTextSize = 10.sp,
                        maxTextSize = 12.sp,
                    )
                }
            }
            Row(
                modifier =
                    Modifier
                        .padding(horizontal = 6.dp)
                        .padding(top = 6.dp)
                        .fillMaxWidth()
                        .border(width = 1.dp, color = Color(0xFF29E9BF), shape = RoundedCornerShape(size = 2.dp))
                        .background(color = Color.White, shape = RoundedCornerShape(2.dp))
                        .padding(2.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                Image(WakooIcons.AudienceTaskGift, contentDescription = null)

                Text(
                    "${dailyTask.giftTask.goldCoinCnt}",
                    color = Color(0xff047767),
                    fontSize = 12.sp,
                    lineHeight = 12.sp,
                    fontWeight = FontWeight.Bold,
                )
            }
        }
    }
}

@Preview
@Composable
private fun DailyBannerWidgetPreview() {
    DailyBannerWidget(
        dailyTask =
            AudioRoomAudienceBean(
                giftTask = AudioRoomAudienceBean.GiftTask(5),
                interactTask =
                    AudioRoomAudienceBean.InteractTask(
                        interactStatus = 1,
                        interactSeconds = 300,
                    ),
            ),
    )
}

//endregion
