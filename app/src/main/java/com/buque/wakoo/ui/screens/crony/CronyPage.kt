package com.buque.wakoo.ui.screens.crony

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.utils.eventBus.AppEvent
import com.buque.wakoo.utils.eventBus.EventBus
import com.buque.wakoo.utils.eventBus.tryToLink

@Composable
fun CronyPage(
    userId: String,
    settingsViewModel: CronySettingsViewModel,
    modifier: Modifier = Modifier,
    showBottomButtons: Boolean = false,
) {
    val nav = LocalAppNavController.root
    val self = LocalSelfUserProvider.current
    val tagsViewModel = viewModel<CronyTagsViewModel>(initializer = { CronyTagsViewModel(self.nativeRegion) })
    val state by settingsViewModel.state
    val data = state.dataOrNull ?: return
    val lm = LocalLoadingManager.current
    val showIntimacy = self.isCN
    CronyMainCard(
        modifier = modifier,
        showBottomButtons = showBottomButtons,
        unlockEnable = data.settings.relativeSeatMostPaidCnt > data.settings.relativeSeatAvailableCnt - data.settings.relativeSeatFreeCnt,
        onUnlock = {
            EventBus.trySend(AppEvent.CustomDialog {
                UnLockCronyPositionDialogContent(data.settings.relativeSeatPrice) { count: Int ->
                    lm.show(null) {
                        settingsViewModel.buySeat(count)
                            .onSuccess {
                                showToast("亲友团席位购买成功".localized)
                                settingsViewModel.refreshState()
                                dismiss()
                            }
                    }
                }
            })
        },
        onSetting = {
            nav.push(Route.CronySetting)
        },
        onQuestionClick = {
            data.settings.relationshipRulePageLink.tryToLink()
        }, content = {
            LazyVerticalGrid(
                modifier = Modifier.fillMaxSize().padding(bottom = 10.dp),
                columns = GridCells.Fixed(3),
                horizontalArrangement = Arrangement.spacedBy(10.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp)
            ) {
                items(data.items) { item ->
                    when (item) {
                        is CronyItem -> {
                            CronyItemCard(item, showIntimacy, modifier = Modifier.fillMaxWidth())
                        }

                        is EmptyCronyItem -> {
                            CronyEmptyCard(modifier = Modifier.fillMaxWidth(), onClick = {
                                showSelectTagDialog(tagsViewModel) {
                                    nav.push(
                                        Route.SelectUserV2(
                                            "发送亲友团邀请".localized,
                                            "发送邀请".localized,
                                            Route.SelectUserV2.SELECT_FOR_INVITE_CRONY,
                                            it.id,
                                        )
                                    )
                                }
                            })
                        }

                        else -> {
                            LoadingCronyCard(modifier = Modifier.fillMaxWidth())
                        }
                    }
                }

            }
        })
}

fun showSelectTagDialog(viewModel: CronyTagsViewModel, onConfirm: (CronyTag) -> Unit = {}) {
    EventBus.trySend(AppEvent.CustomDialog {
        val st by viewModel.state
        LaunchedEffect(Unit) {
            viewModel.refreshState()
        }
        CStateLayout(st, modifier = Modifier.width(290.dp)) { list ->
            SelectRelationshipLabelDialog(list) {
                onConfirm.invoke(it)
                dismiss()
            }
        }
    })
}