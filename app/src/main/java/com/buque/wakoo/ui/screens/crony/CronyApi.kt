package com.buque.wakoo.ui.screens.crony

import com.buque.wakoo.network.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query


interface CronyApi {

    @GET("api/xya/tie/v1/intimate/tags")
    suspend fun getTags(@Query("native_region") nativeRegion: Int): ApiResponse<JsonObject>


    @POST("api/xya/tie/v1/intimate/invite")
    suspend fun inviteUser(@Body map: Map<String, String>): ApiResponse<JsonObject>

    //    invited_id
    @POST("api/xya/tie/v1/intimate/accept")
    suspend fun acceptInvite(@Body map: Map<String, String>): ApiResponse<JsonObject>

    @GET("api/xya/tie/v1/intimate/panel")
    suspend fun getSettings(@Query("userid") userId: String): ApiResponse<CronySettings>

    @GET("api/xya/tie/v1/intimate/bonds")
    suspend fun getRelationshipLabelList(@Query("userid") userId: String, @Query("last_intimate_id") id: Int): ApiResponse<JsonObject>

    @POST("api/xya/tie/v1/intimate/slots/unlock")
    suspend fun buyRelationshipSeat(@Body map: Map<String, Int>): ApiResponse<JsonObject>

    //    relationship_id
    @POST("api/xya/tie/v1/intimate/dissolve")
    suspend fun breakUp(@Body map: Map<String, Int>): ApiResponse<JsonObject>
}
