package com.qyqy.ucoo.widget.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.bean.FirstClassNotificationBean
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.utils.eventBus.toLink
import kotlinx.coroutines.launch

/**
 * 头等舱开通欢迎弹窗(用户激活头等舱时不在app内, 重新打开app时显示)
 *
 */
@Composable
fun FirstClassWelcomeWidget(
    bean: FirstClassNotificationBean,
    dismiss: () -> Unit = {},
) {
    Box {
        Column(
            modifier =
                Modifier
                    .padding(top = 69.dp)
                    .background(color = Color(0xFF615A53), shape = RoundedCornerShape(12.dp))
                    .paint(
                        painter = painterResource(id = R.drawable.ic_fc_welcome_bg),
                        contentScale = ContentScale.FillBounds,
                    ).border(
                        1.dp,
                        brush = Brush.verticalGradient(listOf(Color(0xffFFF0C5), Color(0xff615A53))),
                        shape = RoundedCornerShape(12.dp),
                    ),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            NetworkImage(
                bean.user.avatar,
                modifier =
                    Modifier
                        .padding(top = 38.dp)
                        .size(64.dp)
                        .border(1.dp, Color.White, CircleShape)
                        .clip(CircleShape),
                contentScale = ContentScale.FillWidth,
            )

            Text(bean.user.name, color = Color(0xffFFD895), modifier = Modifier.padding(8.dp), fontSize = 14.sp, lineHeight = 14.sp)
            NetworkImage(
                R.drawable.ic_fc_welcome_title,
                modifier =
                    Modifier
                        .padding(top = 24.dp, start = 24.dp, end = 24.dp)
                        .fillMaxWidth()
                        .heightIn(max = 48.dp),
                contentScale = ContentScale.FillWidth,
            )
            Text(
                bean.no,
                color = Color(0xffFFD895),
                fontSize = 16.sp,
                lineHeight = 30.sp,
                modifier =
                    Modifier
                        .padding(top = 12.dp)
                        .paint(painter = painterResource(id = R.drawable.ic_fc_number_bg)),
                textAlign = TextAlign.Center,
            )
            Text(
                bean.days,
                color = Color(0xffFFD895),
                fontSize = 12.sp,
                lineHeight = 12.sp,
                modifier = Modifier.padding(top = 12.dp),
                textAlign = TextAlign.Center,
            )

            Text(
                modifier =
                    Modifier
                        .padding(top = 24.dp, bottom = 20.dp)
                        .size(156.dp, 36.dp)
                        .background(
                            brush =
                                Brush.verticalGradient(
                                    listOf(Color(0xFFFFF2A7), Color(0xFFFFB04A)),
                                ),
                            shape = CircleShape,
                        ).click {
                            dismiss()
                        },
                text = "好的".localized,
                fontSize = 16.sp,
                lineHeight = 36.sp,
                textAlign = TextAlign.Center,
                color = Color(0xff7E3E06),
                fontWeight = FontWeight.Medium,
            )
        }
        NetworkImage(
            R.drawable.ic_fc_welcome_top,
            modifier =
                Modifier
                    .align(Alignment.TopCenter)
                    .height(100.dp),
            contentScale = ContentScale.FillHeight,
        )
    }
}

/**
 * 头等舱开通事件弹窗
 *
 */
@Composable
fun FirstClassActiveWidget(
    bean: FirstClassNotificationBean,
    dismiss: () -> Unit = {},
) {
    Box {
        Column(
            modifier =
                Modifier
                    .height(424.dp)
                    .paint(painterResource(id = R.drawable.ic_fc_intime_bg), contentScale = ContentScale.Fit),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            NetworkImage(
                bean.user.avatar,
                modifier =
                    Modifier
                        .padding(top = 64.dp)
                        .size(72.dp)
                        .clip(CircleShape),
            )
            Text(
                bean.user.name,
                color = Color(0xff975821),
                modifier = Modifier.padding(top = 8.dp),
                fontSize = 16.sp,
                lineHeight = 16.sp,
            )
            Text(
                bean.no,
                modifier =
                    Modifier
                        .padding(top = 14.dp)
                        .size(148.dp, 30.dp)
                        .paint(painter = painterResource(id = R.drawable.ic_fc_intime_number_bg)),
                fontSize = 18.sp,
                lineHeight = 30.sp,
                textAlign = TextAlign.Center,
                color = Color(0xffD94F46),
            )
            Text(
                buildAnnotatedString {
                    append(bean.headMessage)
                    append("\n")
                    append(bean.middleMessage)
                    append("\n")
                    withStyle(SpanStyle(fontSize = 12.sp)) {
                        append(bean.message)
                    }
                },
                color = Color(0xff975821),
                fontSize = 18.sp,
                lineHeight = 23.sp,
                modifier =
                    Modifier
                        .padding(top = 4.dp)
                        .width(240.dp),
            )
            Spacer(modifier = Modifier.weight(1f))
            Text(
                "好的".localized,
                modifier =
                    Modifier
                        .padding(bottom = 32.dp)
                        .height(32.dp)
                        .background(
                            brush =
                                Brush.horizontalGradient(
                                    listOf(Color(0xffeffcde), Color(0xffe2bc94)),
                                ),
                            shape = CircleShape,
                        ).padding(horizontal = 46.dp)
                        .click {
                            dismiss()
                        },
                color = Color(0xff1E1E22),
                fontSize = 16.sp,
                lineHeight = 32.sp,
                textAlign = TextAlign.Center,
            )
        }
        NetworkImage(
            R.drawable.ic_fc_intime_title,
            modifier =
                Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = 8.dp)
                    .fillMaxWidth()
                    .heightIn(max = 32.dp),
            contentScale = ContentScale.FillHeight,
        )
    }
}

@Composable
fun FirstClassGiftTipsWidget(
    msg: String = "",
    dismiss: () -> Unit = {},
) {
    val ctx = LocalContext.current
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .background(
                    brush =
                        Brush.verticalGradient(
                            listOf(
                                Color(0xff2A231C),
                                Color(0xff161616),
                            ),
                        ),
                    shape = RoundedCornerShape(12.dp),
                ).border(
                    1.dp,
                    brush = Brush.verticalGradient(listOf(Color(0xffFFF0C5), Color(0xff615A53))),
                    shape = RoundedCornerShape(12.dp),
                ).padding(horizontal = 16.dp, vertical = 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            msg.ifBlank { "该礼物是头等舱俱乐部成员特权礼物,仅俱乐部成员可赠送".localized },
            color = Color(0xffF9C478),
            fontSize = 15.sp,
            lineHeight = 22.sp,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(horizontal = 8.dp),
        )
        Text(
            modifier =
                Modifier
                    .padding(top = 24.dp)
                    .height(36.dp)
                    .background(
                        brush =
                            Brush.verticalGradient(
                                listOf(Color(0xFFFFF2A7), Color(0xFFFFB04A)),
                            ),
                        shape = CircleShape,
                    ).padding(horizontal = 48.dp)
                    .click {
                        appCoroutineScope.launch {
                            "${EnvironmentManager.current.apiUrl}h5/zh/firstclass".toLink()
                        }
                        dismiss()
                    },
            text = "查看特权".localized,
            fontSize = 16.sp,
            lineHeight = 36.sp,
            textAlign = TextAlign.Center,
            color = Color(0xff7E3E06),
            fontWeight = FontWeight.Medium,
        )
    }
}

@Preview
@Composable
private fun FirstClassWelcomeWidgetPreview() {
    FirstClassWelcomeWidget(FirstClassNotificationBean(user = BasicUser.sampleBoy))
}

@Preview
@Composable
private fun FirstClassActiveWidgetPreview() {
    FirstClassActiveWidget(
        FirstClassNotificationBean(
            headMessage = "恭喜您",
            middleMessage = "成為尊敬的頭等艙俱樂部成員",
            user = BasicUser.sampleBoy,
            message = "所有俱樂部成員特權已解鎖,祝您使用愉快",
        ),
    )
}

@Preview
@Composable
private fun FirstClassGiftTipsWidgetPreview() {
    FirstClassGiftTipsWidget()
}
