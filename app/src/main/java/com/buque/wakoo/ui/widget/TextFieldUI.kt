package com.buque.wakoo.ui.widget

import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsFocusedAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.selection.LocalTextSelectionColors
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldColors
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.TextFieldDefaults.contentPaddingWithLabel
import androidx.compose.material3.TextFieldDefaults.contentPaddingWithoutLabel
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.takeOrElse
import androidx.compose.ui.semantics.error
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.ext.by
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.theme.WakooWhite

@Composable
fun AppTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    readOnly: Boolean = false,
    textStyle: TextStyle = LocalTextStyle.current,
    label: @Composable (() -> Unit)? = null,
    placeholder: @Composable (() -> Unit)? = null,
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    prefix: @Composable (() -> Unit)? = null,
    suffix: @Composable (() -> Unit)? = null,
    supportingText: @Composable (() -> Unit)? = null,
    isError: Boolean = false,
    initialCursorPosition: Int? = null, // 新增的光标配置参数
    visualTransformation: VisualTransformation = VisualTransformation.None,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    singleLine: Boolean = false,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    minLines: Int = 1,
    minWidth: Dp = TextFieldDefaults.MinWidth,
    minHeight: Dp = TextFieldDefaults.MinHeight,
    interactionSource: MutableInteractionSource? = null,
    shape: Shape = TextFieldDefaults.shape,
    contentPadding: PaddingValues =
        if (label == null) {
            contentPaddingWithoutLabel(
                start = TextFieldPadding,
                top = TextFieldPadding,
                end = TextFieldPadding,
                bottom = TextFieldPadding,
            )
        } else {
            contentPaddingWithLabel(
                start = TextFieldPadding,
                end = TextFieldPadding,
            )
        },
    colors: TextFieldColors = TextFieldDefaults.colors(),
) {
    // --- 核心逻辑开始 (参考官方 BasicTextField 源码) ---

    // 1. 内部创建并记住一个完整的 TextFieldValue 状态。
    //    这是整个实现的关键。我们在这里使用 `initialCursorPosition` 来设置初始光标。
    var textFieldValueState by rememberSaveable(stateSaver = TextFieldValue.Saver) {
        val cursorPosition = initialCursorPosition?.coerceIn(0, value.length) ?: value.length
        val initialSelection = TextRange(cursorPosition)
        mutableStateOf(TextFieldValue(text = value, selection = initialSelection))
    }

    // 2. 创建一个新的 TextFieldValue，它使用外部传入的 `value` 字符串，
    //    但保留了我们内部状态的 `selection` (光标位置) 和 `composition`。
    //    这确保了当外部以编程方式更新文本时，用户的光标位置得以保留。
    val textFieldValue = textFieldValueState.copy(text = value)

    // 3. (参考官方) 使用 SideEffect 来同步状态。
    //    这确保了 `textFieldValueState` 总是持有 `textFieldValue` 的最新 selection 和 composition。
    //    这对于处理复杂的输入法（如中文、日文）至关重要。
    SideEffect {
        if (
            textFieldValue.selection != textFieldValueState.selection ||
            textFieldValue.composition != textFieldValueState.composition
        ) {
            textFieldValueState = textFieldValue
        }
    }

    // 4. 创建一个包装好的 onValueChange 回调。
    //    当底层的 BasicTextField 返回新的 TextFieldValue 时，我们用它更新内部状态，
    //    然后只将 `String` 部分回调给外部的 onValueChange 监听器。
    val onValueChangeWrapper: (TextFieldValue) -> Unit = { newTfv ->
        // 更新内部的完整状态
        textFieldValueState = newTfv
        // 只有当文本内容实际发生变化时，才通知外部监听器
        if (value != newTfv.text) {
            onValueChange(newTfv.text)
        }
    }

    // --- 核心逻辑结束 ---

    @Suppress("NAME_SHADOWING")
    val interactionSource = interactionSource ?: remember { MutableInteractionSource() }
    // If color is not provided via the text style, use content color as a default
    val textColor =
        textStyle.color.takeOrElse {
            val focused = interactionSource.collectIsFocusedAsState().value
            colors.textColor(
                enabled,
                isError,
                focused,
            )
        }
    val mergedTextStyle = textStyle.merge(TextStyle(color = textColor))

    CompositionLocalProvider(LocalTextSelectionColors provides colors.textSelectionColors) {
        BasicTextField(
            value = textFieldValue, // 使用我们内部管理的 TextFieldValue
            onValueChange = onValueChangeWrapper, // 使用我们包装过的回调
            modifier =
                modifier
                    .defaultErrorSemantics(
                        isError = isError,
                        defaultErrorMessage = "default_error_message",
                    ).defaultMinSize(
                        minWidth = minWidth,
                        minHeight = minHeight,
                    ),
            enabled = enabled,
            readOnly = readOnly,
            textStyle = mergedTextStyle,
            cursorBrush = SolidColor(colors.cursorColor(isError)),
            visualTransformation = visualTransformation,
            keyboardOptions = keyboardOptions,
            keyboardActions = keyboardActions,
            interactionSource = interactionSource,
            singleLine = singleLine,
            maxLines = maxLines,
            minLines = minLines,
            decorationBox =
                @Composable { innerTextField ->
                    // places leading icon, text field with label and placeholder, trailing icon
                    TextFieldDefaults.DecorationBox(
                        value = value,
                        visualTransformation = visualTransformation,
                        innerTextField = innerTextField,
                        placeholder = placeholder,
                        label = label,
                        leadingIcon = leadingIcon,
                        trailingIcon = trailingIcon,
                        prefix = prefix,
                        suffix = suffix,
                        supportingText = supportingText,
                        shape = shape,
                        contentPadding = contentPadding,
                        singleLine = singleLine,
                        enabled = enabled,
                        isError = isError,
                        interactionSource = interactionSource,
                        colors = colors,
                    )
                },
        )
    }
}

@Composable
fun AppTextField(
    value: TextFieldValue,
    onValueChange: (TextFieldValue) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    readOnly: Boolean = false,
    textStyle: TextStyle = LocalTextStyle.current,
    label: @Composable (() -> Unit)? = null,
    placeholder: @Composable (() -> Unit)? = null,
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    prefix: @Composable (() -> Unit)? = null,
    suffix: @Composable (() -> Unit)? = null,
    supportingText: @Composable (() -> Unit)? = null,
    isError: Boolean = false,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    singleLine: Boolean = false,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    minLines: Int = 1,
    interactionSource: MutableInteractionSource? = null,
    shape: Shape = TextFieldDefaults.shape,
    contentPadding: PaddingValues =
        if (label == null) {
            contentPaddingWithoutLabel(
                start = TextFieldPadding,
                top = TextFieldPadding,
                end = TextFieldPadding,
                bottom = TextFieldPadding,
            )
        } else {
            contentPaddingWithLabel(
                start = TextFieldPadding,
                end = TextFieldPadding,
            )
        },
    colors: TextFieldColors = TextFieldDefaults.colors(),
) {
    @Suppress("NAME_SHADOWING")
    val interactionSource = interactionSource ?: remember { MutableInteractionSource() }
    // If color is not provided via the text style, use content color as a default
    val textColor =
        textStyle.color.takeOrElse {
            val focused = interactionSource.collectIsFocusedAsState().value
            colors.textColor(
                enabled,
                isError,
                focused,
            )
        }
    val mergedTextStyle = textStyle.merge(TextStyle(color = textColor))

    CompositionLocalProvider(LocalTextSelectionColors provides colors.textSelectionColors) {
        BasicTextField(
            value = value,
            modifier =
                modifier
                    .defaultErrorSemantics(
                        isError = isError,
                        defaultErrorMessage = "default_error_message",
                    ).defaultMinSize(
                        minWidth = TextFieldDefaults.MinWidth,
                        minHeight = TextFieldDefaults.MinHeight,
                    ),
            onValueChange = onValueChange,
            enabled = enabled,
            readOnly = readOnly,
            textStyle = mergedTextStyle,
            cursorBrush = SolidColor(colors.cursorColor(isError)),
            visualTransformation = visualTransformation,
            keyboardOptions = keyboardOptions,
            keyboardActions = keyboardActions,
            interactionSource = interactionSource,
            singleLine = singleLine,
            maxLines = maxLines,
            minLines = minLines,
            decorationBox =
                @Composable { innerTextField ->
                    // places leading icon, text field with label and placeholder, trailing icon
                    TextFieldDefaults.DecorationBox(
                        value = value.text,
                        visualTransformation = visualTransformation,
                        innerTextField = innerTextField,
                        placeholder = placeholder,
                        label = label,
                        leadingIcon = leadingIcon,
                        trailingIcon = trailingIcon,
                        prefix = prefix,
                        suffix = suffix,
                        supportingText = supportingText,
                        shape = shape,
                        contentPadding = contentPadding,
                        singleLine = singleLine,
                        enabled = enabled,
                        isError = isError,
                        interactionSource = interactionSource,
                        colors = colors,
                    )
                },
        )
    }
}

@Composable
fun AppTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    boxModifier: Modifier = Modifier,
    placeholder: String = "请输入内容".localized,
    maxLength: Int = Int.MAX_VALUE,
    showLengthTip: Boolean = maxLength < Int.MAX_VALUE,
    initialCursorPosition: Int? = null,
    enabled: Boolean = true,
    readOnly: Boolean = false,
    lineHeight: TextUnit = 21.sp,
    prefix: @Composable (() -> Unit)? = null,
    suffix: @Composable (() -> Unit)? = null,
    textStyle: TextStyle =
        MaterialTheme.typography.bodyMedium.merge(
            color = Color(0xFF111111),
            lineHeight = lineHeight,
        ),
    placeholderStyle: TextStyle =
        textStyle.merge(
            color = Color(0xFFB6B6B6),
        ),
    supportingStyle: TextStyle =
        MaterialTheme.typography.labelLarge.copy(
            color = Color(0xFFB6B6B6),
        ),
    backgroundColor: Color = Color.Unspecified,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    singleLine: Boolean = false,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    minLines: Int = 1,
    minWidth: Dp = TextFieldDefaults.MinWidth,
    minHeight: Dp = TextFieldDefaults.MinHeight,
    shape: Shape = RoundedCornerShape(8.dp),
    contentPadding: PaddingValues =
        PaddingValues(
            start = TextFieldPadding,
            end = TextFieldPadding,
            top = TextFieldPadding,
            bottom = 32.dp to TextFieldPadding by showLengthTip,
        ),
    colors: TextFieldColors =
        TextFieldDefaults.colors(
            focusedContainerColor = backgroundColor.takeOrElse { WakooWhite },
            unfocusedContainerColor = backgroundColor.takeOrElse { WakooWhite },
            disabledContainerColor = backgroundColor.takeOrElse { WakooWhite },
            focusedIndicatorColor = Color.Transparent,
            unfocusedIndicatorColor = Color.Transparent,
        ),
) {
    Box(modifier = boxModifier) {
        // 输入框
        AppTextField(
            value = value,
            onValueChange = {
                if (it.length <= maxLength) {
                    onValueChange(it)
                }
            },
            modifier = modifier,
            enabled = enabled,
            readOnly = readOnly,
            textStyle = textStyle,
            placeholder = {
                Text(
                    text = placeholder,
                    style = placeholderStyle,
                    maxLines = if (singleLine) 1 else Int.MAX_VALUE,
                )
            },
            prefix = prefix,
            suffix = suffix,
            initialCursorPosition = initialCursorPosition,
            visualTransformation = visualTransformation,
            keyboardOptions = keyboardOptions,
            keyboardActions = keyboardActions,
            singleLine = singleLine,
            maxLines = maxLines,
            minLines = minLines,
            minWidth = minWidth,
            minHeight = minHeight,
            shape = shape,
            contentPadding = contentPadding,
            colors = colors,
        )

        // 字数统计
        if (showLengthTip) {
            Text(
                text = "${value.length}/$maxLength",
                style = supportingStyle,
                modifier =
                    Modifier
                        .align(Alignment.BottomEnd)
                        .padding(12.dp),
            )
        }
    }
}

val TextFieldPadding = 12.dp

private fun Modifier.defaultErrorSemantics(
    isError: Boolean,
    defaultErrorMessage: String,
): Modifier = if (isError) semantics { error(defaultErrorMessage) } else this

@Stable
private fun TextFieldColors.textColor(
    enabled: Boolean,
    isError: Boolean,
    focused: Boolean,
): Color =
    when {
        !enabled -> disabledTextColor
        isError -> errorTextColor
        focused -> focusedTextColor
        else -> unfocusedTextColor
    }

private fun TextFieldColors.cursorColor(isError: Boolean): Color = if (isError) errorCursorColor else cursorColor
