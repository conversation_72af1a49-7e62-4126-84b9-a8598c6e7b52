package com.buque.wakoo.navigation

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.R
import com.buque.wakoo.bean.user.isSelf
import com.buque.wakoo.ext.getIntOrNull
import com.buque.wakoo.im_business.conversation.AppConversationManger
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.api.service.NotificationApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.theme.MI_SANS
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

interface ITabItemContent {
    @Composable
    fun TabContent(
        selected: Boolean,
        modifier: Modifier = Modifier,
        color: Color = Color.Unspecified,
    )
}

sealed interface HomeNavTab : ITabItemContent {
    @Serializable
    data object Home : HomeNavTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Column(
                modifier = modifier,
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(2.dp),
            ) {
                Image(
                    painter = painterResource(if (!selected) R.drawable.ic_tab_home else R.drawable.ic_tab_home_st),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                )
                BasicText(
                    text = "首页".localized,
                    style =
                        MaterialTheme.typography.labelMedium.copy(
                            color = if (selected) Color(0xFF222222) else Color(0xFFB6B6B6),
                        ),
                    autoSize = TextAutoSize.StepBased(minFontSize = 6.sp, maxFontSize = 11.sp),
                )
            }
        }
    }

    @Serializable
    data object Task : HomeNavTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Column(
                modifier = modifier,
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(2.dp),
            ) {
                Image(
                    painter = painterResource(if (!selected) R.drawable.ic_task_inactive else R.drawable.ic_task_active),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                )
                BasicText(
                    text = "任务".localized,
                    style =
                        MaterialTheme.typography.labelMedium.copy(
                            color = if (selected) Color(0xFF222222) else Color(0xFFB6B6B6),
                        ),
                    autoSize = TextAutoSize.StepBased(minFontSize = 6.sp, maxFontSize = 11.sp),
                )
            }
        }
    }

    @Serializable
    data object Discover : HomeNavTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Column(
                modifier = modifier,
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(2.dp),
            ) {
                Image(
                    painter = painterResource(if (!selected) R.drawable.ic_tab_discover else R.drawable.ic_tab_discover_st),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                )
                BasicText(
                    text = "发现".localized,
                    style =
                        MaterialTheme.typography.labelMedium.copy(
                            color = if (selected) Color(0xFF222222) else Color(0xFFB6B6B6),
                        ),
                    autoSize = TextAutoSize.StepBased(minFontSize = 6.sp, maxFontSize = 11.sp),
                )
            }
        }
    }

    @Serializable
    data object Message : HomeNavTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Column(
                modifier = modifier,
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(2.dp),
            ) {
                var appMailUnRead by rememberSaveable {
                    mutableIntStateOf(0)
                }
                val imUnread by AppConversationManger.unReadCountFlow.collectAsStateWithLifecycle()

                val coroutineScope = rememberCoroutineScope()
                LifecycleEventEffect(Lifecycle.Event.ON_START) {
                    coroutineScope.launch {
                        executeApiCallExpectingData {
                            NotificationApiService.instance.getUnreadBadge()
                        }.getOrNull()?.getIntOrNull("notification")?.also {
                            appMailUnRead = it
                        }
                    }
                }

                val notificationCount by remember {
                    derivedStateOf {
                        appMailUnRead + imUnread
                    }
                }

                BadgedBox(badge = {
                    if (notificationCount > 0) {
                        Badge {
                            Text(
                                text = notificationCount.toString(),
                                modifier =
                                    Modifier.semantics {
                                        contentDescription = "$notificationCount new notifications"
                                    },
                                fontSize = 10.sp,
                            )
                        }
                    }
                }) {
                    Image(
                        painter = painterResource(if (!selected) R.drawable.ic_tab_message else R.drawable.ic_tab_message_st),
                        contentDescription = null,
                        modifier = Modifier.size(24.dp),
                    )
                }

                BasicText(
                    text = "消息".localized,
                    style =
                        MaterialTheme.typography.labelMedium.copy(
                            color = if (selected) Color(0xFF222222) else Color(0xFFB6B6B6),
                        ),
                    autoSize = TextAutoSize.StepBased(minFontSize = 6.sp, maxFontSize = 11.sp),
                )
            }
        }
    }

    @Serializable
    data object Mine : HomeNavTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Column(
                modifier = modifier,
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(2.dp),
            ) {
                Image(
                    painter = painterResource(if (!selected) R.drawable.ic_tab_mine else R.drawable.ic_tab_mine_st),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                )
                BasicText(
                    text = "我的".localized,
                    style =
                        MaterialTheme.typography.labelMedium.copy(
                            color = if (selected) Color(0xFF222222) else Color(0xFFB6B6B6),
                        ),
                    autoSize = TextAutoSize.StepBased(minFontSize = 6.sp, maxFontSize = 11.sp),
                )
            }
        }
    }
}

sealed interface VoiceListTab : ITabItemContent {
    sealed interface ISquare : VoiceListTab

    data object Recommend : ISquare {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "推荐".localized,
                color = color,
                modifier = modifier,
                fontFamily = FontFamily.MI_SANS,
                fontSize = 20.sp,
            )
        }
    }

    data object Follow : ISquare {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "关注".localized,
                modifier = modifier,
                color = color,
                fontFamily = FontFamily.MI_SANS,
                fontSize = 20.sp,
            )
        }
    }

    sealed interface IUser : VoiceListTab {
        val userId: String

        val emptyText: String
            @Composable
            get() = ""

        val emptyId: Int
            get() = R.drawable.ic_empty_for_all

        val emptyButton: String?
            @Composable
            get() = null

        @Composable
        fun TabContent(
            selected: Boolean,
            modifier: Modifier = Modifier,
            color: Color = Color.Unspecified,
            style: TextStyle,
        )
    }

    data class MyPublish(
        override val userId: String,
    ) : IUser {
        override val emptyText: String
            @Composable
            get() = if (userId.isSelf) "您还没有发布声音，快去发布吧~".localized else "TA还没有发布声音".localized

        override val emptyButton: String?
            @Composable
            get() = if (userId.isSelf) "发布声音内容".localized else null

        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
            style: TextStyle,
        ) {
            Text(
                text = if (userId.isSelf) "我的发布".localized else "TA的发布".localized,
                modifier = modifier,
                style = style,
                fontWeight = if (selected) FontWeight.SemiBold else FontWeight.Normal,
            )
        }

        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = if (userId.isSelf) "我的发布".localized else "TA的发布".localized,
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.SemiBold else FontWeight.Normal,
            )
        }
    }

    data class Like(
        override val userId: String,
    ) : IUser {
        override val emptyText: String
            @Composable
            get() = "暂无点赞内容~".localized

        override val emptyId: Int
            get() = R.drawable.ic_empty_for_like

        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "点赞".localized,
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.SemiBold else FontWeight.Normal,
            )
        }

        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
            style: TextStyle,
        ) {
            Text(
                text = "点赞".localized,
                modifier = modifier,
                style = style,
                fontWeight = if (selected) FontWeight.SemiBold else FontWeight.Normal,
            )
        }
    }

    data class Favorite(
        override val userId: String,
    ) : IUser {
        override val emptyText: String
            @Composable
            get() = "暂无收藏内容~".localized

        override val emptyId: Int
            get() = R.drawable.ic_empty_for_favorite

        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "收藏".localized,
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.SemiBold else FontWeight.Normal,
            )
        }

        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
            style: TextStyle,
        ) {
            Text(
                text = "收藏".localized,
                modifier = modifier,
                style = style,
                fontWeight = if (selected) FontWeight.SemiBold else FontWeight.Normal,
            )
        }
    }
}

sealed interface RelationsKey {
    sealed interface TabContent :
        RelationsKey,
        ITabItemContent

    data object RecentlyChat : TabContent {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "聊天".localized,
                modifier = modifier,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object Following : TabContent {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "关注".localized,
                modifier = modifier,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object Followers : TabContent {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "粉丝".localized,
                modifier = modifier,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object Friends : TabContent {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "好友".localized,
                modifier = modifier,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object BlackList : RelationsKey
}

sealed interface MessageTab : ITabItemContent {
    data object IMMessage : MessageTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "私信".localized,
                modifier = modifier,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object Notification : MessageTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "通知".localized,
                modifier = modifier,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }
}

sealed interface RoomRankTab : ITabItemContent {
    data object Contribution : RoomRankTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "贡献榜".localized,
                modifier = modifier,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object Charm : RoomRankTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "魅力榜".localized,
                modifier = modifier,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }
}

sealed interface RankTimely : ITabItemContent {
    data object Daily : RankTimely {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "日榜".localized,
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object Weekly : RankTimely {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "周榜".localized,
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }
}

sealed interface UserProfileTab : ITabItemContent {
    data object CPTab : UserProfileTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "我的CP".localized,
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object ProfileTab : UserProfileTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "个人资料".localized,
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object VoiceTab : UserProfileTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "声音作品".localized,
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object GiftWallTab : UserProfileTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "道具展馆".localized,
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object CronyTab : UserProfileTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "好友圈".localized,
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }
}

@Serializable
sealed interface SilverMallTab : ITabItemContent {
    data object Gift : SilverMallTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "限量礼物".localized,
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object Prop : SilverMallTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "装扮道具".localized,
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }
}
