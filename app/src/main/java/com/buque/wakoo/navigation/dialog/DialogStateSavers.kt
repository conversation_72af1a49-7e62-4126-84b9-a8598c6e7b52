package com.buque.wakoo.navigation.dialog

import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.listSaver
import androidx.compose.runtime.toMutableStateList
import androidx.savedstate.SavedState
import androidx.savedstate.serialization.SavedStateConfiguration
import androidx.savedstate.serialization.decodeFromSavedState
import androidx.savedstate.serialization.encodeToSavedState
import com.buque.wakoo.navigation.UnsafePolymorphicSerializer // 假设的序列化器
import kotlinx.serialization.Serializable

/**
 * 内部用于持久化可恢复Dialog状态的数据类。
 * 这是Dialog状态在被`SavedStateHandle`保存时的序列化形式。
 */
@Serializable
internal data class RestorableDialogState(
    val id: Int,
    val destination: DialogDestination,
)

/**
 * 创建用于保存和恢复Dialog列表的`Saver`。
 * 此函数封装了所有与状态持久化相关的复杂逻辑。
 */
internal fun createDialogListSaver(singleMode: Boolean): Saver<MutableList<ActiveDialog>, out Any> {
    val stateSaver: Saver<RestorableDialogState, SavedState> =
        Saver(
            save = { original ->
                encodeToSavedState(
                    UnsafePolymorphicSerializer(),
                    original,
                    SavedStateConfiguration.DEFAULT,
                )
            },
            restore = { savedState ->
                decodeFromSavedState(
                    UnsafePolymorphicSerializer(),
                    savedState,
                    SavedStateConfiguration.DEFAULT,
                )
            },
        )

    return listSaver(
        save = { dialogList ->
            stateSaver.run {
                dialogList.mapNotNull { activeDialog ->
                    if (activeDialog.destination.isRestorable && !activeDialog.dismissFlag) {
                        val state = RestorableDialogState(activeDialog.id, activeDialog.destination)
                        save(state)
                    } else {
                        null
                    }
                }
            }
        },
        restore = { savedList ->
            savedList
                .mapIndexedNotNull { index, savedState ->
                    val restoredState = stateSaver.restore(savedState)
                    if (restoredState != null) {
                        ActiveDialog(
                            id = restoredState.id,
                            destination = restoredState.destination,
                            resultDeferred = null, // 进程重建后无法恢复Deferred
                            showState = mutableStateOf(!singleMode || index == savedList.lastIndex),
                        )
                    } else {
                        null
                    }
                }.toMutableStateList()
        },
    )
}
