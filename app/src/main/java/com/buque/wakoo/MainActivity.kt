package com.buque.wakoo

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.activity.compose.setContent
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionLayout
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.navigation3.runtime.NavEntry
import com.buque.wakoo.app.Const
import com.buque.wakoo.core.webview.AppLinkNavigator
import com.buque.wakoo.ext.backToHome
import com.buque.wakoo.im.push.PushRouteHandler
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.AppManager
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.navigation.AppNavDisplay
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.LocalNavSharedTransitionScope
import com.buque.wakoo.navigation.LoggedInHost
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.appEntry
import com.buque.wakoo.navigation.appEntryProvider
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.easyPostBottomPanel
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.navigation.rememberRootNavController
import com.buque.wakoo.ui.dialog.loading.ProvideLoadingManager
import com.buque.wakoo.ui.floating.AppFloatingWidgets
import com.buque.wakoo.ui.screens.WebScreen
import com.buque.wakoo.ui.screens.login.LoginHostScreen
import com.buque.wakoo.ui.setEdgeToEdgeConfig
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.drag.LocalFloatingLayoutManagerState
import com.buque.wakoo.ui.widget.drag.rememberFloatingLayoutManagerState
import com.buque.wakoo.ui.widget.media.selector.MediaSelectorScreen
import com.buque.wakoo.utils.eventBus.AppEvent
import com.buque.wakoo.utils.eventBus.EventBusEffect
import com.buque.webview.WBH5FaceVerifySDK
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {
    init {
        AppManager.initialize(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        setTheme(R.style.Theme_Wakoo)
        super.onCreate(savedInstanceState)
        setEdgeToEdgeConfig()
        setContent {
            WakooTheme {
                val originalDensity = LocalDensity.current
                val fixedDensity =
                    Density(
                        density = originalDensity.density,
                        fontScale = 1.0f, // 强制字体缩放比例为1.0，即不缩放
                    )
                CompositionLocalProvider(LocalDensity provides fixedDensity) {
                    WakooAppScreen(AccountManager.isLoggedIn)
                }
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        if (intent.getBooleanExtra(Const.KVKey.FROM_MESSAGE_NOTIFICATION, false)) {
            lifecycleScope.launch {
                delay(200)
                PushRouteHandler.handle("")
            }
        }
    }

    @OptIn(ExperimentalSharedTransitionApi::class)
    @Composable
    private fun WakooAppScreen(isLoggedIn: Boolean) {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background,
        ) {
            val navController =
                rememberRootNavController(
                    loginNavKey = Route.Login,
                    isLoggedIn = isLoggedIn,
                    if (isLoggedIn) LoggedInHost else Route.Login,
                )

            val dialogController = rememberDialogController(render = false)

            LaunchedEffect(navController) {
                AccountManager.accountStateFlow
                    .map {
                        it != null
                    }.distinctUntilChanged()
                    .collectLatest {
                        if (it) {
                            navController.login()
                        } else {
                            navController.logout()
                        }
                    }
            }

            val account by AccountManager.accountStateFlow.collectAsStateWithLifecycle()

            if (account != null) {
                EventBusEffect<AppEvent.DeepLink>(navController, dialogController) {
                    if (it.uri.isNotBlank()) {
                        AppLinkNavigator.go(it.uri, navController, dialogController)
                    }
                }

                EventBusEffect<AppEvent.Route>(navController) {
                    navController.push(it.navKey)
                }

                EventBusEffect<AppEvent.Dialog>(dialogController) { event ->
                    if (event is AppEvent.RestorableDialog) {
                        dialogController.post(event.destination)
                    } else {
                        when (event) {
                            is AppEvent.CustomDialog -> {
                                dialogController.easyPost(
                                    dialogProperties = event.dialogProperties,
                                    content = event.content,
                                )
                            }

                            is AppEvent.PanelDialog -> {
                                dialogController.easyPostBottomPanel(dialogProperties = event.dialogProperties, content = event.content)
                            }

                            else -> {}
                        }

                    }
                }
            }

            CompositionLocalProvider(
                LocalFloatingLayoutManagerState provides rememberFloatingLayoutManagerState(collisionSpacing = 10.dp),
            ) {
                ProvideLoadingManager {
                    LocalAppNavController.ProvideController(navController) {
                        SharedTransitionLayout {
                            CompositionLocalProvider(LocalNavSharedTransitionScope provides this) {
                                AppNavDisplay(
                                    backStack = navController.backStack,
                                    entryProvider =
                                        appEntryProvider(fallback = { unknownScreen ->
                                            NavEntry(Route.Fallback("Unknown screen $unknownScreen")) {
                                                LaunchedEffect(Unit) {
                                                    if (EnvironmentManager.isProdRelease) {
                                                        navController.popIf(unknownScreen)
                                                    } else {
                                                        throw IllegalStateException("Unknown screen $unknownScreen")
                                                    }
                                                }
                                            }
                                        }) {
                                            appEntry<Route.Login> {
                                                LoginHostScreen()
                                            }
                                            appEntry<Route.Web> {
                                                WebScreen(it, onOpenPage = {
                                                    navController.push(it)
                                                }) {
                                                    navController.pop()
                                                }
                                            }
                                            appEntry<Route.MediaSelector> {
                                                MediaSelectorScreen(
                                                    it.resultKey,
                                                    it.mediaType,
                                                    it.maxSelectCount.coerceAtLeast(1),
                                                )
                                            }
                                            appEntry(LoggedInHost) {
                                                LoggedInHostScreen(navController)
                                            }
                                        },
                                )
                            }
                        }

                        dialogController.RenderDialogs(Unit)

                        AppFloatingWidgets()
                    }
                }
            }
        }
    }

    override fun onActivityResult(
        requestCode: Int,
        resultCode: Int,
        data: Intent?,
    ) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == WBH5FaceVerifySDK.VIDEO_REQUEST) { // 录制模式中，调用系统相机录制完视频后再回到当前app页面
            if (WBH5FaceVerifySDK
                    .getInstance()
                    .receiveH5FaceVerifyResult(requestCode, resultCode, data)
            ) {
                return
            }
        }
    }
}
